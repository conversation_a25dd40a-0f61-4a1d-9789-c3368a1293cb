import 'package:almashal/src/data/models/comment_user.dart';

class ReviewSummaryResponse {
  final ReviewSummaryData data;
  final String message;

  ReviewSummaryResponse({
    required this.data,
    required this.message,
  });

  factory ReviewSummaryResponse.fromJson(Map<String, dynamic> json) {
    return ReviewSummaryResponse(
      data: ReviewSummaryData.fromJson(json['data']),
      message: json['message'] ?? 'Success',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
      'message': message,
    };
  }
}

class ReviewSummaryData {
  final CommentUser user;
  final int reviewsCount;
  final double averageRating;
  final Map<String, int> ratingsBreakdown;

  ReviewSummaryData({
    required this.user,
    required this.reviewsCount,
    required this.averageRating,
    required this.ratingsBreakdown,
  });

  factory ReviewSummaryData.fromJson(Map<String, dynamic> json) {
    return ReviewSummaryData(
      user: CommentUser.from<PERSON><PERSON>(json['user']),
      reviewsCount: json['reviews_count'] ?? 0,
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      ratingsBreakdown: Map<String, int>.from(json['ratings_breakdown'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'reviews_count': reviewsCount,
      'average_rating': averageRating,
      'ratings_breakdown': ratingsBreakdown,
    };
  }
}
