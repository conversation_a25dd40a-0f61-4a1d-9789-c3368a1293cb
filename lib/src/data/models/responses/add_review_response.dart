import 'package:almashal/src/data/models/profile/review.dart';

class AddReviewResponse {
  final String message;
  final Review? data;

  AddReviewResponse({
    required this.message,
    this.data,
  });

  factory AddReviewResponse.fromJson(Map<String, dynamic> json) {
    return AddReviewResponse(
      message: json['message'] ?? 'تمت إضافة المراجعة بنجاح',
      data: json['data'] != null ? Review.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'data': data?.toJson(),
    };
  }
}
