import 'dart:io';

import 'package:almashal/src/data/models/album.dart';
import 'package:almashal/src/data/models/committee.dart';
import 'package:almashal/src/data/models/excellence_award.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/data/models/news.dart';
import 'package:almashal/src/data/models/occasion.dart';
import 'package:almashal/src/data/models/profile/cv.dart';
import 'package:almashal/src/data/models/profile/experience.dart';
import 'package:almashal/src/data/models/profile/skill.dart';
import 'package:almashal/src/data/models/responses/add_comment_response.dart';
import 'package:almashal/src/data/models/responses/comment_response.dart';
import 'package:almashal/src/data/models/responses/excellence_award_response.dart';
import 'package:almashal/src/data/models/responses/general_response.dart';
import 'package:almashal/src/data/models/responses/occasion_count_response.dart';
import 'package:almashal/src/data/models/responses/occasions_response.dart';
import 'package:almashal/src/data/models/responses/profile_response.dart';
import 'package:almashal/src/data/models/responses/slide_data_response.dart';
import 'package:almashal/src/data/models/responses/update_cv_response.dart';
import 'package:almashal/src/data/models/responses/update_profile_cover_response.dart';
import 'package:almashal/src/data/models/responses/update_profile_response.dart';
import 'package:almashal/src/data/models/social_media_link.dart';
import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/models/responses/reviews_list_response.dart';
import 'package:almashal/src/data/models/responses/add_review_response.dart';
import 'package:almashal/src/data/models/responses/review_summary_response.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart';

import '../../../core/values/app_config.dart';
import '../../models/branch.dart';
import '../../models/city.dart';
import '../../models/country.dart';
import '../../models/responses/about_family_response.dart';
import '../../models/responses/albums_response.dart';
import '../../models/responses/family_tree_response.dart';
import '../../models/responses/login_response.dart';
import '../../models/responses/news_response.dart';

part 'api_provider.g.dart';

@RestApi(baseUrl: AppConfig.apiUrl)
abstract class ApiProvider {
  factory ApiProvider(Dio dio, {String baseUrl}) = _ApiProvider;
  @GET('/family-information/tree-image')
  Future<FamilyTreeResponse> getFamilyTreeImage();
  @GET('/family-information/about')
  Future<AboutFamilyResponse> getAboutFamily();
  @GET('/albums')
  Future<AlbumsResponse> getAlbums({@Query('per_page') int perPage = 10, @Query('page') int page = 1});
  @GET('/albums/search')
  Future<AlbumsResponse> searchInAlbums({
    @Query('search') required String search,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  @GET('/album/{id}')
  Future<Album> getAlbumDetail(@Path('id') int id);
  // news
  @GET('/news')
  Future<NewsResponse> getNews({@Query('per_page') int perPage = 10, @Query('page') int page = 1});
  @GET('/news/search')
  Future<NewsResponse> searchInNews({
    @Query('search') required String search,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  @GET('/news/{id}')
  Future<News> getNewsDetail(@Path('id') int id);
  // occasions
  @GET('/occasions')
  Future<OccasionsResponse> getOccasions({
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
    @Query('type') int type = 0,
  });
  @GET('/occasions/search')
  Future<OccasionsResponse> searchInOccasions({
    @Query('search') required String search,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
    @Query('type') int type = 0,
  });
  @GET('/occasions/count')
  Future<OccasionCountResponse> getOccasionCount();

  @GET('/occasion/{id}')
  Future<Occasion> getOccasionDetail(
    @Path('id') int id,
  );
  // committees
  @GET('/committees')
  Future<List<Committee>> getCommittees();
  // login
  @POST('/login')
  Future<LoginResponse> login(
    @Body() Map<String, dynamic> body,
  );
  // logout
  @POST('/logout')
  Future<GeneralResponse> logout();
  // forget password
  @POST('/forget-password')
  Future<GeneralResponse> forgetPassword(
    @Body() Map<String, dynamic> body,
  );
  // reset password
  @POST('/reset-password')
  Future<GeneralResponse> resetPassword(
    @Body() Map<String, dynamic> body,
  );
  // register
  @POST('/register')
  Future<GeneralResponse> register(
    @Body() Map<String, dynamic> body,
  );
  @POST('/tree/nodes/request')
  Future<GeneralResponse> createTreeChangeRequest(
    @Body() Map<String, dynamic> body,
  );
  // register device
  @POST('/device/register')
  Future<GeneralResponse> registerDevice(
    @Body() Map<String, dynamic> body,
  );
  // get countries
  @GET('/countries')
  Future<List<Country>> getCountries();
  // get cities
  @GET('/cities/{id}')
  Future<List<City>> getCities(
    @Path('id') int countryId,
  );
  // get branches
  @GET('/branches')
  Future<List<Branch>> getBranches();
  // contact
  @POST('/contact')
  Future<GeneralResponse> contact(
    @Body() Map<String, dynamic> body,
  );
  // get comments
  @GET('/comments/{id}/{type}')
  Future<CommentResponse> getComments({
    @Path('id') required int id,
    @Path('type') required String type,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  // get comment replies
  @GET('/comments/{id}/replies')
  Future<CommentResponse> getCommentReplies({
    @Path('id') required int id,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  // comment
  @POST('/comment')
  Future<AddCommentResponse> comment(@Body() Map<String, dynamic> body);
  // like comment
  @POST('/comment/{id}/like')
  Future<GeneralResponse> likeComment(@Path('id') int commentId);

  // delete comment
  @POST('/comment/{id}/delete')
  Future<GeneralResponse> deleteComment(@Path('id') int commentId);

  // unlike comment
  @POST('/comment/{id}/unlike')
  Future<GeneralResponse> unlikeComment(@Path('id') int commentId);

  // get excellence awards
  @GET('/excellence-awards')
  Future<ExcellenceAwardResponse> getExcellenceAwards({
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  @GET('/excellence-awards/search')
  Future<ExcellenceAwardResponse> searchInExcellenceAwards({
    @Query('search') required String search,
    @Query('per_page') int perPage = 10,
    @Query('page') int page = 1,
  });
  @GET('/excellence-award/{id}')
  Future<ExcellenceAward> getExcellenceAwardDetail(@Path('id') int id);

  // get social media links
  @GET('/social-media-links')
  Future<List<SocialMediaLink>> getSocialMediaLinks();

  // profile endpoints
  @GET('/profile')
  Future<User> getProfile([@Query('id') String? userId]);

  @POST('/update-profile')
  @MultiPart()
  Future<UpdateProfileResponse> updateProfile(@Body() FormData body, @Part(name: 'image') File? image);

  // CV endpoints
  @GET('/profile/cv')
  Future<ProfileCVsResponse> getUserCVs();

  @GET('/profile/{userId}/cv')
  Future<ProfileCVsResponse> getUserCVsById(@Path('userId') String userId);

  @DELETE('/profile/cv/{cvId}')
  Future<GeneralResponse> deleteCV(@Path('cvId') String cvId);

  @PUT('/profile/cv/{cvId}/make-default')
  Future<CV> makeDefaultCV(@Path('cvId') String cvId);

  @PUT('/profile/cv/{cvId}/privacy')
  Future<CV> updateCVPrivacy(
    @Path('cvId') String cvId,
    @Body() Map<String, dynamic> privacy,
  );

  // Skills endpoints
  @GET('/profile/skills')
  Future<List<Skill>> getSkills(@Query('id') int? userId);

  @POST('/profile/skill/create')
  Future<GeneralResponse> addSkill(@Body() Map<String, dynamic> skill);

  @POST('/profile/skill/{skillId}/update')
  Future<GeneralResponse> updateSkill(
    @Path('skillId') String skillId,
    @Body() Map<String, dynamic> skill,
  );

  @DELETE('/profile/skill/{skillId}/delete')
  Future<GeneralResponse> deleteSkill(@Path('skillId') String skillId);

  // Experience endpoints
  @GET('/profile/experiences')
  Future<List<Experience>> getExperiences(@Query('id') int? userId);

  @POST('/profile/experience/create')
  Future<GeneralResponse> addExperience(@Body() Map<String, dynamic> data);

  @POST('/profile/experience/{id}/update')
  Future<GeneralResponse> updateExperience(@Path() int id, @Body() Map<String, dynamic> data);

  @DELETE('/profile/experience/{id}/delete')
  Future<GeneralResponse> deleteExperience(@Path() int id);

  // Achievement endpoints
  @GET('/profile/achievements')
  Future<ProfileAchievementsResponse> getUserAchievements();

  @GET('/profile/{userId}/achievements')
  Future<ProfileAchievementsResponse> getUserAchievementsById(@Path('userId') String userId);

  @POST('/profile/achievements')
  Future<ProfileResponse> addAchievement(@Body() dynamic data);

  @PUT('/profile/achievements/{id}')
  Future<ProfileResponse> updateAchievement(@Path() String id, @Body() dynamic data);

  @DELETE('/profile/achievements/{id}')
  Future<GeneralResponse> deleteAchievement(@Path() String id);

  // Privacy Settings endpoints
  @PUT('/profile/privacy')
  Future<GeneralResponse> updatePrivacySettings(@Body() Map<String, dynamic> settings);

  @GET('/tree/nodes')
  Future<List<FamilyTreeNode>> getFamilyTree();

  // File Upload Endpoints
  @POST('/upload')
  @MultiPart()
  Future<GeneralResponse> uploadFile(@Body() FormData formData);

  @DELETE('/files')
  Future<GeneralResponse> deleteFile(@Body() Map<String, dynamic> fileUrl);

  // check registration status
  @POST('/check-registration-status')
  Future<GeneralResponse> checkRegistrationStatus(
    @Body() Map<String, dynamic> body,
  );

  // Comments - renamed to avoid duplication
  @GET("/comments")
  Future<CommentResponse> getCommentsForItem({
    @Query("id") required int id,
    @Query("type") required String type,
    @Query("page") int? page,
  });

  // Reviews
  @GET("/users/{id}/reviews")
  Future<ReviewsListResponse> getReviews({
    @Path("id") required String userId,
    @Query("page") int? page,
    @Query("per_page") int? perPage,
  });

  @GET("/users/{id}/reviews/summary")
  Future<ReviewSummaryResponse> getReviewSummary({
    @Path("id") required String userId,
  });

  @POST("/reviews")
  Future<AddReviewResponse> addReview(@Body() Map<String, dynamic> body);

  @PUT("/reviews/{id}")
  Future<AddReviewResponse> updateReview(
    @Path("id") int reviewId,
    @Body() Map<String, dynamic> body,
  );

  @DELETE("/reviews/{id}")
  Future<GeneralResponse> deleteReview(@Path("id") int reviewId);

  // تغيير كلمة المرور
  @POST('/change-password')
  Future<GeneralResponse> changePassword(@Body() Map<String, dynamic> body);

  // تحديث صورة الملف الشخصي
  @POST('/update-profile-image')
  @MultiPart()
  Future<UpdateProfileResponse> updateProfileImage(@Body() FormData body);

  @POST('/update-profile-cover')
  @MultiPart()
  Future<UpdateProfileCoverResponse> updateProfileCover(@Body() FormData body);
  @POST('/update-cv')
  @MultiPart()
  Future<UpdateCvResponse> updateCV(@Body() FormData body);

  @GET('/slide-data')
  Future<SlideDataResponse> getSlideData();

  @POST("/cv/update")
  Future<HttpResponse> updateCv(@Body() FormData body);

  @POST("/record-visit")
  Future<HttpResponse> recordVisit(@Body() Map<String, dynamic> body);
}
