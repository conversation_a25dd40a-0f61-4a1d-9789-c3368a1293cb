<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AchievementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "title" => $this->title,
            "description" => $this->description,
            "date" => $this->date,
            "file" => $this->file,
            "file_url" => $this->file == null ? null : url('uploads/' . $this->file),
            "type" => $this->type->value,
            "type_label" => $this->type->getLabel(),
        ];
    }
}
