<?php

namespace App\Http\Resources\Api\Collections;

use App\Http\Resources\Api\MessageJsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class MessageCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            "total" => $this->total(),
            "current_page" => $this->currentPage(),
            "last_page" => $this->lastPage(),
            "per_page" => $this->perPage(),
            "data" => MessageJsonResource::collection($this->collection),
        ];
    }
}
