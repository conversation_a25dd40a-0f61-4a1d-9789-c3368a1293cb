<?php

namespace App\Http\Resources\Api\Collections;

use App\Http\Resources\Api\NewsJsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class NewsCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            "total" => $this->total(),
            "current_page" => $this->currentPage(),
            "last_page" => $this->lastPage(),
            "per_page" => $this->perPage(),
            "data" => NewsJsonResource::collection($this->collection),
        ];
    }
}
