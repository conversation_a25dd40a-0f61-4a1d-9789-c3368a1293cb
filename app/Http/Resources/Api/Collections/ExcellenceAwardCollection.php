<?php

namespace App\Http\Resources\Api\Collections;

use App\Http\Resources\Api\ExcellenceAwardJsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ExcellenceAwardCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            "total" => $this->total(),
            "current_page" => $this->currentPage(),
            "last_page" => $this->lastPage(),
            "per_page" => $this->perPage(),
            "data" => ExcellenceAwardJsonResource::collection($this->collection),
        ];
    }
}
