<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CommentJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "body" => $this->body,
            "replies" => CommentJsonResource::collection($this->children),
            "likes_count" => $this->likes_count,
            "liked" => $this->liked,
            "parent_id" => $this->parent_id,
            "commentable_id" => $this->commentable_id,
            "commentable_type" => $this->commentable_type,
            "user" => $this->user != null ? [
                "id" => $this->user->id,
                "display_name" => $this->user->display_name,
                'image' => $this->user->thumbImageUrl,
            ] : null,
            "created_at" => $this->created_at->diffForHumans([
                'parts' => 2,
                'join' => ' و'
            ]),
        ];
    }
}
