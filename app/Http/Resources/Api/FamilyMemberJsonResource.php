<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FamilyMemberJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "first_name" => $this->first_name,
            "middle_name" => $this->middle_name,
            "last_name" => $this->last_name,
            "grandfather_name" => $this->grandfather_name,
            "branch" => $this->branch?->name,
            "branch_id" => $this->branch_id,
            "gender" => $this->gender,
            "mobile" => $this->mobile,
            "email" => $this->email,
            "image" => $this->thumbImageUrl,
            'birth_date' => $this->birth_date?->format('Y-m-d'),
            'birth_place' => $this->birth_place,
            'country' => $this->country?->name,
            'country_id' => $this->country_id,
            'city' => $this->city?->name,
            'city_id' => $this->city_id,
            'address' => $this->address,
            'overview' => $this->overview,
            'cover_image' => $this->cover_image,
            'facebook_link' => $this->facebook_link,
            'x_link' => $this->x_link,
            'snapshot_link' => $this->snapshot_link,
            'youtube_link' => $this->youtube_link,
            'linkedin_link' => $this->linkedin_link,
            'instagram_link' => $this->instagram_link,
            'cv_file' => $this->cv_file,
            'cv_text' => $this->cv_text,
            'cv_type' => $this->cv_type,
            'family_tree_node_id' => $this->family_tree_node_id,
        ];
    }
}
