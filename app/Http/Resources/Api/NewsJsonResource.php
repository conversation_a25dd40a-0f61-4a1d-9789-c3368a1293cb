<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class NewsJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "title" => $this->title,
            "content" => $this->content,
            "image" => $this->image,
            "images" => $this->images,
            "youtube_videos" => get_videos_ids($this->youtube_videos),
            "published_at" => $this->published_at->translatedFormat('j F Y h:i A'),
            "created_at" => $this->created_at->format('Y-m-d H:i:s'),
            "share_link" => $this->share_link,
            "visits_count" => $this->visits_count,
        ];
    }
}
