<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FamilyMemberExperienceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "company_name" => $this->company_name,
            "job_title" => $this->job_title,
            "start_date" => $this->start_date,
            "end_date" => $this->end_date,
            "description" => $this->description,
            "location" => $this->location,
            "is_current_position" => $this->is_current_position == 1,
            "type" => $this->type->value,
            "type_label" => $this->type->getLabel(),
        ];
    }
}
