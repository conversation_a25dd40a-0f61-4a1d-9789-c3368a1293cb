<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FamilyTreeNodeJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            'name' => $this->name,
            'nick_name' => $this->nick_name,
            'overview' => $this->family_member != null ? $this->family_member->overview : $this->overview,
            'image' => $this->family_member != null ? $this->family_member->image_url : $this->imageUrl,
            'thumb_image_url' => $this->family_member != null ? $this->family_member->thumb_image_url : $this->thumb_image_url,
            'order' => $this->order,
            'gender' => $this->gender,
            'alive' => $this->alive,
            'birth_date' => $this->birth_date,
            'death_date' => $this->death_date,
            'birth_place' => $this->birth_place,
            'death_place' => $this->death_place,
            'job' => $this->job,
            'address' => $this->address,
            'family_member_id' => $this->family_member_id,
            // 'family_member' => $this->family_member,
            'children' => ChildFamilyTreeNodeJsonResource::collection($this->children),
            'parent' => new ChildFamilyTreeNodeJsonResource($this->parent),
        ];
    }
}
