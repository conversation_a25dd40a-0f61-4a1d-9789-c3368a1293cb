<?php

namespace App\Http\Livewire\Pages;

use App\Helpers\ArabicTextHelper;
use App\Models\GreetingCard;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Intervention\Image\Facades\Image;

class GreetingCardsPage extends Component
{
    public function render()
    {
        return view('livewire.pages.greeting-cards-page')
            ->layoutData(['title' => __('app.greeting_cards')]);
    }

    #[Computed()]
    public function cards()
    {
        return GreetingCard::all();
    }

    public function downloadCard($cardId, $name, $text)
    {
        // Find the selected card
        $card = GreetingCard::findOrFail($cardId);

        // Load the background image
        $image = Image::make(Storage::disk('uploads')->path($card->image));

        // Preprocess Arabic text for name and additional text
        $name = ArabicTextHelper::shapeArabicText($name);
        $text = ArabicTextHelper::shapeArabicText($text);

        // Calculate font sizes based on image width
        $nameFontSize = $card->name_font_size * ($image->width() / $card->image_width);
        $textFontSize = $card->text_font_size * ($image->width() / $card->image_width);

        // Add name to the image
        $image->text($name, $card->name_x * ($image->width() / 100), $card->name_y * ($image->height() / 100), function ($font) use ($nameFontSize, $card) {
            $font->file(public_path('fonts/tajawal/Tajawal-Medium.ttf')); // Path to font file
            $font->size($nameFontSize);
            $font->color($card->name_font_color);
            $font->align('center');
            $font->valign('middle');
        });

        // Handle multi-line text
        $lines = $this->splitTextIntoLines($text); // 80% of image width
        $lineHeight = $textFontSize * 1.5; // Adjust line height as needed

        // Add each line of text to the image
        foreach ($lines as $index => $line) {
            $yPosition = $card->text_y * ($image->height() / 100) + ($index * $lineHeight);
            $image->text($line, $card->text_x * ($image->width() / 100), $yPosition, function ($font) use ($textFontSize, $card) {
                $font->file(public_path('fonts/cocon-next-arabic.ttf')); // Path to font file
                $font->size($textFontSize);
                $font->color($card->text_font_color);
                $font->align('center');
                $font->valign('middle');
            });
        }

        // Save the generated image
        $fileName = 'greeting-card-' . time() . '.png';
        $imagePath = Storage::disk('public')->path($fileName);
        $image->save($imagePath);

        // Return the image as a download response
        return response()->download($imagePath)->deleteFileAfterSend(true);
    }

    /**
     * Split Arabic text into multiple lines based on a maximum width.
     */
    private function splitTextIntoLines($text)
    {
        $lines =  explode("\n", $text);


        return $lines;
    }

    /**
     * Calculate the width of a text string.
     */
    private function calculateTextWidth($text, $fontSize)
    {
        // Approximate width calculation (adjust as needed)
        return strlen($text) * ($fontSize * 0.5);
    }
}
