<?php

namespace App\Http\Livewire;

use App\Facades\Whatsapp;
use Livewire\Component;

class ConnectWhatsappPage extends Component
{
    public $phone;
    public $isConnected = false;
    public $qrcode = "";
    public function render()
    {
        $check = Whatsapp::checkSession();
        $this->phone = $check['phone'];
        $this->isConnected = $check['connected'];
        $this->qrcode = "";
        if (!$this->isConnected) {
            $getQr = Whatsapp::getQr();
            $this->qrcode = $getQr['qr'];
        }
        return view('livewire.connect-whatsapp-page');
    }
}
