<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\CommentCollection;
use App\Http\Resources\Api\CommentJsonResource;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CommentController extends Controller
{
    /**
     *  comment
     *
     *  Add comment
     *
     *
     **/
    public function store(Request $request)
    {

        $inputs = $request->all();
        $validator = Validator::make($request->all(), [
            // body comment Example: test body
            'body' => 'required',
            // id of news or occasion Example: 1
            'commentable_id' => 'required',
            // type of news or occasion Example: news
            'commentable_type' => 'required|in:news,occasion,award',
            // parent comment id Example: 1
            'parent_id' => 'nullable',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message' => __('comment.messages.validation_error'),
                'errors' => $validator->errors()
            ], 400);
        }
        $inputs['status'] = 1;
        $inputs['family_member_id'] = $request->user()->id;
        $comment =  Comment::create($inputs);
        return response()->json([
            "message" => "تم إضافة الرد بنجاح",
            "data" => new CommentJsonResource($comment)
        ]);
    }
    /**
     *  get comment base on type
     *
     *  get all comment
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index($id, $type)
    {
        $inputs = request()->all();
        return response()->json(
            new CommentCollection(
                Comment::where('status', 1)
                    ->whereHas('user')
                    ->where('commentable_type', $type)
                    ->where('commentable_id', $id)
                    ->whereNull('parent_id')
                    ->with('user')
                    ->orderBy('id', 'desc')
                    ->with(['user', 'children'])
                    ->withCount('likes')
                    ->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1),
            )
        );
    }

    public function destroy($id)
    {
        $comment = Comment::find($id);
        $comment->delete();
        return response()->json([
            "message" => "تم حذف الرد بنجاح",
        ]);
    }

    public function like($id)
    {
        $comment = Comment::find($id);
        $isLiked =  $comment->likes()->where('family_member_id', auth()->user()->id)->first();
        if (!$isLiked) {
            $comment->likes()->create([
                'family_member_id' => auth()->user()->id,
            ]);
        }
        return response()->json([
            "message" => "تم الاعجاب بنجاح",
        ]);
    }

    public function unlike($id)
    {
        $comment = Comment::find($id);
        $isLiked =  $comment->likes()->where('family_member_id', auth()->user()->id)->first();
        if ($isLiked) {
            $comment->likes()->where('family_member_id', auth()->user()->id)->delete();
        }
        return response()->json([
            "message" => "تم الغاء الاعجاب بنجاح",
        ]);
    }
}
