<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\MessageCollection;
use App\Models\Message;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;

class MessageController extends Controller
{
    /**
     * contact
     *
     *  contact
     *
     * @unauthenticated
     *
     * @response scenario=success
     * {
     *    "message": "تم إضافة الرسالة بنجاح",
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function create()
    {
        $inputs = request()->all();
        $validator =  Validator::make($inputs, [
            // name of sender Example: ايمن العريقي
            'name' => 'required',
            // email of sender Example: <EMAIL>
            'email' => 'required',
            // mobile of sender Example: 12365465
            'mobile' => 'required',
            // subject of message Example: موضوع جديد
            'subject' => 'required',
            // message Example: رسالة
            'message' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors()
            ], 422);
        }
        $inputs['seen'] = false;
        $data = Message::create($inputs);
        if ($data) {
            $recipients = User::where('is_active', true)->get();
            Notification::make()
                ->title('رسالة جديدة من '.$data->name)
                ->sendToDatabase($recipients);
            return response()->json([
                'message' => __('message.messages.created'),
            ], 200);
        }
        return response()->json([
            'message' => __('message.messages.failed_creation'),
        ], 500);
    }
}
