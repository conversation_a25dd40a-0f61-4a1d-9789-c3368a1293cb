<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Visit;
use Illuminate\Http\Request;

class VisitController extends Controller
{
    /**
     * Record a visit
     * @unauthenticated
     */
    public function recordVisit(Request $request)
    {
        $userId = $request->user()->id ?? null;
        $validated = $request->validate([
            'model_type' => 'required|string',
            'model_id' => 'required|integer',
        ]);

        Visit::create([
            'user_id' => $userId,
            'model_type' => $validated['model_type'],
            'model_id' => $validated['model_id'],
            'visited_at' => now(),
        ]);

        return response()->json(['message' => 'Visit recorded successfully.'], 201);
    }
}
