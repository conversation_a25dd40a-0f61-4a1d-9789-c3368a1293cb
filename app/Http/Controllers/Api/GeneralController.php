<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CityJsonResource;
use App\Http\Resources\Api\Collections\CommitteeCollection;
use App\Http\Resources\Api\CommitteeJsonResource;
use App\Http\Resources\Api\CountryJsonResource;
use App\Http\Resources\Api\FamilyBranchJsonResource;
use App\Models\City;
use App\Models\Committee;
use App\Models\Country;
use App\Models\FamilyBranch;

class GeneralController extends Controller
{
    /**
     * Family Branches
     *
     * Get all Family Branches
     *
     * @unauthenticated
     *
     * @response scenario=success
     * [
     *           {
     *              "id": 1,
     *              "name": "",
     *            }
     *      ]
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function getFamilyBranches()
    {
        $data = FamilyBranch::all();
        return response()->json(FamilyBranchJsonResource::collection($data));
    }
    /**
     * Countries
     *
     * Get all Countries
     *
     * @unauthenticated
     *
     * @response scenario=success
     * [
     *           {
     *              "id": 1,
     *              "name": "",
     *            }
     *      ]
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function getCountries()
    {
        $data = Country::orderBy('sort', 'desc')->get();
        return response()->json(CountryJsonResource::collection($data));
    }

    /**
     * Cities
     *
     * Get all Cities
     *
     * @unauthenticated
     *
     * @urlParam id int required The ID of the country. Example: 1

     *
     * @response scenario=success
     * [
     *           {
     *              "id": 1,
     *              "name": "",
     *            }
     *      ]
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function getCities($id)
    {
        $data = City::where('country_id', $id)->orderBy('sort', 'desc')->get();
        return response()->json(CityJsonResource::collection($data));
    }
}
