<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\CommentCollection;
use App\Http\Resources\Api\CommentJsonResource;
use App\Models\Comment;
use App\Models\FamilyTreeRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FamilyTreeRequestController extends Controller
{
    /**
     *
     * Add Family Tree Change Request
     *
     * @authenticated
     *
     * @response scenario=success
     * {
     *    "message": "تم إضافة الطلب بنجاح",
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function store(Request $request)
    {

        $inputs = $request->all();
        $validator = Validator::make($inputs, [
            // request type 0 delete 1 update 2 add Example: 0
            'request_type' => 'required',
            // family tree node id Example: 1
            'family_tree_node_id' => 'required',
            // type of news or occasion Example: news
            'note' => 'nullable',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message' => __('comment.messages.validation_error'),
                'errors' => $validator->errors()
            ], 400);
        }
        $inputs['family_member_id'] = $request->user()->id;
        FamilyTreeRequest::create($inputs);
        return response()->json([
            "message" => "تم إضافة الطلب بنجاح",
        ]);
    }
}
