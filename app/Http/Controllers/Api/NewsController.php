<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\NewsCollection;
use App\Http\Resources\Api\NewsJsonResource;
use App\Models\News;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\Console\Input\Input;

class NewsController extends Controller
{
    /**
     *
     * Get all News
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $data = News::withCount('visits')->published()->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new NewsCollection($data));
    }
    /**
     *
     * search in News
     *
     * @unauthenticated
     *
     * @queryParam search string search key default  Example: "key"
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function search()
    {
        $inputs = request()->all();
        $data = News::withCount('visits')->published()
            ->where('title', 'like', '%' . $inputs['search'] . '%')
            ->orWhere('content', 'like', '%' . $inputs['search'] . '%')
            ->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new NewsCollection($data));
    }

    /**
     *
     * create news
     *
     */

    public function store(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $inputs,
            [
                "title" => "required",
                "image" => "required|mimes:png,jpg,jpeg,webp,gif",
                "content" => "required",
                "images" => "nullable|array",
                "images.*" => "image|mimes:png,jpg,jpeg,webp,gif",
                "youtube_videos" => "nullable|array",
            ],
            [],
            [
                "title" => __("news.title.label"),
                "image" => __("news.image.label"),
                "content" => __("news.content.label"),
                "images" => __("news.images.label"),
                "youtube_videos" => __("news.youtube_videos.label"),
            ]
        );

        if ($validator->fails()) {
            return response()->json(
                [
                    "message" => __('common.messages.validation_error'),
                    "errors" => $validator->errors(),
                ],
                422
            );
        }
        if ($inputs['images'] != null) {
            foreach ($inputs['images'] as $image) {
            }
        }
        $data = News::create($inputs);
        return response()->json(
            [
                'message' => __('news.messages.created'),
                'data' => new NewsJsonResource($data),
            ],
            200
        );
    }

    public function comment(Request $request) {}
    /**
     *
     * Get News by id
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "created_at": "",
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/

    public function show($id)
    {
        $album = News::withCount('visits')->find($id);
        if (!$album) {
            return response()->json(['message' => __('common.messages.not_found')], 404);
        }
        return response()->json(new NewsJsonResource($album));
    }
}
