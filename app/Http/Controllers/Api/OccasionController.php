<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\OccasionCollection;
use App\Http\Resources\Api\OccasionJsonResource;
use App\Models\Occasion;
use Carbon\Carbon;

class OccasionController extends Controller
{
    /**
     *
     * Get all Occasions
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     * @queryParam type int occasion type 0 : past ,1 : future default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "date": "",
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $type = $inputs['type'] ?? 0;
        $query = Occasion::withCount('visits')->published();
        if ($type == 0) {
            $query = $query->whereDate('date', '<', Carbon::now());
        } else {
            $query = $query->whereDate('date', '>=', Carbon::now());
        }
        $data = $query->orderBy('date', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new OccasionCollection($data));
    }
    /**
     *
     * Search in Occasions
     *
     * @unauthenticated
     *
     * @queryParam search string search key default test Example: test
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     * @queryParam type int occasion type 0 : past ,1 : future default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "date": "",
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function search()
    {
        $inputs = request()->all();
        $type = $inputs['type'] ?? 0;
        $query = Occasion::withCount('visits')->published()
            ->where(function ($query) use ($inputs) {
                $query->where('title', 'like', '%' . $inputs['search'] . '%')
                    ->orWhere('content', 'like', '%' . $inputs['search'] . '%');
            });
        if ($type == 0) {
            $query = $query->whereDate('date', '<', Carbon::now());
        } else {
            $query = $query->whereDate('date', '>=', Carbon::now());
        }
        $data = $query->orderBy('date', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new OccasionCollection($data));
    }
    /**
     *
     * Get Occasion by id
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *
     *             "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "date": "",
     *              "created_at": "",
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/

    public function show($id)
    {
        $album = Occasion::withCount('visits')->find($id);
        if (!$album) {
            return response()->json(['message' => __('common.messages.not_found')], 404);
        }
        return response()->json(new OccasionJsonResource($album));
    }
    /**
     *
     * Get Occasions count
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *    "future_count" : 10,
     *     "past_count" : 20,
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function count()
    {
        $future_count = Occasion::published()->whereDate('date', '>=', Carbon::now())->count();
        $past_count = Occasion::published()->whereDate('date', '<', Carbon::now())->count();
        return response()->json([
            "future_count" => $future_count,
            "past_count" => $past_count,
        ]);
    }
}
