<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\CommitteeCollection;
use App\Http\Resources\Api\CommitteeJsonResource;
use App\Models\Committee;

class CommitteeController extends Controller
{
    /**
     * committees
     *
     * Get all Committees
     *
     * @unauthenticated
     *
     * @response scenario=success
     * [
     *           {
     *              "id": 1,
     *              "name": "",
     *              "content": "",
     *            }
     *      ]
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $data = Committee::orderBy('sort','asc')->get();
        return response()->json( CommitteeJsonResource::collection($data));
    }
}
