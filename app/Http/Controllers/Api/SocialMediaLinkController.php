<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\CommitteeCollection;
use App\Http\Resources\Api\CommitteeJsonResource;
use App\Http\Resources\Api\SocialMediaLinkJsonResource;
use App\Models\Committee;
use App\Models\SocialMediaLink;

class SocialMediaLinkController extends Controller
{
    /**
     * social media links
     *
     * Get all social media links
     *
     * @unauthenticated
     *
     * @response scenario=success
     * [
     *           {
     *              "id": 1,
     *              "name": "",
     *              "icon": "",
     *              "url": "",
     *            }
     *      ]
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $data = SocialMediaLink::orderBy('sort', 'asc')->get();
        return response()->json(SocialMediaLinkJsonResource::collection($data),);
    }
}
