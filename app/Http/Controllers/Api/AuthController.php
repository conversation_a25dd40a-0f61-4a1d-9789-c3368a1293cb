<?php

namespace App\Http\Controllers\Api;

use App\Facades\Fcm;
use App\Facades\Whatsapp;
use App\Filament\Resources\FamilyMemberResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\FamilyMemberJsonResource;
use App\Mail\ResetPassword;
use App\Models\FamilyInformation;
use App\Models\FamilyMember;
use App\Models\FamilyMemberDevice;
use App\Models\User;
use App\Rules\MinWords;
use Exception;
use Filament\Notifications\Notification;
use Hamcrest\Arrays\IsArray;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

/**
 * @group Auth
 *
 * APIs for employee authentication
 */
class AuthController extends Controller
{
    /**
     * Login
     * @unauthenticated
     */
    public function login(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                // employee email required unless mobile is provided. Example: <EMAIL>
                'email' => 'required_if:mobile,null|email',
                // employee mobile number required unless email is provided. Example: 777582069
                'mobile' => 'required_if:email,null|numeric',
                //  password. Example: 2500
                'password' => 'required',
                // device type. [1: android, 2: ios] Example: 1
                "device_type" => "required",
                // device id. Example: 123456789
                "device_id" => "required",
                // cloud messaging token. Example: 123456789
                "fcm_token" => "required",
            ],
            [],
            [
                "email" => __('family_member.fields.email.label'),
                "mobile" => __('family_member.fields.mobile.label'),
                "password" => __('family_member.fields.password.label'),
                "device_type" => __('family_member.fields.device_type.label'),
                "device_id" => __('family_member.fields.device_id.label'),
                "fcm_token" => __('family_member.fields.fcm_token.label'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $mail = $inputs['email'] ?? null;
        $mobile = $inputs['mobile'] ?? null;
        if (empty($mail) && empty($mobile)) {
            return response()->json(
                [
                    'message' => __('family_member.messages.provide_email_or_mobile'),
                ],
                403
            );
        }
        // check if family_member exists
        if (!empty($mobile)) {
            $data = FamilyMember::where(['mobile' => $inputs['mobile']])->first();
        } else {
            $data = FamilyMember::where(['email' => $inputs['email']])->first();
        }
        if ($data && Hash::check($inputs['password'], $data->password)) {
            if ($data->status == false) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.not_active'),
                    ],
                    403
                );
            }
            // create token
            $token = $data->createToken('authToken')->plainTextToken;
            // add device
            $device = FamilyMemberDevice::firstWhere('device_id', $inputs['device_id']);
            if ($device) {
                $device->update([
                    'device_id' => $inputs['device_id'],
                    'device_type' => $inputs['device_type'],
                    'fcm_token' => $inputs['fcm_token'],
                    'family_member_id' => $data->id,
                ]);
            } else {
                $device = FamilyMemberDevice::create([
                    'device_id' => $inputs['device_id'],
                    'device_type' => $inputs['device_type'],
                    'fcm_token' => $inputs['fcm_token'],
                    'family_member_id' => $data->id,
                ]);
            }
            return response()->json(
                [
                    'message' => __('family_member.messages.logged_in'),
                    'data' => new FamilyMemberJsonResource($data),
                    'token' => $token,
                ],
                200
            );
        } else {
            if ($mobile) {
                $message = __('family_member.messages.mobile_credentials_error');
            } else {
                $message = __('family_member.messages.email_credentials_error');
            }
            return response()->json(
                [
                    'message' => $message,
                ],
                403
            );
        }
    }


    /**
     *
     * Forget password
     *
     * Forget password
     * this is the second step in the login process
     * @unauthenticated
     */
    public function forgetPassword(Request $request)
    {
        $inputs = $request->all();
        // validate data
        $validator = Validator::make(
            $request->all(),
            [
                // employee mobile number. Example: <EMAIL>
                'email' => 'required_if:mobile,null|email',
                'mobile' => 'required_if:email,null|numeric',
            ],
            [],
            [
                "email" => __('family_member.fields.email.label'),
                "mobile" => __('family_member.fields.mobile.label'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        try {


            $mobile = $inputs['mobile'] ?? null;
            $email = $inputs['email'] ?? null;
            // check if family_member exists
            if ($mobile) {
                $data = FamilyMember::where(['mobile' => $inputs['mobile']])->first();
            } else {
                $data = FamilyMember::where(['email' => $inputs['email']])->first();
            }
            if ($data === null) {
                if ($mobile) {
                    $message = __("family_member.messages.mobile_not_found");
                } else {
                    $message = __("family_member.messages.email_not_found");
                }
                return response()->json([
                    'message' => $message,
                ], 404);
            }
            $reset_password_code = rand(100000, 999999);
            $data->update([
                'reset_password_code' => $reset_password_code,
            ]);
            if ($mobile) {
                $number = $data->whatsapp_number;
                $this->sendResetPasswordCode($number, $reset_password_code);
            } else {
                Mail::to($data->email)->send(new ResetPassword($reset_password_code));
            }
            if ($mobile) {
                $message = __('family_member.messages.forget_password_sent_mobile');
            } else {
                $message = __('family_member.messages.forget_password_sent_email');
            }
            return response()->json(
                [
                    'message' => $message,
                ],
                200
            );
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset password
     *
     * Reset password
     * this is the second step in the login process
     *
     *  @unauthenticated
     */
    public function resetPassword(Request $request)
    {
        $inputs = $request->all();
        // validate data
        $validator = Validator::make(
            $request->all(),
            [
                // employee email. Example: <EMAIL>
                'email' => 'required_if:mobile,null|email',
                // employee mobile number. Example: 777582069
                'mobile' => 'required_if:email,null|numeric',
                // reset password code number Example: 123456
                'reset_password_code' => 'required',
                // new password Example: 123456
                'new_password' => 'required|confirmed',
                // confirm password Example: 123456
                'new_password_confirmation' => 'required|same:new_password',
            ],
            [],
            [
                "email" => __('family_member.fields.email.label'),
                "mobile" => __('family_member.fields.mobile.label'),
                "reset_password_code" => __('family_member.fields.reset_password_code.label'),
                "new_password" => __('family_member.fields.new_password.label'),
                "new_password_confirmation" => __('family_member.fields.new_password_confirmation.label'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $mobile = $inputs['mobile'] ?? null;
        // check if family_member exists
        if ($mobile) {
            $data = FamilyMember::where(['mobile' => $inputs['mobile']])->first();
        } else {
            $data = FamilyMember::where(['email' => $inputs['email']])->first();
        }
        if ($data === null) {
            if ($mobile) {
                $message = __("family_member.messages.mobile_not_found");
            } else {
                $message = __("family_member.messages.email_not_found");
            }
            return response()->json([
                'message' => $message,
            ], 404);
        }
        if ($data->reset_password_code != $inputs['reset_password_code']) {
            return response()->json([
                'errors' => __("family_member.messages.reset_password_code_error"),
            ], 404);
        }
        $data->password = Hash::make($inputs['new_password']);
        $data->save();
        return response()->json(
            [
                'message' => __('family_member.messages.reset_password'),
            ],
            200
        );
    }

    /**
     * Logout
     *
     * logout family_member
     *
     * @authenticated
     * @response 200 scenario=success {
     * "message": "تم تسجيل الخروج بنجاح"
     * }
     * @response 401 scenario=unauthorized {
     * "message": "unauthorized",
     * }
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        return response()->json(
            [
                'message' => __('family_member.messages.logged_out'),
            ],
            200
        );
    }
    /**
     * Profile
     *
     * Profile
     *
     * @authenticated
     * @response 200 scenario=success {
     *
     * }
     * @response 401 scenario=unauthorized {
     * "message": "unauthorized",
     * }
     */


    /**
     * Register
     *
     * Register
     * this is the second step in the login process
     *
     *  @unauthenticated
     */
    public function register(Request $request)
    {
        $inputs = $request->all();
        // validate data
        $validator = Validator::make(
            $request->all(),
            [
                // full_name. Example: Ayman Ameen Shaef Naji
                "full_name" => ['required_if:first_name,null', new MinWords(3)],
                // first_name. Example: Ayman
                "first_name" => 'nullable',
                // middle_name. Example: Ameen
                "middle_name" => "nullable",
                // last name. Example: Shaef
                "last_name" => "nullable",
                // // grandfather_name. Example: Naji
                // "grandfather_name" => "required",
                // // branch_id. Example: 1
                // "branch_id" => "required",
                // // gender. Example: 1
                // "gender" => "required|in:1,2",
                // mobile. Example: 777582069
                "mobile" => 'nullable',
                // Country code. Example: +966
                "country_code" => 'nullable',
                // email. Example: <EMAIL>
                'email' => 'nullable|email|unique:family_members,email',
                //  password. Example: 2500
                'password' => 'required|confirmed',
                //  password_confirmation. Example: 2500
                'password_confirmation' => 'required',
                //  birth_date. Example: 1996-12-12
                // 'birth_date' => 'required',
                // //  birth_place. Example: Sanaa City - Yemen
                // 'birth_place' => 'required',
                // //  country_id. Example: 1
                // 'country_id' => 'nullable',
                // //  city_id. Example: 1
                // 'city_id' => 'nullable',
                // //  address. Example: Sanaa - Yemen
                // 'address' => 'nullable',
            ],
            [],
            [
                "full_name" => __('family_member.fields.full_name.label'),
                "first_name" => __('family_member.fields.first_name.label'),
                "middle_name" => __('family_member.fields.middle_name.label'),
                "last_name" => __('family_member.fields.last_name.label'),
                "grandfather_name" => __('family_member.fields.grandfather_name.label'),
                "mobile" => __('family_member.fields.mobile.label'),
                "country_code" => __('family_member.fields.country_code.label'),
                "gender" => __('family_member.fields.gender.label'),
                "branch_id" => __('family_member.fields.branch_id.label'),
                "email" => __('family_member.fields.email.label'),
                "password" => __('family_member.fields.password.label'),
                "birth_date" => __('family_member.fields.birth_date.label'),
                "birth_place" => __('family_member.fields.birth_place.label'),
                "country_id" => __('family_member.fields.country_id.label'),
                "city_id" => __('family_member.fields.city_id.label'),
                "address" => __('family_member.fields.address.label'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $mobile = $inputs['mobile'] ?? null;
        $user = FamilyMember::where('mobile', $mobile)->first();
        $inputs['password'] = Hash::make($inputs['password']);
        $inputs['country_id'] = 1;
        if ($inputs['full_name']) {
            $names = explode(' ', $inputs['full_name']);
            $nameCount = count($names);
            if ($nameCount >= 3) {
                $inputs['first_name'] = $names[0];
                $inputs['middle_name'] = $names[1];
                $inputs['last_name'] = implode(' ', array_slice($names, 2));
            } elseif ($nameCount === 2) {
                $inputs['first_name'] = $names[0];
                $inputs['middle_name'] = null;
                $inputs['last_name'] = $names[1];
            } elseif ($nameCount === 1) {
                $inputs['first_name'] = $names[0];
                $inputs['middle_name'] = null;
                $inputs['last_name'] = null;
            } else {
                $inputs['first_name'] = null;
                $inputs['middle_name'] = null;
                $inputs['last_name'] = null;
            }
        }
        if ($user) {
            if ($user->status == 1) {
                return response()->json([
                    'message' => __('family_member.messages.already_registered_with_active_account'),
                    'data' => new FamilyMemberJsonResource($user),
                ]);
            } else {
                $this->notifyAdminsToActivateAccount($user);
                return response()->json([
                    'message' => __('family_member.messages.already_registered_with_pending_account'),
                    'data' => new FamilyMemberJsonResource($user),
                ]);
            }
        } else {
            $data = FamilyMember::create($inputs);
            if ($data) {
                $recipients = User::where('is_active', true)->get();
                Notification::make()
                    ->title('تم انضمام فرد جديد من العائلة')
                    ->sendToDatabase($recipients);
                $this->sendWelcomeMessage($data);
                return response()->json([
                    'message' =>
                    __('family_member.messages.registered'),
                    'data' => new FamilyMemberJsonResource($data),
                ]);
            }
        }
    }

    public function registerDevice(Request $request)
    {
        $inputs = $request->all();
        $validator = validator($inputs, [
            'device_id' => 'required',
            'device_type' => 'required|in:1,2', // 1 android 2 ios
            'fcm_token' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    'message' => __('family_member.messages.validation_error'),
                    'errors' => $validator->errors()
                ],
                400
            );
        }

        $userId = $request->user_id;
        FamilyMemberDevice::updateOrCreate(
            [
                'device_id' => $inputs['device_id'],
                'device_type' => $inputs['device_type'],
            ],
            [
                'family_member_id' => $userId,
                'fcm_token' => $inputs['fcm_token'],
            ]
        );
        $topics = ['all'];
        if ($userId != null) {
            $topics[] = 'family_member';
        }
        $tokens = [$inputs['fcm_token']];
        Fcm::subscribeToTopics($topics, $tokens);
        return response()->json(['message' => __('family_member.messages.device_added')]);
    }
    public function changePassword(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'current_password' => 'required',
                'new_password' => 'required|confirmed',
            ],
            [],
            [
                'current_password' => __('family_member.fields.current_password.label'),
                'new_password' => __('family_member.fields.new_password.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        if (!Hash::check($inputs['current_password'], $request->user()->password)) {
            return response()->json([
                'message' => __('family_member.messages.current_password_incorrect'),
            ], 422);
        }
        $request->user()->update([
            'password' => Hash::make($inputs['new_password']),
        ]);
        return response()->json([
            'message' => __('family_member.messages.password_changed'),
        ]);
    }

    public function updateProfileImage(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'image' => 'required|image',
            ],
            [],
            [
                'image' => __('family_member.fields.image.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->update([
            'image' => $inputs['image'],
        ]);
        return response()->json([
            'message' => __('family_member.messages.image_updated'),
            'data' => new FamilyMemberJsonResource($request->user()),
        ]);
    }

    public function sendResetPasswordCode($number, $code)
    {
        $message = "كود استعادة كلمة المرور هو:\n *" . $code . "*\nبصراحة، حتى نحن ننسى كلمات المرور أحيانًا. لا تقلق، أنت لست وحدك! 😅";
        Whatsapp::send($number, $message);
    }

    public function sendWelcomeMessage($family_member)
    {
        Log::info($family_member->whatsapp_number);
        $message = "يا هلا بـ *" . $family_member->first_name . " " . $family_member->middle_name . " " . $family_member->last_name . "*! 👋 \nحسابك قيد التدقيق العائلي السريع عشان نتأكد إنك واحد مننا! 😉 جهز حالك للانضمام لجمعتنا الرقمية!";
        $adminMessage = "تم انضمام فرد جديد لتطبيق الاسرة:\n\nاسم الفرد: *" . $family_member->first_name . " " . $family_member->middle_name . " " . $family_member->last_name . "*\nرقم الجوال: *" . $family_member->mobile . "* \n\n يمكنك تفعيل الحساب من الرابط التالى: \n" . FamilyMemberResource::getUrl('edit', ['record' => $family_member->id]);
        try {
            Whatsapp::send($family_member->whatsapp_number, $message);
            Whatsapp::sendToAdmins($adminMessage);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
    /**
     * Check registration status
     * @unauthenticated
     */
    public function checkRegistrationStatus(Request $request)
    {
        $mobile = $request->mobile;
        $request->validate([
            'mobile' => 'required|numeric',
        ]);
        $family_member = FamilyMember::where('mobile', $mobile)->first();
        if (!$family_member) {
            return response()->json([
                'message' => __('family_member.messages.mobile_number_not_found'),
            ], 404);
        }
        $status = $family_member->status;
        if ($status == 1) {
            return response()->json([
                'message' => __('family_member.messages.account_active'),
            ]);
        } else if ($status == 2) {
            return response()->json([
                'message' => __('family_member.messages.account_blocked'),
            ], 403);
        }
        $this->notifyAdminsToActivateAccount($family_member);
        return response()->json([
            'message' => __('family_member.messages.account_pending'),
        ], 403);
    }

    public function notifyAdminsToActivateAccount($family_member)
    {
        $message = "يرجي التحقق من بيانات  $family_member->first_name $family_member->middle_name $family_member->last_name \nرقم الهاتف: $family_member->mobile \nمن الرابط " . FamilyMemberResource::getUrl('edit', ["record" => $family_member->id]);
        Whatsapp::sendToAdmins($message);
    }
}
