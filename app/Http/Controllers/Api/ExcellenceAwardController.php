<?php

namespace App\Http\Controllers\Api;

use App\Enums\ExcellenceAwardType;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\ExcellenceAwardCollection;
use App\Http\Resources\Api\Collections\OccasionCollection;
use App\Http\Resources\Api\ExcellenceAwardJsonResource;
use App\Models\ExcellenceAward;
use App\Models\Occasion;

class ExcellenceAwardController extends Controller
{
    /**
     *
     * Get all Excellence Award
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     * @queryParam type string filter by type (التفوق الدراسي or جوائز حفظ القرآن) Example: التفوق الدراسي
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $query = ExcellenceAward::withCount('visits')->published();

        // Filter by type if provided
        if (isset($inputs['type']) && !empty($inputs['type'])) {
            $query->where('type', $inputs['type']);
        }

        $data = $query->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new ExcellenceAwardCollection($data));
    }
    /**
     *
     * search in Excellence Award
     *
     * @unauthenticated
     *
     * @queryParam search string search key default test Example: test
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     * @queryParam type string filter by type (التفوق الدراسي or جوائز حفظ القرآن) Example: التفوق الدراسي
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function search()
    {
        $inputs = request()->all();
        $query = ExcellenceAward::withCount('visits')->published();

        // Apply search filters
        $query->where(function ($q) use ($inputs) {
            $q->where('title', 'like', '%' . $inputs['search'] . '%')
                ->orWhere('content', 'like', '%' . $inputs['search'] . '%');
        });

        // Filter by type if provided
        if (isset($inputs['type']) && !empty($inputs['type'])) {
            $query->where('type', $inputs['type']);
        }

        $data = $query->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new ExcellenceAwardCollection($data));
    }

    /**
     * excellence awards
     *
     * Get ExcellenceAward by id
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *
     *             "id": 1,
     *              "title": "",
     *              "content": "",
     *              "created_at": "",
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/

    public function show($id)
    {
        $album = ExcellenceAward::withCount('visits')->find($id);
        if (!$album) {
            return response()->json(['message' => __('common.messages.not_found')], 404);
        }
        return response()->json(new ExcellenceAwardJsonResource($album));
    }
}
