<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AlbumJsonResource;
use App\Http\Resources\Api\Collections\AlbumCollection;
use App\Models\Album;

class AlbumController extends Controller
{
    /**
     *
     * Get all Albums
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "image": "",
     *              "media": [],
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $data = Album::withCount('visits')->published()->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new AlbumCollection($data));
    }
    /**
     *
     * search in albums
     *
     * @unauthenticated
     *
     * @queryParam search string search key default  Example: "key"
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "image": "",
     *              "media": [],
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function search()
    {
        $inputs = request()->all();
        $search = $inputs['search'] ?? '';
        $data = Album::withCount('visits')->published()
            ->where('title', 'like', '%' . $search . '%')
            ->orderBy('published_at', 'desc')->fastPaginate($inputs['per_page'] ?? 15, "*", 'page', $inputs['page'] ?? 1);
        return response()->json(new AlbumCollection($data));
    }
    /**
     *
     * Get Album by id
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *
     *              "id": 1,
     *              "title": "",
     *              "image": "",
     *              "media": [],
     *              "created_at": ""
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/

    public function show($id)
    {
        $album = Album::withCount('visits')->find($id);
        if (!$album) {
            return response()->json(['message' => __('common.messages.not_found')], 404);
        }
        return response()->json(new AlbumJsonResource($album));
    }
}
