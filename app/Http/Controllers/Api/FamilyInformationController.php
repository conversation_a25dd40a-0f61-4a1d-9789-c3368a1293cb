<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\CommitteeCollection;
use App\Http\Resources\Api\CommitteeJsonResource;
use App\Models\Committee;
use App\Models\FamilyInformation;

class FamilyInformationController extends Controller
{
    /**
     * tree image
     *
     * Get tree image
     *
     * @unauthenticated
     *
     * @response scenario=success
     *           {
     *              "tree_image": "",
     *            }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function treeImage()
    {
        $data = FamilyInformation::where('id', 1)->select(['tree_image', 'share_tree_text'])->first();
        return response()->json([
            'tree_image' => $data->tree_image,
            'tree_share_text' => $data->share_tree_text,
        ]);
    }

    /**
     * About Family
     *
     * Get about Family
     *
     * @unauthenticated
     *
     * @response scenario=success
     *           {
     *              "about": "",
     *              "images": [],
     *              "youtube_videos": [],
     *            }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function about()
    {
        $data = FamilyInformation::where('id', 1)->select(['about', 'images', 'youtube_videos'])->first();
        return response()->json([
            'about' => $data->about,
            'images' => $data->images,
            'youtube_videos' => get_videos_ids($data->youtube_videos),
            'share_link' => $data->share_link,
        ]);
    }
    /**
     * Slide Data
     * @unauthenticated
     */
    public function slideData()
    {
        $data = FamilyInformation::where('id', 1)->select(['slide_text', 'slide_coffette_enabled'])->first();
        return response()->json([
            'slide_text' => $data->slide_text,
            'slide_coffette_enabled' => $data->slide_coffette_enabled == 1 ? true : false
        ]);
    }
}
