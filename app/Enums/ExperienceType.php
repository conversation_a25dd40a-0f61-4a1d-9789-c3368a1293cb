<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ExperienceType: string implements HasLabel
{
    case Job = 'job';
    case Training = 'training';
    case Volunteering = 'volunteering';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Job => __('Job'),
            self::Training => __('Training'),
            self::Volunteering => __('Volunteering'),
        };
    }
}
