<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum AchievementType: string implements HasLabel
{
    case Certificate = 'certificate';
    case Award = 'award';
    case PersonalAchievement = 'personal_achievement';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Certificate => __('Certificate'),
            self::Award => __('Award'),
            self::PersonalAchievement => __('Personal Achievement'),
        };
    }
}
