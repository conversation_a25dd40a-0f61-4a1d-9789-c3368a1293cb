<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class MinWords implements ValidationRule
{
    protected int $minWords;

    /**
     * Create a new rule instance.
     *
     * @param  int  $minWords
     * @return void
     */
    public function __construct(int $minWords)
    {
        $this->minWords = $minWords;
    }
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $array = explode(" ", $value);
        if (count($array) < $this->minWords) {
            $fail(__('validation.min_words', ['min_words' => $this->minWords]));
        }
    }
}
