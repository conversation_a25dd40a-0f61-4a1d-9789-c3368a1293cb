<?php

namespace App\Console\Commands;

use App\Models\AlbumMedia;
use Illuminate\Console\Command;

class MigrateMedia extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:media';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $this->info("Start Migrate Media");
        $albums = \App\Models\Album::all();
        foreach ($albums as $album) {
            foreach ($album->media as $media) {
                AlbumMedia::updateOrCreate([
                    'album_id' => $album->id,
                    'type' => $media['type'],
                    'image' => $media['type'] == 1 ? $media['image'] : null,
                    'video_id' => $media['type'] == 2 ? $media['youtube_video'] : null,
                ], [],);
            }

        }
    }
}
