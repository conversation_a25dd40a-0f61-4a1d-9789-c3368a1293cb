<?php

namespace App\Console\Commands;

use App\Models\FamilyMember;
use App\Models\Review;
use Illuminate\Console\Command;

class TestReviewsApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:reviews-api';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Reviews API functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Reviews API functionality...');
        $this->newLine();

        // Test 1: Create test users
        $this->info('1. Creating test users...');
        $user1 = FamilyMember::factory()->create([
            'first_name' => 'Ahmed',
            'middle_name' => 'Mohammed',
            'last_name' => 'Almashal',
            'email' => '<EMAIL>',
        ]);
        
        $user2 = FamilyMember::factory()->create([
            'first_name' => 'Fatima',
            'middle_name' => 'Ali',
            'last_name' => 'Almashal',
            'email' => '<EMAIL>',
        ]);

        $this->info("Created user 1: {$user1->display_name} (ID: {$user1->id})");
        $this->info("Created user 2: {$user2->display_name} (ID: {$user2->id})");
        $this->newLine();

        // Test 2: Create reviews
        $this->info('2. Creating test reviews...');
        
        $review1 = Review::create([
            'reviewer_id' => $user1->id,
            'reviewed_user_id' => $user2->id,
            'rating' => 5,
            'comment' => 'عضو رائع في العائلة!',
            'status' => true,
        ]);

        $review2 = Review::create([
            'reviewer_id' => $user2->id,
            'reviewed_user_id' => $user1->id,
            'rating' => 4,
            'comment' => 'شخص محترم ومتعاون.',
            'status' => true,
        ]);

        $this->info("Created review 1: {$user1->display_name} → {$user2->display_name} (Rating: 5)");
        $this->info("Created review 2: {$user2->display_name} → {$user1->display_name} (Rating: 4)");
        $this->newLine();

        // Test 3: Test model relationships
        $this->info('3. Testing model relationships...');
        
        $user1->refresh();
        $user2->refresh();
        
        $this->info("User 1 reviews given: " . $user1->reviewsGiven()->count());
        $this->info("User 1 reviews received: " . $user1->reviewsReceived()->count());
        $this->info("User 1 average rating: " . $user1->average_rating);
        $this->info("User 1 reviews count: " . $user1->reviews_count);
        
        $this->newLine();
        
        $this->info("User 2 reviews given: " . $user2->reviewsGiven()->count());
        $this->info("User 2 reviews received: " . $user2->reviewsReceived()->count());
        $this->info("User 2 average rating: " . $user2->average_rating);
        $this->info("User 2 reviews count: " . $user2->reviews_count);
        
        $this->newLine();

        // Test 4: Test review stats
        $this->info('4. Testing review statistics...');
        
        $stats1 = $user1->review_stats;
        $stats2 = $user2->review_stats;
        
        $this->info("User 1 stats:");
        $this->info("  - Count: " . $stats1['count']);
        $this->info("  - Average: " . $stats1['average']);
        $this->info("  - Breakdown: " . json_encode($stats1['ratings_breakdown']));
        
        $this->newLine();
        
        $this->info("User 2 stats:");
        $this->info("  - Count: " . $stats2['count']);
        $this->info("  - Average: " . $stats2['average']);
        $this->info("  - Breakdown: " . json_encode($stats2['ratings_breakdown']));
        
        $this->newLine();

        // Test 5: Test validation rules
        $this->info('5. Testing validation rules...');
        
        try {
            // Try to create a self-review (should fail)
            Review::create([
                'reviewer_id' => $user1->id,
                'reviewed_user_id' => $user1->id,
                'rating' => 5,
                'comment' => 'Self review',
                'status' => true,
            ]);
            $this->error("ERROR: Self-review was allowed (should be prevented)");
        } catch (\Exception $e) {
            $this->info("✓ Self-review prevention working: " . $e->getMessage());
        }

        try {
            // Try to create a duplicate review (should fail)
            Review::create([
                'reviewer_id' => $user1->id,
                'reviewed_user_id' => $user2->id,
                'rating' => 3,
                'comment' => 'Duplicate review',
                'status' => true,
            ]);
            $this->error("ERROR: Duplicate review was allowed (should be prevented)");
        } catch (\Exception $e) {
            $this->info("✓ Duplicate review prevention working: " . $e->getMessage());
        }

        $this->newLine();

        // Test 6: Test soft deletes
        $this->info('6. Testing soft deletes...');
        
        $initialCount = $user2->reviews_count;
        $review1->delete();
        $user2->refresh();
        $newCount = $user2->reviews_count;
        
        $this->info("Reviews count before delete: {$initialCount}");
        $this->info("Reviews count after delete: {$newCount}");
        
        if ($newCount < $initialCount) {
            $this->info("✓ Soft delete working correctly");
        } else {
            $this->error("ERROR: Soft delete not working");
        }

        $this->newLine();

        // Cleanup
        $this->info('7. Cleaning up test data...');
        Review::whereIn('reviewer_id', [$user1->id, $user2->id])->forceDelete();
        $user1->delete();
        $user2->delete();
        $this->info("✓ Test data cleaned up");

        $this->newLine();
        $this->info('✅ Reviews API test completed successfully!');
    }
}
