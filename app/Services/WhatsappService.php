<?php

namespace App\Services;

use App\Models\FamilyInformation;
use App\Models\FamilyMember;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsappService
{
    public $auth_key;
    public $app_key;
    public $api_url;
    public function __construct()
    {
        $this->auth_key = config('services.whatsapp.auth_key');
        $this->app_key = config('services.whatsapp.app_key');
        $this->api_url = config('services.whatsapp.api_url');
    }

    public function send($number, $message): bool
    {
        $url = $this->api_url . '/create-message';
        $data = [
            'authkey' => $this->auth_key,
            'appkey' => $this->app_key,
            'to' => $number,
            'message' => $message,
            'sandbox' => 'false'
        ];
        try {
            $response =  Http::timeout(30000)->retry(3, 0)->post($url, $data);
            return $response->successful() && $response->json('data.status_code') == 200;
        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return false;
        }
    }

    public function sendServer($number, $message): bool
    {
        $url = "https://waserver.smart-fingers.com/chats/send?id=device_1";
        $data = [
            "receiver" => $number,
            "delay" => 0,
            "message" =>
            [
                "text" => "$message"
            ]
        ];
        try {
            $response =  Http::retry(3, 500, throw: false, when: function ($exception, $request) {
                return $exception->getCode() != 200;
            })->post($url, $data);
            dd($response->headers());
            Log::info($response->status());
            Log::info($response->json());
            return $response->successful() && $response->json('data.status_code') == 200;
        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return false;
        }
    }

    public function checkSession()
    {
        $url = $this->api_url . '/check-session';
        $data = [
            'authkey' => $this->auth_key,
            'appkey' => $this->app_key,
        ];
        try {
            $response =  Http::timeout(30000)->retry(3, 0)->post($url, $data);
            return $response->json();
        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return false;
        }
    }

    public function getQr()
    {
        $url = $this->api_url . '/qr';
        $data = [
            'authkey' => $this->auth_key,
            'appkey' => $this->app_key,
        ];
        try {
            $response =  Http::timeout(30000)->retry(3, 0)->post($url, $data);
            return $response->json();
        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return false;
        }
    }

    public function sendToAdmins($message)
    {
        $adminNumbers = ["967777582069"];
        $adminNumbers[] = FamilyInformation::getSupportWhatsAppNumber();
        foreach ($adminNumbers as $number) {
            $this->send($number, $message);
        }
    }
}
