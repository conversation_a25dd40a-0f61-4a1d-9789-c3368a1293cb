<?php

namespace App\Services;

use App\Models\FamilyMember;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class FcmService
{
    public Messaging $messaging;
    public function __construct()
    {
        $this->messaging = app('firebase.messaging');
    }

    public function subscribeUserToTopics(array | String $topics, FamilyMember $user)
    {
        $tokens = $user->getFcmTokens();
        if (is_array($topics)) {
            $result = $this->messaging->subscribeToTopics($topics, $tokens);
        } else {
            $result = $this->messaging->subscribeToTopic($topics, $tokens);
        }
        return $result;
    }
    public function subscribeToTopics(array | String $topics, array $tokens)
    {
        if (is_array($topics)) {
            $result = $this->messaging->subscribeToTopics($topics, $tokens);
        } else {
            $result = $this->messaging->subscribeToTopic($topics, $tokens);
        }
        return $result;
    }

    public function unsubscribeFromTopics(array | String $topics, array $tokens)
    {
        if (is_array($topics)) {
            $result = $this->messaging->unsubscribeFromTopics($topics, $tokens);
        } else {
            $result = $this->messaging->unsubscribeFromTopic($topics, $tokens);
        }
        return $result;
    }

    public function sendToTopic(string $topic, ?string $title = null, ?string $body = null, ?string $imageUrl = null, array $data = [])
    {
        $message = CloudMessage::withTarget('topic', $topic)
            ->withNotification(Notification::create(title: $title, body: $body, imageUrl: $imageUrl))
            ->withData($data);
        $this->messaging->send($message);
    }
}
