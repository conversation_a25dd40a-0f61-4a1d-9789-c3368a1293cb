<?php

namespace App\Traits;

use function Filament\Support\get_model_label;

trait HasLabelTranslation
{
    public static function getModelLabel(): string
    {
        return __(str_replace(' ', '_', get_model_label(static::getModel())).'.singular_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __(str_replace(' ', '_', get_model_label(static::getModel())).'.plural_label');
    }
}
