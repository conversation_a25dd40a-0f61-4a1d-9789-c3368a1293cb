<?php
namespace App\Traits;

use Illuminate\Support\Carbon;

trait HasPublishedScope{

    public function scopePublished($query)
    {
        $now = Carbon::now();
        $date = Carbon::parse($now)->toDateString();
        $time = Carbon::parse($now)->toTimeString();
        return $query->where('status', 1)->whereDate('published_at', '<', $date)
            ->orWhere(function ($query) use ($date, $time) {
                $query->whereDate('published_at', '=', $date)
                    ->whereTime('published_at', '<=', $time);
            });
    }
}
