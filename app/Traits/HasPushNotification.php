<?php

namespace App\Traits;

use App\Facades\Fcm;

trait HasPushNotification
{
    public function sendNewPushNotification()
    {
        $title = "";
        $type = $this->notification_type;
        switch ($type) {
            case "news":
                $title = "تم اضافة خبر جديدة";
                break;
            case "occasion":
                $title = "تم اضافة مناسبة جديدة";
                break;
            case "excellence-award":
                $title = "تم اضافة جائزة تفوق جديدة";
                break;
            case "album":
                $title = "تم اضافة البوم جديد";
        }
        Fcm::sendToTopic(topic: 'all', title: $title, body: str($this->title)->limit(), data: ["screen" => $type, 'id' => $this->id,]);
        // $this->notified_at = now();
        // $this->save();
    }
}
