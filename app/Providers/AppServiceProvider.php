<?php

namespace App\Providers;

use App\Helpers\TranslatorWithFallback;
use App\Services\FcmService;
use App\Services\WhatsappService;
use Awcodes\Curator\Facades\Curator;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\SecurityRequirement;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Dedoc\Scramble\Support\Generator\Types\StringType;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Illuminate\Support\ServiceProvider;
use Filament\Facades\Filament;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Infolists\Components\Entry;
use Filament\Tables\Columns\Column;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Foundation\Vite;
use Filament\Navigation\NavigationGroup;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentColor;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Translation\FileLoader;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;
use SolutionForest\FilamentTree\Actions\Action;

use function Filament\Support\get_model_label;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (!(App::runningInConsole() && App::runningConsoleCommand('scribe:generate'))) {
            $this->registerTranslation();
        }
        FilamentColor::register([
            'primary' => Color::hex('#ab7b47'),
        ]);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Scramble::configure()
            ->withDocumentTransformers(function (OpenApi $openApi) {
                $openApi->components->securitySchemes['apiKey'] = SecurityScheme::apiKey('header', 'api-key');
                $openApi->components->securitySchemes['bearer'] = SecurityScheme::http('bearer');

                $openApi->security[] = new SecurityRequirement([
                    // 'apiKey' => "test",
                    'bearer' => [],
                ]);
            })->withOperationTransformers(function (Operation $operation) {
                $operation->parameters[] = (new Parameter('api-key', 'header'))
                    ->description('api key to access the api')
                    ->setSchema(Schema::fromType(new StringType))
                    ->example('NwaAi8q5SXQAu9P5X3bqSPGkakoI');
            });
        $this->app->bind('fcm', function () {
            return new FcmService();
        });
        $this->app->bind('whatsapp', function () {
            return new WhatsappService();
        });
        Relation::morphMap([
            'news' => 'App\Models\News',
            'occasion' => 'App\Models\Occasion',
            'award' => 'App\Models\ExcellenceAward',
            'album' => 'App\Models\Album',
        ]);
        Filament::serving(function () {

            Filament::registerNavigationGroups(
                collect(__('common.navigation_groups'))->map(function ($label, $key) {
                    return NavigationGroup::make()
                        ->collapsed(false)
                        ->label($label);
                })->toArray()
            );
            Action::configureUsing(function (Action $action) {
                $action->tooltip($action->getLabel());
                $action->iconButton();
            });
            DateTimePicker::configureUsing(
                function (DateTimePicker $dateTimePicker) {
                    $dateTimePicker->native(false)
                        ->displayFormat('l j F Y h:i A')
                        ->timezone('Asia/riyadh')
                        ->seconds(false)
                        ->firstDayOfWeek(6);
                },
                null,
                true
            );
            FileUpload::configureUsing(
                function (FileUpload $fileUpload) {
                    $fileUpload->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                        return (string) date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
                    });
                    $fileUpload->downloadable();
                    $fileUpload->openable();
                    $fileUpload->disk('uploads');
                },
                null,
                true
            );

            TinyEditor::configureUsing(
                function (TinyEditor $tinyEditor) {
                    $tinyEditor->setConvertUrls(false)
                        ->setRelativeUrls(false);
                },
                null,
                true
            );

            ImageColumn::configureUsing(
                function (ImageColumn $imageColumn) {
                    $imageColumn->getStateUsing(
                        function ($record, $column) {
                            $fileName = $record->{$column->getName()};

                            if ($fileName) {
                                if (is_array($fileName)) {
                                    return $fileName;
                                } else {
                                    // get file extension
                                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                    if ($ext == 'svg') {
                                        return url('uploads/' . $fileName);
                                    } else {
                                        return  url('images/thumb/' . $fileName);
                                    }
                                }
                            }
                            return null;
                        }
                    );
                },
                null,
                true
            );

            Field::configureUsing(function (Field $component) {
                $suffix = '';
                $component->label(
                    function ($record, $component, $livewire) use (&$suffix) {
                        $model = "";
                        $fieldOrRelation = "";

                        if (
                            $livewire instanceof RelationManager
                        ) {
                            $model = $livewire->getOwnerRecord()::class;
                            $fieldOrRelation = ".relations." . $livewire->getRelationshipName() . '.fields.';
                        } else {
                            if (method_exists(app($livewire->getModel()), 'getTranslatableAttributes')) {
                                if (in_array($component->getName(), (app($livewire->getModel()))->getTranslatableAttributes())) {
                                    $suffix = ' [' . __('common.options.languages.' . $livewire->activeFormLocale) . ']';
                                }
                            }
                            $model = $livewire->getModel();
                            $fieldOrRelation = ".fields.";
                        }

                        if (
                            $component->getName() == 'id'
                        ) {
                            return '#';
                        }
                        if (
                            $component->getName() == 'created_at'
                        ) {
                            return __('common.fields.created_at.label');
                        }
                        if (
                            $component->getName() == 'updated_at'
                        ) {
                            return __('common.fields.updated_at.label');
                        }

                        if (
                            $component->getName() == 'creater'
                        ) {
                            return __('common.fields.creater.label');
                        }
                        if (
                            $component->getName() == 'updater'
                        ) {
                            return __('common.fields.updater.label');
                        }

                        return __(str_replace(' ', '_', get_model_label($model)) . $fieldOrRelation . str_replace('.', '_', $component->getName()) . '.label') . $suffix;
                    }
                );
                if (!method_exists($component, 'getPlaceholder') || !method_exists($component, 'getPlaceholder')) {
                    return null;
                }
                $component->placeholder(function ($record, $component, $livewire) use (&$suffix) {
                    if (!method_exists($livewire, 'getModel')) {
                        return null;
                    }
                    $key = str_replace(' ', '_', get_model_label($livewire->getModel())) . '.fields.' . str_replace('.', '_', $component->getName()) . '.placeholder';
                    $tans = __($key);
                    if ($tans == $key) {
                        return null;
                    }
                    return $tans . $suffix;
                });
            });
            Entry::configureUsing(function (Entry $entry) {
                $suffix = '';
                $entry->label(
                    function ($record, $component) use (&$suffix) {
                        if (method_exists(app($record::class), 'getTranslatableAttributes')) {
                            if (in_array($component->getName(), (app($record::class))->getTranslatableAttributes())) {
                                $suffix = ' [' . __('common.options.languages.' . $component->livewire->activeFormLocale) . ']';
                            }
                        }
                        if (
                            $component->getName() == 'id'
                        ) {
                            return '#';
                        }
                        if (
                            $component->getName() == 'created_at'
                        ) {
                            return __('common.fields.created_at.label');
                        }
                        if (
                            $component->getName() == 'updated_at'
                        ) {
                            return __('common.fields.updated_at.label');
                        }

                        if (
                            $component->getName() == 'creater'
                        ) {
                            return __('common.fields.creater.label');
                        }
                        if (
                            $component->getName() == 'updater'
                        ) {
                            return __('common.fields.updater.label');
                        }

                        return __(str_replace(' ', '_', get_model_label($record::class)) . '.fields.' . str_replace('.', '_', $component->getName()) . '.label') . $suffix;
                    }
                );
            });

            Column::configureUsing(function (Column $column) {

                $column->label(
                    function ($record, $column, $livewire) {

                        if (
                            $column->getName() == 'id'
                        ) {
                            return '#';
                        }
                        if (
                            $column->getName() == 'created_at'
                        ) {
                            return __('common.fields.created_at.label');
                        }
                        if (
                            $column->getName() == 'updated_at'
                        ) {
                            return __('common.fields.updated_at.label');
                        }

                        if (
                            $column->getName() == 'creater'
                        ) {
                            return __('common.fields.creater.label');
                        }
                        if (
                            $column->getName() == 'updater'
                        ) {
                            return __('common.fields.updater.label');
                        }
                        $suffix = '';
                        $model = "";
                        $fieldOrRelation = "";
                        if (
                            $livewire instanceof RelationManager
                        ) {
                            $model = $livewire->getOwnerRecord()::class;
                            $fieldOrRelation = ".relations." . $livewire->getRelationshipName() . '.fields.';
                        } else {
                            if (method_exists(app($livewire->getModel()), 'getTranslatableAttributes')) {
                                if (in_array($column->getName(), (app($livewire->getModel()))->getTranslatableAttributes())) {
                                    $suffix = ' [' . __('common.options.languages.' . $livewire->activeLocale) . ']';
                                }
                            }

                            $model = $livewire->getModel();
                            $fieldOrRelation = '.fields.';
                        }



                        return __(str_replace(' ', '_', get_model_label($model)) . $fieldOrRelation . str_replace('.', '_', $column->getName()) . '.label') . $suffix;
                    }
                );
            });

            Placeholder::configureUsing(function (Placeholder $component) {
                $component->label(
                    function ($record, $component, $livewire) {
                        if (
                            $livewire instanceof RelationManager
                        ) {
                            return;
                        }
                        if (
                            $component->getName() == 'id'
                        ) {
                            return '#';
                        }
                        if (
                            $component->getName() == 'created_at'
                        ) {
                            return __('common.fields.created_at.label');
                        }
                        if (
                            $component->getName() == 'updated_at'
                        ) {
                            return __('common.fields.updated_at.label');
                        }

                        if (
                            $component->getName() == 'creater'
                        ) {
                            return __('common.fields.creater.label');
                        }
                        if (
                            $component->getName() == 'updater'
                        ) {
                            return __('common.fields.updater.label');
                        }
                        $suffix = '';
                        if (method_exists(app($livewire->getModel()), 'getTranslatableAttributes')) {
                            if (in_array($component->getName(), (app($livewire->getModel()))->getTranslatableAttributes())) {
                                $suffix = ' [' . __('common.options.languages.' . $livewire->activeFormLocale) . ']';
                            }
                        }
                        return __(str_replace(' ', '_', get_model_label($livewire->getModel())) . '.fields.' . str_replace('.', '_', $component->getName()) . '.label') . $suffix;
                    }
                );
            });
        });
    }
    private function registerTranslation()
    {
        $this->app->extend('translator', function ($translator, $app) {

            $trans = new TranslatorWithFallback(new FileLoader($app['files'], [__DIR__ . '/lang', $app['path.lang']]), $app['config']['app.locale']);

            $trans->setFallback($app['config']['app.fallback_locale']);

            return $trans;
        });
    }
}
