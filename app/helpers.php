<?php

use App\Models\Configuration;
use App\Models\Department;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\EmployeeActivityLog;
use App\Models\EmployeeDevice;
use App\Models\EmployeeStatus;
use App\Models\MaritialStatus;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Ladumor\OneSignal\OneSignal;

if (!function_exists('get_video_id')) {
    function get_video_id($video)
    {
        if ($video) {
            return $video['youtube_video'];
        }
        return null;
    }
}
if (!function_exists('get_videos_ids')) {
    function get_videos_ids($videos)
    {
        $ids = [];
        if ($videos != null) {
            foreach ($videos as $video) {
                if (get_video_id($video) != null) {
                    $ids[] = get_video_id($video);
                }
            }
        }
        return $ids;
    }
}

// media
if (!function_exists('get_medias')) {
    function get_medias($medias)
    {
        if ($medias) {
            $media = [];
            foreach ($medias as $m) {
                $temp = "";
                switch (intval($m['type'])) {
                    case 1:
                        $temp = $m['image'];
                        break;
                    case 2:
                        $temp = $m['video_id'];
                        break;
                    case 3:
                        $temp = $m['pdf_file'];
                        break;

                    default:

                        break;
                }
                $media[] = [
                    "type" => intval($m['type']),
                    'media' => $temp,
                    'images' => $m['images'] ?? [],
                    'title' => $m['title'],
                    'detail' => $m['detail'],
                ];
            }
            return $media;
        }
        return null;
    }
}
