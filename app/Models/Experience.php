<?php

namespace App\Models;

use App\Enums\ExperienceType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Experience extends Model
{
    use HasFactory;
    // fillable
    protected $fillable = [
        'job_title',
        'description',
        'company_name',
        'location',
        'start_date',
        'end_date',
        'type',
        'family_member_id',
    ];

    // cast
    protected $casts = [
        'type' => ExperienceType::class,
    ];

    public function family_member()
    {
        return $this->belongsTo(FamilyMember::class);
    }
}
