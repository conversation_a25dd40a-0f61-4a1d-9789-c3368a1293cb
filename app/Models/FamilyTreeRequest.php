<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FamilyTreeRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'request_type',
        'family_member_id',
        'family_tree_node_id',
        'note',
    ];

    public function family_member()
    {
        return $this->belongsTo(FamilyMember::class);
    }

    public function family_tree_node()
    {
        return $this->belongsTo(FamilyTreeNode::class);
    }
}
