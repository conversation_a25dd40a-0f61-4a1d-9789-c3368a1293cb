<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountDeletionRequest extends Model
{
    use HasFactory;

    // fillable
    protected $fillable = [
        'email',
        'reason'
    ];

    // account
    public function account()
    {
        return $this->belongsTo(FamilyMember::class, 'email', 'email');
    }
}
