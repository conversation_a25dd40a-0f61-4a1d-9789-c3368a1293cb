<?php

namespace App\Models;

use App\Traits\HasShareLink;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FamilyInformation extends Model
{
    use HasFactory;
    // filable
    protected $fillable = [
        'id',
        'tree_image',
        'about',
        'youtube_videos',
        'images',
        'share_tree_text',
        'slide_text',
        'slide_coffette_enabled',
        'support_whatsapp_number',
    ];
    // cast
    protected $casts = [
        'youtube_videos' => 'array',
        'images' => 'array',
    ];

    public function getShareLinkAttribute()
    {
        return "https://almashalfamily.com/share/about";
    }

    static public function getSupportWhatsAppNumber()
    {
        $number = FamilyInformation::where('id', 1)
            ->select(['support_whatsapp_number'])
            ->first()
            ->support_whatsapp_number;
        $number = str_replace('+', '', $number);
        return $number;
    }
}
