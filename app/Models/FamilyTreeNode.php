<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use SolutionForest\FilamentTree\Concern\ModelTree;

class FamilyTreeNode extends Model
{
    use HasFactory;
    use SoftDeletes;
    use ModelTree;
    protected $fillable = [
        'name',
        'nick_name',
        'parent_id',
        'order',
        'gender',
        'alive',
        'image',
        'overview',
        'birth_date',
        'death_date',
        'birth_place',
        'death_place',
        'job',
        'address',
        'family_member_id',
    ];

    public function children()
    {
        return $this->hasMany(FamilyTreeNode::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(FamilyTreeNode::class, 'parent_id');
    }

    public function family_member()
    {
        return $this->belongsTo(FamilyMember::class, 'family_member_id');
    }

    // creating event
    protected static function booted()
    {
        static::creating(function (FamilyTreeNode $familyTreeNode) {
            if (!$familyTreeNode->parent_id) {
                $familyTreeNode->parent_id = -1;
            }
        });
    }
    public function determineTitleColumnName(): string
    {
        return 'name';
    }

    public function thumbImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('/images/thumb/' . $this->image) : url('/assets/images/male-avatar.webp')
        );
    }
    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('/uploads/' . $this->image) : url('/assets/images/male-avatar.webp')
        );
    }
}
