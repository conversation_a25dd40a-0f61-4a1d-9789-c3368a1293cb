<?php

namespace App\Models;

use App\Traits\HasPublishedScope;
use App\Traits\HasPushNotification;
use App\Traits\HasShareLink;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExcellenceAward extends Model
{
    use SoftDeletes;
    use HasFactory;
    use HasPublishedScope;
    use HasPushNotification;
    use HasShareLink;

    protected $fillable = [
        'title',
        'content',
        'published_at',
        'status',

    ];
    public string $notification_type = "excellence-award";

    protected $with = ['visits'];

    protected $casts = [
        'published_at' => 'datetime'
    ];

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    public function visits()
    {
        return $this->morphMany(Visit::class, 'model');
    }
}
