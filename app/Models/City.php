<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class City extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $fillable = [
        'name',
        'country_id',
        'sort'
    ];

    // country relationship
    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
