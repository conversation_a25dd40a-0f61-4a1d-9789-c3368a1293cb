<?php

namespace App\Models;

use App\Traits\HasPublishedScope;
use App\Traits\HasPushNotification;
use App\Traits\HasShareLink;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Album extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasPublishedScope;
    use HasPushNotification;
    use HasShareLink;

    // fillable
    protected $fillable = [
        'title',
        'image',
        'view_type',
        'published_at',
        'status',
    ];
    public string $notification_type = "album";

    protected $with = ['visits'];

    // cast
    protected $casts = [
        'published_at' => 'datetime'
    ];

    public function media()
    {
        return $this->hasMany(AlbumMedia::class, 'album_id')->orderBy('sort', 'asc');
    }

    public function visits()
    {
        return $this->morphMany(Visit::class, 'model');
    }
}
