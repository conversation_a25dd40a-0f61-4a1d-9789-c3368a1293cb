<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AlbumMedia extends Model
{
    use HasFactory;
    // fillable
    protected $fillable = [
        'album_id',
        'type',
        'image',
        'images',
        'video_id',
        'pdf_file',
        'title',
        'detail',
        'sort',
    ];

    // casts
    protected $casts = [
        'images' => 'array'
    ];

    public function album()
    {
        return $this->belongsTo(Album::class);
    }
}
