<?php

namespace App\Models;

use App\Traits\HasPublishedScope;
use App\Traits\HasPushNotification;
use App\Traits\HasShareLink;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class News extends Model
{
    use SoftDeletes;
    use HasFactory;
    use HasPublishedScope;
    use HasPushNotification;
    use HasShareLink;

    // table
    protected $table = 'news';

    public string $notification_type = "news";

    protected $fillable = [
        'title',
        'image',
        'content',
        'images',
        'youtube_videos',
        'published_at',
        'status',
    ];

    // cast
    protected $casts = [
        'images' => 'array',
        'youtube_videos' => 'array',
        'published_at' => 'datetime'
    ];

    protected $with = ['visits'];

    /**
     * Get all of the news's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get all of the news's visits.
     */
    public function visits()
    {
        return $this->morphMany(Visit::class, 'model');
    }
}
