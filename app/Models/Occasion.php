<?php

namespace App\Models;

use App\Traits\HasPublishedScope;
use App\Traits\HasPushNotification;
use App\Traits\HasShareLink;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Occasion extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasPublishedScope;
    use HasPushNotification;
    use HasShareLink;

    // filable
    protected $fillable = [
        'title',
        'image',
        'content',
        'images',
        'youtube_videos',
        'date',
        'published_at',
        'status',
    ];

    // cast
    protected $casts = [
        'images' => 'array',
        'youtube_videos' => 'array',
        'date' => 'datetime',
        'published_at' => 'datetime'
    ];

    protected $with = ['visits'];

    public string $notification_type = "occasion";

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    public function visits()
    {
        return $this->morphMany(Visit::class, 'model');
    }
}
