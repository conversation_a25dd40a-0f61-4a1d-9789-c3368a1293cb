<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class GreetingCard extends Model
{
    use HasFactory;

    protected $fillable = [
        "title",
        "image",
        "image_width",
        "image_height",
        "text_font_size",
        "text_font_color",
        "text_x",
        "text_y",
        "text_coordinate",
        "name_font_size",
        "name_font_color",
        "name_x",
        "name_y",
        "name_coordinate",
    ];

    protected function textCoordinate(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => [
                "x" => $attributes["text_x"],
                "y" => $attributes["text_y"],
            ],
            set: fn(mixed $value, array $attributes) => [
                "text_x" => $value["x"],
                "text_y" => $value["y"],
            ],
        );
    }

    protected function nameCoordinate(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => [
                "x" => $attributes["name_x"],
                "y" => $attributes["name_y"],
            ],
            set: fn(mixed $value, array $attributes) => [
                "name_x" => $value["x"],
                "name_y" => $value["y"],
            ],
        );
    }

    protected function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => Storage::disk("uploads")->url($attributes["image"]),
        );
    }

    protected $appends = ['text_coordinate', 'image_url', 'name_coordinate'];
}
