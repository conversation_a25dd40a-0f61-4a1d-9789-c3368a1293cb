<?php

namespace App\Models;

use App\Enums\AchievementType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Achievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'file',
        'type',
        'date',
        'family_member_id',
    ];

    public function familyMember()
    {
        return $this->belongsTo(FamilyMember::class);
    }

    // casting
    protected $casts = [
        'type' => AchievementType::class,
    ];
}
