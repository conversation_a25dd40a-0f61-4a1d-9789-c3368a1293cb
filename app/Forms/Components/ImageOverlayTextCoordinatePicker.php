<?php

namespace App\Forms\Components;

use Closure;
use Filament\Forms\Components\Field;

class ImageOverlayTextCoordinatePicker extends Field
{
    protected string $view = 'forms.components.image-overlay-text-coordinate-picker';

    public string| Closure| null $imageUrl = null;

    public ?string $overlayText = null;

    public string| Closure| null $textColor = null;

    public string| Closure| null $textSize = null;

    public float| Closure| null $imageWidth = null;

    public float| Closure| null $imageHeight = null;

    public function imageUrl(string| Closure $imageUrl): static
    {
        $this->imageUrl = $imageUrl;

        return $this;
    }

    public function getImageUrl(): ?string
    {
        if ($this->imageUrl instanceof Closure) {
            return $this->evaluate($this->imageUrl);
        }
        return $this->imageUrl;
    }

    public function overlayText(string| Closure $overlayText): static
    {
        $this->overlayText = $overlayText;

        return $this;
    }

    public function getOverlayText(): ?string
    {
        return $this->overlayText;
    }

    public function textColor(string| Closure $textColor): static
    {
        $this->textColor = $textColor;

        return $this;
    }

    public function getTextColor(): ?string
    {
        if ($this->textColor instanceof Closure) {
            return $this->evaluate($this->textColor);
        }
        return $this->textColor;
    }

    public function textSize(string| Closure $textSize): static
    {
        $this->textSize = $textSize;

        return $this;
    }

    public function getTextSize(): ?string
    {
        if ($this->textSize instanceof Closure) {
            return $this->evaluate($this->textSize);
        }
        return $this->textSize;
    }

    public function imageWidth(float| Closure $imageWidth): static
    {
        $this->imageWidth = $imageWidth;

        return $this;
    }

    public function getImageWidth(): ?float
    {
        if ($this->imageWidth instanceof Closure) {
            return $this->evaluate($this->imageWidth);
        }
        return $this->imageWidth;
    }

    public function imageHeight(float| Closure $imageHeight): static
    {
        $this->imageHeight = $imageHeight;

        return $this;
    }

    public function getImageHeight(): ?float
    {
        if ($this->imageHeight instanceof Closure) {
            return $this->evaluate($this->imageHeight);
        }
        return $this->imageHeight;
    }
}
