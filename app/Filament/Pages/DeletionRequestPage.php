<?php

namespace App\Filament\Pages;

use App\Models\AccountDeletionRequest;
use App\Models\FamilyMember;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Hash;

class DeletionRequestPage extends BaseRegister
{
    protected static string $view = 'filament.pages.deletion-request-page';


    public function getTitle(): string | Htmlable
    {
        return __('account_deletion_request.page_title');
    }
    public function getHeading(): string | Htmlable
    {
        return __('account_deletion_request.page_title');
    }

    public function mount(): void
    {
        $this->form->fill();
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('email')
                    ->label(__('account_deletion_request.fields.email.label'))
                    ->placeholder(__('account_deletion_request.fields.email.placeholder'))
                    ->email()
                    ->exists(FamilyMember::class, 'email')
                    ->required(),
                TextInput::make('password')
                    ->label(__('account_deletion_request.fields.password.label'))
                    ->placeholder(__('account_deletion_request.fields.password.placeholder'))
                    ->password()
                    ->required(),
                Textarea::make('reason')
                    ->label(__('account_deletion_request.fields.reason.label'))
                    ->placeholder(__('account_deletion_request.fields.reason.placeholder'))
                    ->rows(3)
                    ->required(),
            ]);
    }
    public function request()
    {

        $data = $this->form->getState();
        $account = FamilyMember::where('email', $data['email'])->first();
        if ($account) {
            if (Hash::check($data['password'], $account->password)) {
                $request =  AccountDeletionRequest::where('email', $data['email'])->first();
                if ($request) {
                    Notification::make()
                        ->title(__('account_deletion_request.messages.request_already_sent'))
                        ->danger()
                        ->send();
                } else {
                    AccountDeletionRequest::create([
                        'email' => $data['email'],
                        'reason' => $data['reason'],
                    ]);

                    Notification::make()
                        ->title(__('account_deletion_request.messages.request_sent'))
                        ->success()
                        ->send();
                }
            } else {
                Notification::make()
                    ->title(__('account_deletion_request.messages.invalid_credentials'))
                    ->danger()
                    ->send();
            }
        } else {
            Notification::make()
                ->title(__('account_deletion_request.messages.invalid_credentials'))
                ->danger()
                ->send();
        }
    }
    public function getRegisterFormAction(): Action
    {
        return Action::make('request')
            ->label(__('account_deletion_request.actions.request.label'))
            ->submit('request');
    }
}
