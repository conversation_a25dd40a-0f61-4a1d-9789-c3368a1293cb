<?php

namespace App\Filament\Pages;

use App\Models\FamilyInformation;
use Filament\Pages\Page;
use App\Traits\HasLabelTranslation;
use Filament\Facades\Filament;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class FamilyInformationPage extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-information-circle';

    public FamilyInformation $information;

    public $data;

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.family");
    }

    public static function getNavigationLabel(): string
    {
        return __('family_information.singular_label');
    }
    public function getTitle(): string
    {
        return __('family_information.singular_label');
    }

    protected static string $view = 'filament.pages.family-information-page';

    public function mount(): void
    {
        $this->information = FamilyInformation::firstOrCreate(
            ['id' => 1],
            [
                'id' => 1,
                'tree_image' => '',
                'about' => '',
                'youtube_videos' => [],
                'images' => [],
            ]
        );
        $this->form->fill($this->information->toArray());
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make(__('family_information.singular_label'))
                ->schema([
                    FileUpload::make('tree_image')
                        ->label(__('family_information.fields.tree_image.label'))
                        ->placeholder(__('family_information.fields.tree_image.placeholder'))
                        ->directory('family-information/images')
                        ->previewable()
                        ->required(),
                    TextInput::make('share_tree_text')
                        ->label(__('family_information.fields.share_tree_text.label'))
                        ->placeholder(__('family_information.fields.share_tree_text.placeholder'))
                        ->url()
                        ->required(),
                    TinyEditor::make('about')
                        ->label(__('family_information.fields.about.label'))
                        ->placeholder(__('family_information.fields.about.placeholder'))
                        ->fileAttachmentsDirectory('family-information/about')
                        ->required(),
                    FileUpload::make('images')
                        ->label(__('family_information.fields.images.label'))
                        ->placeholder(__('family_information.fields.images.placeholder'))
                        ->previewable()
                        ->downloadable()
                        ->multiple()
                        ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                        ->directory('family-information/images'),
                    Repeater::make('youtube_videos')
                        ->label(__('family_information.fields.youtube_videos.label'))
                        ->schema(
                            [
                                TextInput::make('youtube_video')
                                    ->label(__('family_information.fields.youtube_video.label'))
                                    ->placeholder(__('family_information.fields.youtube_video.placeholder'))
                                    ->live()
                                    ->afterStateUpdated(function (\Filament\Forms\Set $set, $state) {
                                        $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                                        if (preg_match($re, $state, $matches)) {
                                            $set('youtube_video', $matches[1]);
                                        } else {
                                            $set('youtube_video', null);
                                        }
                                    })
                                    ->maxLength(255),
                                Placeholder::make('youtube_video_preview')
                                    ->label(__('family_information.fields.youtube_video_preview.label'))
                                    ->content(function (\Filament\Forms\Get $get) {
                                        return new HtmlString("<iframe class='w-full aspect-video rounded-lg'  src=\"https://www.youtube.com/embed/{$get('youtube_video')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                                    })
                                    ->hidden(
                                        function (\Filament\Forms\Get $get) {
                                            return $get('youtube_video') == null;
                                        },
                                    ),
                            ]
                        ),
                ]),
            Section::make()
                ->heading(__('family_information.fields.slide.label'))
                ->schema([
                    Textarea::make('slide_text')
                        ->label(__('family_information.fields.slide_text.label'))
                        ->placeholder(__('family_information.fields.slide_text.placeholder')),
                    Toggle::make('slide_coffette_enabled')
                        ->label(__('family_information.fields.slide_coffette_enabled.label')),
                ]),
            Section::make()
                ->heading(__('family_information.fields.contact_information.label'))
                ->schema([
                    TextInput::make('support_whatsapp_number')
                        ->label(__('family_information.fields.support_whatsapp_number.label'))
                        ->placeholder(__('family_information.fields.support_whatsapp_number.placeholder')),
                ])
        ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();
        $this->information->update($data);
        Notification::make()
            ->success()
            ->title(__('family_information.messages.updated'))
            ->send();
    }
}
