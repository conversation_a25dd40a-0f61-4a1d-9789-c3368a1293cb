<?php

namespace App\Filament\Exports;

use App\Models\FamilyTreeNode;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class FamilyTreeNodeExporter extends Exporter
{
    protected static ?string $model = FamilyTreeNode::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('الرقم التعريفي'),
            ExportColumn::make('name')
                ->label('الاسم'),
            ExportColumn::make('nick_name')
                ->label('الكنية'),
            ExportColumn::make('image')
                ->label('الصورة')
                ->formatStateUsing(function ($state) {
                    if ($state) {
                        return url('/uploads/' . $state);
                    }
                }),
            ExportColumn::make('overview')
                ->label('النبذة'),
            ExportColumn::make('parent_id')
                ->label('الرقم التعريفي للاب'),
            ExportColumn::make('order')
                ->label('الترتيب بين اخوانه'),
            ExportColumn::make('gender')
                ->label('الجنس'),
            ExportColumn::make('alive')
                ->label('على قيد الحياة؟'),
            ExportColumn::make('birth_date')
                ->label('تاريخ الميلاد'),
            ExportColumn::make('death_date')
                ->label('تاريخ الوفاة'),
            ExportColumn::make('birth_place')
                ->label('مكان الميلاد'),
            ExportColumn::make('death_place')
                ->label('مكان الوفاة'),
            ExportColumn::make('job')
                ->label('العمل'),
            ExportColumn::make('address')
                ->label('محل الاقامة'),
            ExportColumn::make('created_at')
                ->label('تاريخ الانشاء'),
            ExportColumn::make('updated_at')
                ->label('تاريخ آخر تعديل'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'تم تصدير البيانات المحدده من شجرة العائلة عدد الصفوف المصدره بنجاح ' . number_format($export->successful_rows);

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= 'وعدد الصفوف الفاشلة ' . number_format($failedRowsCount);
        }

        return $body;
    }
}
