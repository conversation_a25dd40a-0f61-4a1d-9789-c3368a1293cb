<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;

class DeactivateAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'deactivate';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.deactivate'));
        $this->icon('heroicon-s-x-circle');
        $this->color('danger');
        $this->hidden(fn($record) => $record->status == false);
        $this->requiresConfirmation(true);
        $this->action(function ($record) {
            $record->status = false;
            $record->save();
        });
    }
}
