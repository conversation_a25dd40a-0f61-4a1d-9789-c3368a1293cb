<?php

namespace App\Filament\Actions\Table;

use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;

class ActivateBulkAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'bulk_activate';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.activate'));
        $this->icon('heroicon-s-check-circle');
        $this->color('success');
        // $this->hidden(fn($record) => $record->status == true);
        $this->requiresConfirmation(true);
        $this->action(function ($records) {
            foreach ($records as $record) {
                $record->status = true;
                $record->save();
            }
        });
    }
}
