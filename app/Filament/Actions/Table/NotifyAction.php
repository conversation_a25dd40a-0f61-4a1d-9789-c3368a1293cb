<?php

namespace App\Filament\Actions\Table;

use Filament\Tables\Actions\Action;

class NotifyAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'notify_user';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.send_notification'));
        $this->icon('heroicon-s-bell');
        $this->color('success');
        $this->hidden(fn ( $record) => $record->notified_at !== null);
        $this->requiresConfirmation(true);
        $this->action(fn ($record) => $record->sendNewPushNotification());
    }
}
