<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;

class ActivateAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'activate';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.activate'));
        $this->icon('heroicon-s-check-circle');
        $this->color('success');
        $this->hidden(fn($record) => $record->status == true);
        $this->requiresConfirmation(true);
        $this->action(function ($record) {
            $record->status = true;
            $record->save();
        });
    }
}
