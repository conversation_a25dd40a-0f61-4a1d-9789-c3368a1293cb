<?php

namespace App\Filament\Imports;

use App\Models\FamilyTreeNode;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class FamilyTreeNodeImporter extends Importer
{
    protected static ?string $model = FamilyTreeNode::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('id')
                ->label('الرقم التعريفي')
                ->numeric()
                ->requiredMapping(),
            ImportColumn::make('name')
                ->label('الاسم')
                ->requiredMapping()
                ->rules(['required']),
            ImportColumn::make('nick_name')
                ->label('اللقب'),
            ImportColumn::make('overview')
                ->label('نبذه'),
            ImportColumn::make('parent_id')
                ->label('الرقم التعريقي للاب')
                ->numeric()
                ->rules(['nullable', 'integer']),
            ImportColumn::make('order')
                ->label('الترتيب')
                ->helperText('ترتيبة بين اخوانه حسب العمر')
                ->numeric()
                ->rules(['required', 'integer']),
            ImportColumn::make('gender')
                ->label('الجنس')
                ->helperText('1 = ذكر, 2 = انثى')
                ->fillRecordUsing(function ($record, $state) {
                    $record->gender = $record->gender ?? $state ?? 1;
                })
                ->numeric()
                ->rules(['integer']),
            ImportColumn::make('alive')
                ->label('هل الفرد مازال علي قيد الحياة؟')
                // ->requiredMapping()
                ->boolean()
                ->rules(['required', 'boolean']),
            ImportColumn::make('birth_date')
                ->label('تاريخ الميلاد')
                ->rules(['nullable', 'date']),
            ImportColumn::make('death_date')
                ->label('تاريخ الوفاة')
                ->rules(['nullable', 'date']),
            ImportColumn::make('birth_place')
                ->label('مكان الميلاد'),
            ImportColumn::make('death_place')
                ->label('مكان الوفاة'),
            ImportColumn::make('job')
                ->label('الوظيفة'),
            ImportColumn::make('address')
                ->label('عنوان السكن الحالي'),
        ];
    }

    public function resolveRecord(): ?FamilyTreeNode
    {
        return FamilyTreeNode::firstOrNew([
            'id' => $this->data['id'],
        ]);

        // return new FamilyTreeNode();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = ' تم اكتمال عملية استيراد بيانات شجرة العائلة بنجاح وتم استيراد ' . number_format($import->successful_rows) . "بنجاح";

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= 'وفشل استيراد ' . number_format($failedRowsCount);
        }

        return $body;
    }
}
