<?php

namespace App\Filament\Resources\OccasionResource\Pages;

use App\Filament\Actions\NotifyAction;
use App\Filament\Resources\OccasionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewOccasion extends ViewRecord
{

    protected static string $resource = OccasionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            NotifyAction::make(),
            Actions\EditAction::make(),
        ];
    }
}
