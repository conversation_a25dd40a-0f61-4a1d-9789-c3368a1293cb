<?php

namespace App\Filament\Resources\AlbumResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class MediaRelationManager extends RelationManager
{
    protected static string $relationship = 'media';

    public static function getTitle($ownerRecord, string $pageClass): string
    {
        return __('album.relations.media.label');
    }
    public function form(Form $form): Form
    {
        return $form
            ->columns(1)

            ->schema([
                Forms\Components\Select::make('type')
                    ->options(__('album.options.media_type'))
                    ->live()
                    ->required(),
                Forms\Components\TextInput::make('title')
                    ->visible(fn ($get) => $get('type') != null),
                TinyEditor::make('detail')
                    ->fileAttachmentsDirectory('album/media')
                    ->visible(fn ($get) => $get('type') != null),
                Forms\Components\FileUpload::make('image')
                    ->previewable()
                    ->downloadable()
                    ->required()
                    ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                    ->visible(fn ($get) => $get('type') == 1)
                    ->directory('album/images'),
                Forms\Components\FileUpload::make('images')
                    ->previewable()
                    ->downloadable()
                    ->multiple()
                    ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                    ->visible(fn ($get) => $get('type') == 1)
                    ->directory('album/images'),
                Forms\Components\TextInput::make('video_id')
                    ->live()
                    ->visible(fn ($get) => $get('type') == 2)
                    ->afterStateUpdated(function (\Filament\Forms\Set $set, $state) {
                        $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                        if (preg_match($re, $state, $matches)) {
                            $set('video_id', $matches[1]);
                        } else {
                            $set('video_id', null);
                        }
                    })
                    ->maxLength(255),
                Forms\Components\Placeholder::make('video_id_preview')
                    ->visible(fn ($get) => $get('type') == 2)
                    ->content(function (\Filament\Forms\Get $get) {
                        return new HtmlString("<iframe class='w-full aspect-video rounded-lg'  src=\"https://www.youtube.com/embed/{$get('video_id')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                    })
                    ->hidden(function (\Filament\Forms\Get $get) {
                        return $get('video_id') == null;
                    }),
                Forms\Components\FileUpload::make('pdf_file')
                    ->previewable()
                    ->downloadable()
                    ->required()
                    ->acceptedFileTypes(['application/pdf'])
                    ->visible(fn ($get) => $get('type') == 3)
                    ->directory('album/pdf_files'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // ->recordTitleAttribute('title')
            ->reorderable('sort')
            ->defaultSort('sort', 'asc')
            ->modelLabel(fn () => __('album.relations.media.label'))
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('title')
                    ->getStateUsing(fn ($record) =>  !$record->title ? '-' : $record->title),
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(fn ($state) => __('album.options.media_type')[$state] ?? ''),
                Tables\Columns\TextColumn::make('sort'),
                Tables\Columns\TextColumn::make('created_at'),
                Tables\Columns\TextColumn::make('updated_at'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
