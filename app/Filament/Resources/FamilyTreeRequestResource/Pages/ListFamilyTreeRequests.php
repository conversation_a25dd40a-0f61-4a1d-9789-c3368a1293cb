<?php

namespace App\Filament\Resources\FamilyTreeRequestResource\Pages;

use App\Filament\Resources\FamilyTreeRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFamilyTreeRequests extends ListRecords
{
    protected static string $resource = FamilyTreeRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
