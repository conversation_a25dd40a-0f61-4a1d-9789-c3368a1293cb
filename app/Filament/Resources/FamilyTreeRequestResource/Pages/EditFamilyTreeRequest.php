<?php

namespace App\Filament\Resources\FamilyTreeRequestResource\Pages;

use App\Filament\Resources\FamilyTreeRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFamilyTreeRequest extends EditRecord
{
    protected static string $resource = FamilyTreeRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
