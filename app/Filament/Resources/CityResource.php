<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CityResource\Pages;
use App\Filament\Resources\CityResource\RelationManagers;
use App\Models\City;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasLabelTranslation;
use Filament\Actions\ActionGroup;
use Illuminate\Support\HtmlString;

class CityResource extends Resource
{

    use HasLabelTranslation;

    protected static ?string $model = City::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?int $navigationSort = 1;

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.configuration");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\Select::make('country_id')
                        ->options(Country::pluck('name', 'id'))
                        ->required(),
                    Forms\Components\TextInput::make('sort')
                        ->default(\App\Models\City::max('sort') + 1)
                        ->numeric()
                        ->required(),
                    Forms\Components\Toggle::make('status')
                        ->required(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Forms\Components\Section::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->content(fn ($record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->content(fn ($record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('deleted_at')
                        ->visible(fn ($record): bool => $record?->deleted_at ? true : false)
                        ->content(fn ($record): string => $record ? $record->deleted_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('country.name'),
                Tables\Columns\TextColumn::make('sort'),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d h:i A'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCities::route('/'),
            'create' => Pages\CreateCity::route('/create'),
            'view' => Pages\ViewCity::route('/{record}'),
            'edit' => Pages\EditCity::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    protected function shouldPersistTableFiltersInSession(): bool
    {
        return false;
    }

    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        'restore',
        'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
    ];
}
