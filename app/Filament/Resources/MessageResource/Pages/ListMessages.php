<?php

namespace App\Filament\Resources\MessageResource\Pages;

use App\Filament\Resources\MessageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Closure;

class ListMessages extends ListRecords
{

    protected static string $resource = MessageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
    protected function getTableRecordClassesUsing(): ?Closure
    {
        return fn ($record) => $record->seen == 1 ? 'bg-gray-500/5' : 'bg-white';
    }
}
