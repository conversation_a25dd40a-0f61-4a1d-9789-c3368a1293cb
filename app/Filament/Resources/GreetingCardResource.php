<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GreetingCardResource\Pages;
use App\Filament\Resources\GreetingCardResource\RelationManagers;
use App\Forms\Components\ImageOverlayTextCoordinatePicker;
use App\Models\GreetingCard;
use App\Traits\HasLabelTranslation;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GreetingCardResource extends Resource
{
    use HasLabelTranslation;

    protected static ?string $model = GreetingCard::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->columns(5)
            ->schema([
                Section::make([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\FileUpload::make('image')
                        ->image()
                        ->required()
                        ->live(),
                    Forms\Components\TextInput::make('image_width')
                        ->numeric()
                        ->default(1080)
                        ->required(),
                    Forms\Components\TextInput::make('image_height')
                        ->numeric()
                        ->default(1180)
                        ->required(),
                    Forms\Components\TextInput::make('text_font_size')
                        ->required()
                        ->numeric()
                        ->live()
                        ->default(50),
                    Forms\Components\ColorPicker::make('text_font_color')
                        ->required()
                        ->live()
                        ->default('#000000'),
                ])
                    ->columnSpan(3),
                Section::make([
                    Forms\Components\TextInput::make('text_coordinate.x')
                        ->numeric()
                        ->live(),
                    Forms\Components\TextInput::make('text_coordinate.y')
                        ->numeric()
                        ->live(),
                    ImageOverlayTextCoordinatePicker::make('text_coordinate')
                        ->imageUrl(function ($get) {
                            $image = $get('image');
                            if (!$image || !is_array($image) || count($image) == 0) return null;
                            $imageUrl =  $image[array_keys($image)[0]];
                            if (is_string($imageUrl)) return url("uploads/" . $imageUrl);
                            return $imageUrl->temporaryUrl();
                        })
                        ->overlayText("اهنأكم بحلول شهر الخير  \n والبركة")
                        ->textColor(function ($get) {
                            return $get('text_font_color');
                        })
                        ->textSize(function ($get) {
                            return $get('text_font_size');
                        })
                        ->imageWidth(function ($get) {
                            return $get('image_width');
                        })
                        ->imageHeight(function ($get) {
                            return $get('image_height');
                        }),
                    Forms\Components\TextInput::make('name_font_size')
                        ->required()
                        ->numeric()
                        ->default(64),
                    Forms\Components\ColorPicker::make('name_font_color')
                        ->required()
                        ->default('#000000'),
                    Forms\Components\TextInput::make('name_coordinate.x')
                        ->numeric()
                        ->live(),
                    Forms\Components\TextInput::make('name_coordinate.y')
                        ->numeric()
                        ->live(),
                    ImageOverlayTextCoordinatePicker::make('name_coordinate')
                        ->imageUrl(function ($get) {
                            $image = $get('image');
                            if (!$image || !is_array($image) || count($image) == 0) return null;
                            $imageUrl =  $image[array_keys($image)[0]];
                            if (is_string($imageUrl)) return url("uploads/" . $imageUrl);
                            return $imageUrl->temporaryUrl();
                        })
                        ->overlayText("عبدالله بن محمد المشعل")
                        ->textColor(function ($get) {
                            return $get('name_font_color');
                        })
                        ->textSize(function ($get) {
                            return $get('name_font_size');
                        })
                        ->imageWidth(function ($get) {
                            return $get('image_width');
                        })
                        ->imageHeight(function ($get) {
                            return $get('image_height');
                        }),
                ])
                    ->columnSpan(2)

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('image'),
                Tables\Columns\TextColumn::make('image_width')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('image_height')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('text_font_size')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\ColorColumn::make('text_font_color'),
                Tables\Columns\TextColumn::make('text_x')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('text_y')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name_font_size')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\ColorColumn::make('name_font_color'),
                Tables\Columns\TextColumn::make('name_x')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name_y')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ReplicateAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGreetingCards::route('/'),
            'create' => Pages\CreateGreetingCard::route('/create'),
            'view' => Pages\ViewGreetingCard::route('/{record}'),
            'edit' => Pages\EditGreetingCard::route('/{record}/edit'),
        ];
    }
}
