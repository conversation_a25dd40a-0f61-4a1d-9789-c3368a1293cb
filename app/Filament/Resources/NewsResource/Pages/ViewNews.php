<?php

namespace App\Filament\Resources\NewsResource\Pages;

use App\Filament\Actions\NotifyAction;
use App\Filament\Resources\NewsResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewNews extends ViewRecord
{

    protected static string $resource = NewsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            NotifyAction::make(),
            Actions\EditAction::make(),
        ];
    }
}
