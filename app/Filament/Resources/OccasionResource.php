<?php

namespace App\Filament\Resources;

use App\Filament\Actions\Table\NotifyAction;
use App\Filament\Resources\OccasionResource\Pages;
use App\Filament\Resources\OccasionResource\RelationManagers;
use App\Models\Occasion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasLabelTranslation;
use Filament\Actions\ActionGroup;
use Illuminate\Support\HtmlString;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class OccasionResource extends Resource
{

    use HasLabelTranslation;

    protected static ?string $model = Occasion::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $recordTitleAttribute = 'title';

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.cms");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\FileUpload::make('image')
                        ->previewable()
                        ->downloadable()
                        ->required()
                        ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                        ->directory('occasion/images'),
                    TinyEditor::make('content')
                        ->fileAttachmentsDirectory('occasion/content')
                        ->required(),
                    Forms\Components\FileUpload::make('images')
                        ->previewable()
                        ->downloadable()
                        ->multiple()
                        ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                        ->directory('occasion/images'),
                    Forms\Components\Repeater::make('youtube_videos')
                        ->schema(
                            [
                                Forms\Components\TextInput::make('youtube_video')
                                    ->live()
                                    ->required()
                                    ->afterStateUpdated(function (\Filament\Forms\Set $set, $state) {
                                        $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                                        if (preg_match($re, $state, $matches)) {
                                            $set('youtube_video', $matches[1]);
                                        } else {
                                            $set('youtube_video', null);
                                        }
                                    })
                                    ->maxLength(255),
                                Forms\Components\Placeholder::make('youtube_video_preview')
                                    ->content(function (\Filament\Forms\Get $get) {
                                        return new HtmlString("<iframe class='w-full aspect-video rounded-lg'  src=\"https://www.youtube.com/embed/{$get('youtube_video')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                                    })
                                    ->hidden(function (\Filament\Forms\Get $get) {
                                        return $get('youtube_video') == null;
                                    }),
                            ]
                        ),
                    Forms\Components\DateTimePicker::make('date')
                        ->required(),
                    Forms\Components\DateTimePicker::make('published_at')
                        ->required()
                        ->default(now()),
                    Forms\Components\Toggle::make('status')
                        ->required(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Forms\Components\Section::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->content(fn ($record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->content(fn ($record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('deleted_at')
                        ->visible(fn ($record): bool => $record?->deleted_at ? true : false)
                        ->content(fn ($record): string => $record ? $record->deleted_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('image'),
                Tables\Columns\ImageColumn::make('images')
                    ->circular()
                    ->limit(4)
                    ->ring(4)
                    ->stacked()
                    ->limitedRemainingText(isSeparate: true),
                Tables\Columns\TextColumn::make('youtube_videos_count')
                    ->getStateUsing(function ($record) {
                        return $record->youtube_videos == null ? 0 :  count($record->youtube_videos);
                    }),
                Tables\Columns\TextColumn::make('date')
                    ->dateTime('l j F Y h:i A'),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\TextColumn::make('published_at')
                    ->dateTime('l j F Y h:i A')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d h:i A'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                NotifyAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOccasions::route('/'),
            'create' => Pages\CreateOccasion::route('/create'),
            'view' => Pages\ViewOccasion::route('/{record}'),
            'edit' => Pages\EditOccasion::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    protected function shouldPersistTableFiltersInSession(): bool
    {
        return false;
    }

    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        'restore',
        'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
    ];
}
