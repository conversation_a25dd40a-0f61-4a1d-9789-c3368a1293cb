<?php

namespace App\Filament\Resources\FamilyTreeNodeResource\Pages;

use App\Filament\Resources\FamilyTreeNodeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFamilyTreeNode extends EditRecord
{
    protected static string $resource = FamilyTreeNodeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
