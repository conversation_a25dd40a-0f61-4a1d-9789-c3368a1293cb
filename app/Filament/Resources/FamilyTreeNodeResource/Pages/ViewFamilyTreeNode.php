<?php

namespace App\Filament\Resources\FamilyTreeNodeResource\Pages;

use App\Filament\Resources\FamilyTreeNodeResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFamilyTreeNode extends ViewRecord
{
    protected static string $resource = FamilyTreeNodeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
