<?php

namespace App\Filament\Resources\FamilyTreeNodeResource\Pages;

use App\Filament\Imports\FamilyTreeNodeImporter;
use App\Filament\Resources\FamilyTreeNodeResource;
use Filament\Actions;
use Filament\Actions\ImportAction;
use Filament\Resources\Pages\ListRecords;

class ListFamilyTreeNodes extends ListRecords
{
    protected static string $resource = FamilyTreeNodeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ImportAction::make('importFamilyTreeNodes')
                ->label(__('family_tree_node.actions.import.label'))
                ->modalHeading(__('family_tree_node.actions.import.heading'))
                ->color('info')
                ->importer(FamilyTreeNodeImporter::class),
            Actions\Action::make('view_tree')
                ->label(__('family_tree_node.actions.view_tree.label'))
                ->url(FamilyTreeNodeResource::getUrl('tree'))
                ->color('success'),
            Actions\CreateAction::make(),
        ];
    }
}
