<?php

namespace App\Filament\Resources\FamilyTreeNodeResource\Pages;

use App\Filament\Resources\FamilyTreeNodeResource;
use App\Models\FamilyTreeNode;
use Filament\Facades\Filament;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use SolutionForest\FilamentTree\Actions;
use SolutionForest\FilamentTree\Actions\Action;
use SolutionForest\FilamentTree\Actions\EditAction;
use SolutionForest\FilamentTree\Components\Tree;
use SolutionForest\FilamentTree\Concern;
use SolutionForest\FilamentTree\Resources\Pages\TreePage as BasePage;
use SolutionForest\FilamentTree\Support\Utils;

class FamilyTree extends BasePage
{
    protected static string $resource = FamilyTreeNodeResource::class;

    protected static int $maxDepth = 999;


    protected function getActions(): array
    {
        return [
            \Filament\Actions\CreateAction::make()
                ->url(FamilyTreeNodeResource::getUrl('create')),
            // SAMPLE CODE, CAN DELETE
            //\Filament\Pages\Actions\Action::make('sampleAction'),
        ];
    }

    protected function hasDeleteAction(): bool
    {
        return true;
    }

    protected function hasEditAction(): bool
    {
        return true;
    }

    protected function hasViewAction(): bool
    {
        return true;
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    protected function getFooterWidgets(): array
    {
        return [];
    }
    protected function getTreeActions(): array
    {
        return array_merge(
            [
                EditAction::make('add_children')
                    ->icon('heroicon-s-plus')
                    ->model($this->getModel())
                    ->tooltip(__('family_tree_node.actions.add_children.label'))
                    ->fillForm(function () {
                        return [];
                    })
                    ->form([
                        TagsInput::make('sons')
                            ->placeholder(__('family_tree_node.fields.sons.placeholder'))
                            ->reorderable(),
                        TagsInput::make('daughters')
                            ->placeholder(__('family_tree_node.fields.daughters.placeholder'))
                            ->reorderable(),
                    ])
                    ->modalSubmitActionLabel(__('family_tree_node.actions.add_children.submit'))
                    ->modalHeading(fn ($record) => __('family_tree_node.actions.add_children.heading', ['name' => $record->name]))
                    ->action(function (array $data, $record) {
                        $sons = [];
                        foreach ($data['sons'] as $key => $son) {
                            $sons[] = [
                                'name' => $son,
                                'order' => $key + 1,
                                'gender' => 1,
                                'parent_id' => $record->id,
                                'alive' => 1,
                            ];
                        }

                        $daughters = [];
                        foreach ($data['daughters'] as $key => $daughter) {
                            $daughters[] = [
                                'name' => $daughter,
                                'order' => $key + 1,
                                'gender' => 2,
                                'parent_id' => $record->id,
                                'alive' => 1,
                            ];
                        }
                        FamilyTreeNode::insert(array_merge($sons, $daughters));
                    }),
            ],
            ($this->hasEditAction() ? [$this->getEditAction()] : []),
            ($this->hasViewAction() ? [$this->getViewAction()] : []),
            ($this->hasDeleteAction() ? [$this->getDeleteAction()] : []),

        );
    }

    // CUSTOMIZE ICON OF EACH RECORD, CAN DELETE
    // public function getTreeRecordIcon(?\Illuminate\Database\Eloquent\Model $record = null): ?string
    // {
    //     return null;
    // }
}
