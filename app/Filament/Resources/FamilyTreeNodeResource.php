<?php

namespace App\Filament\Resources;

use App\Filament\Exports\FamilyTreeNodeExporter;
use App\Filament\Resources\FamilyTreeNodeResource\Pages;
use App\Filament\Resources\FamilyTreeNodeResource\RelationManagers;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Traits\HasLabelTranslation;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FamilyTreeNodeResource extends Resource
{
    use HasLabelTranslation;

    protected static ?string $model = FamilyTreeNode::class;

    protected static ?string $navigationIcon = 'icon-tree';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = -2;

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.family_tree");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\TextInput::make('name')
                        ->required(),
                    Forms\Components\TextInput::make('nick_name'),
                    Forms\Components\Textarea::make('overview'),
                    Forms\Components\FileUpload::make('image'),
                    Forms\Components\Select::make('gender')
                        ->options(__('common.options.gender'))
                        ->native(false)
                        ->required(),
                    Forms\Components\TextInput::make('order')
                        ->numeric(),
                    SelectTree::make('parent_id')
                        ->relationship('parent', 'name', 'parent_id')
                        ->parentNullValue(-1)
                        ->enableBranchNode()
                        ->withCount()
                        ->required(FamilyTreeNode::count() > 0)
                        ->grouped(false),

                    Forms\Components\Toggle::make('alive')
                        ->inline(false)
                        ->live()
                        ->default(true),
                    Forms\Components\DatePicker::make('birth_date')
                        ->displayFormat('Y-m-d'),
                    Forms\Components\TextInput::make('birth_place'),
                    Forms\Components\Textarea::make('job'),
                    Forms\Components\Textarea::make('address')
                        ->visible(fn($get): bool => $get('alive') == true),
                    Forms\Components\DatePicker::make('death_date')
                        ->displayFormat('Y-m-d')
                        ->visible(fn($get): bool => $get('alive') == false),
                    Forms\Components\TextInput::make('death_place')
                        ->visible(fn($get): bool => $get('alive') == false),
                    Forms\Components\Select::make('family_member_id')
                        ->options(FamilyMember::get()->pluck('display_name', 'id'))
                        ->searchable(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Forms\Components\Section::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->content(fn($record): ?string => $record != null ? $record?->created_at?->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->content(fn($record): ?string => $record != null ? $record?->updated_at?->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('deleted_at')
                        ->visible(fn($record): bool => $record?->deleted_at ? true : false)
                        ->content(fn($record): ?string => $record != null ? $record?->deleted_at?->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('gender')
                    ->formatStateUsing(fn($state): string => __('common.options.gender.' . $state))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    ExportBulkAction::make()
                        ->exporter(FamilyTreeNodeExporter::class),

                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'tree' => Pages\FamilyTree::route('/tree'),
            'index' => Pages\ListFamilyTreeNodes::route('/'),
            'create' => Pages\CreateFamilyTreeNode::route('/create'),
            'view' => Pages\ViewFamilyTreeNode::route('/{record}'),
            'edit' => Pages\EditFamilyTreeNode::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
