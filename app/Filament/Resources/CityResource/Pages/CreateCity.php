<?php

namespace App\Filament\Resources\CityResource\Pages;

use App\Filament\Resources\CityResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use function Filament\Support\get_model_label;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class CreateCity extends CreateRecord
{
    protected static string $resource = CityResource::class;
    protected function getHeaderActions(): array
    {
        return [
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationMessage(): ?string
    {
        return __(str_replace(' ', '_', get_model_label($this->getModel())) . '.messages.created');
    }
}
