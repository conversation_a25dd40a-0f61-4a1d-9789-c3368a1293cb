<?php

namespace App\Filament\Resources\FamilyBranchResource\Pages;

use App\Filament\Resources\FamilyBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFamilyBranch extends ViewRecord
{

    protected static string $resource = FamilyBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
