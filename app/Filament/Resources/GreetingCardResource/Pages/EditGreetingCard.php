<?php

namespace App\Filament\Resources\GreetingCardResource\Pages;

use App\Filament\Resources\GreetingCardResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditGreetingCard extends EditRecord
{
    protected static string $resource = GreetingCardResource::class;
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['text_coordinate']["x"] = 50;
        $data['name_coordinate']["x"] = 50;
        // $coordinate = $data['text_coordinate'];
        // $data['text_x'] = $coordinate["x"];
        // $data['text_y'] = $coordinate["y"];
        // unset($data['text_coordinate']);
        // dd($data);
        return $data;
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
