<?php

namespace App\Filament\Resources\GreetingCardResource\Pages;

use App\Filament\Resources\GreetingCardResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListGreetingCards extends ListRecords
{
    protected static string $resource = GreetingCardResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
