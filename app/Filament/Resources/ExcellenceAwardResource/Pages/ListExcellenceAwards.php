<?php

namespace App\Filament\Resources\ExcellenceAwardResource\Pages;

use App\Filament\Resources\ExcellenceAwardResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExcellenceAwards extends ListRecords
{
    protected static string $resource = ExcellenceAwardResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
