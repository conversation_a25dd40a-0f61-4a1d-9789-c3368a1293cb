<?php

namespace App\Filament\Resources\ExcellenceAwardResource\Pages;

use App\Filament\Resources\ExcellenceAwardResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExcellenceAward extends EditRecord
{
    protected static string $resource = ExcellenceAwardResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
