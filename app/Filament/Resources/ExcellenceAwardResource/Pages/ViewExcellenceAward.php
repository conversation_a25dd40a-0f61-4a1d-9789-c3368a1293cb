<?php

namespace App\Filament\Resources\ExcellenceAwardResource\Pages;

use App\Filament\Actions\NotifyAction;
use App\Filament\Resources\ExcellenceAwardResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewExcellenceAward extends ViewRecord
{
    protected static string $resource = ExcellenceAwardResource::class;

    protected function getHeaderActions(): array
    {
        return [
            NotifyAction::make(),
            Actions\EditAction::make(),
        ];
    }
}
