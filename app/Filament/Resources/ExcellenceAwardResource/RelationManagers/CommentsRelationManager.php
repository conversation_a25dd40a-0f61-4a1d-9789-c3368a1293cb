<?php

namespace App\Filament\Resources\ExcellenceAwardResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CommentsRelationManager extends RelationManager
{
    protected static string $relationship = 'comments';

    // public static function getTitle( $ownerRecord, string $pageClass): string
    // {
    //     return __('comment.title');
    // }
    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('body')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('body')
            ->heading(__('comment.title'))
            ->emptyStateHeading(__('comment.messages.empty'))
            ->columns([
                Tables\Columns\TextColumn::make('user.displayName')
                    ->label(__('comment.fields.user_name.label')),
                Tables\Columns\TextColumn::make('body')
                    ->label(__('comment.fields.body.label')),
                Tables\Columns\ToggleColumn::make('status')
                    ->label(__('comment.fields.status.label'))
                    ,
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('comment.fields.created_at.label')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('comment.fields.updated_at.label')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
