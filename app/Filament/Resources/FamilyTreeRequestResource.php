<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FamilyTreeRequestResource\Pages;
use App\Filament\Resources\FamilyTreeRequestResource\RelationManagers;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Models\FamilyTreeRequest;
use App\Traits\HasLabelTranslation;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FamilyTreeRequestResource extends Resource
{
    use HasLabelTranslation;

    protected static ?string $model = FamilyTreeRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.family_tree");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make([
                    Forms\Components\Select::make('request_type')
                        ->options(__('family_tree_request.options.request_type'))
                        ->required(),
                    Forms\Components\Select::make('family_member_id')
                        ->options(FamilyMember::get()->pluck('display_name', 'id')),
                    Forms\Components\Select::make('family_tree_node_id')
                        ->options(FamilyTreeNode::pluck('name', 'id')),
                    Forms\Components\Textarea::make('note')
                        ->columnSpanFull(),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('family_member.display_name')
                    ->searchable()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('request_type')
                    ->badge()
                    ->formatStateUsing(function ($record) {
                        return __('family_tree_request.options.request_type.' . $record->request_type);
                    })
                    ->colors([
                        'primary',
                        "danger" => 0,
                        "success" => 1,
                        "warning" => 2
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('family_tree_node.name')
                    ->searchable()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\Action::make('add_son')
                    ->label(__('Add son'))
                    ->color('success')
                    ->icon('heroicon-s-user-plus')
                    ->visible(function (FamilyTreeRequest $record) {
                        return $record->request_type == 1;
                    })
                    ->form([
                        Forms\Components\TextInput::make('name')
                            ->label(__('Son name'))
                            ->required()
                            ->maxLength(255),
                    ])
                    ->action(function (FamilyTreeRequest $record, array $data, $action) {
                        $name = $data['name'];
                        $parentId = $record->family_tree_node_id;
                        FamilyTreeNode::create([
                            'name' => $name,
                            'parent_id' => $parentId
                        ]);
                        $action->success();
                    })
                    ->successNotificationTitle(__('Son added successfully')),
                Tables\Actions\Action::make('delete_node')
                    ->label(__('Delete node'))
                    ->requiresConfirmation()
                    ->color('danger')
                    ->icon('heroicon-s-trash')
                    ->visible(function (FamilyTreeRequest $record) {
                        return $record->request_type == 0;
                    })
                    ->action(function (FamilyTreeRequest $record) {
                        $nodeId = $record->family_tree_node_id;
                        FamilyTreeNode::where('id', $nodeId)->delete();
                    }),
                Tables\Actions\Action::make('visit_edit_node')
                    ->label(__('Visit edit node'))
                    ->color('warning')
                    ->icon('heroicon-s-pencil')
                    ->visible(function (FamilyTreeRequest $record) {
                        return $record->request_type == 2;
                    })
                    ->url(function (FamilyTreeRequest $record) {
                        return FamilyTreeNodeResource::getUrl('edit', ['record' => $record->family_tree_node_id]);
                    }, true),

                Tables\Actions\DeleteAction::make()
                    ->label(__('Delete request')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyTreeRequests::route('/'),
            // 'create' => Pages\CreateFamilyTreeRequest::route('/create'),
            // 'edit' => Pages\EditFamilyTreeRequest::route('/{record}/edit'),
        ];
    }
}
