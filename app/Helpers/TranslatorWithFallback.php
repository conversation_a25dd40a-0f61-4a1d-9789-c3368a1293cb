<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Translation\Translator;

class TranslatorWithFallback extends Translator
{
    public function get($key, array $replace = [], $locale = null, $fallback = true)
    {
        $trans = parent::get($key, $replace, $locale, $fallback);
        if ($trans != $key) {
            return $trans;
        }

        $newKey = Str::of($key)->after('.');
        $newKey = 'common.' . $newKey;
        $newTrans = parent::get($newKey, $replace, $locale, $fallback);
        if ($newTrans != $newKey) {
            return $newTrans;
        }
        if (Str::endsWith($key, '.placeholder')) {
            return null;
        }

        return  $trans;
    }
}
