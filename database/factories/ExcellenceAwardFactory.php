<?php

namespace Database\Factories;

use App\Enums\ExcellenceAwardType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExcellenceAward>
 */
class ExcellenceAwardFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'content' => $this->faker->paragraphs(3, true),
            'type' => $this->faker->randomElement([
                ExcellenceAwardType::AcademicExcellence->value,
                ExcellenceAwardType::QuranMemorization->value,
            ]),
            'status' => true,
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the excellence award is for academic excellence.
     */
    public function academicExcellence(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => ExcellenceAwardType::AcademicExcellence->value,
        ]);
    }

    /**
     * Indicate that the excellence award is for Quran memorization.
     */
    public function quranMemorization(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => ExcellenceAwardType::QuranMemorization->value,
        ]);
    }

    /**
     * Indicate that the excellence award is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => false,
        ]);
    }
}
