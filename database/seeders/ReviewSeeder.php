<?php

namespace Database\Seeders;

use App\Models\FamilyMember;
use App\Models\Review;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all active family members
        $familyMembers = FamilyMember::where('status', true)->get();

        if ($familyMembers->count() < 2) {
            $this->command->info('Not enough family members to create reviews. Need at least 2 active members.');
            return;
        }

        $this->command->info('Creating sample reviews...');

        // Create some sample reviews
        $reviewsCreated = 0;
        
        foreach ($familyMembers as $reviewer) {
            // Each member reviews 1-3 other random members
            $numberOfReviews = rand(1, min(3, $familyMembers->count() - 1));
            
            $otherMembers = $familyMembers->where('id', '!=', $reviewer->id)->shuffle();
            
            for ($i = 0; $i < $numberOfReviews; $i++) {
                $reviewedUser = $otherMembers->skip($i)->first();
                
                if (!$reviewedUser) {
                    break;
                }
                
                // Check if review already exists
                $existingReview = Review::where('reviewer_id', $reviewer->id)
                    ->where('reviewed_user_id', $reviewedUser->id)
                    ->first();
                
                if (!$existingReview) {
                    Review::create([
                        'reviewer_id' => $reviewer->id,
                        'reviewed_user_id' => $reviewedUser->id,
                        'rating' => rand(3, 5), // Mostly positive reviews
                        'comment' => $this->getRandomComment(),
                        'status' => true,
                    ]);
                    
                    $reviewsCreated++;
                }
            }
        }

        $this->command->info("Created {$reviewsCreated} sample reviews.");
    }

    /**
     * Get a random review comment.
     */
    private function getRandomComment(): ?string
    {
        $comments = [
            'عضو رائع في العائلة، دائماً مفيد ومتعاون.',
            'شخص محترم وودود، أستمتع بالتعامل معه.',
            'عضو نشط ومساهم في فعاليات العائلة.',
            'شخصية إيجابية ومحبوبة من الجميع.',
            'دائماً ما يقدم المساعدة عند الحاجة.',
            'عضو مميز ومثال يحتذى به.',
            'شخص طيب القلب ومتفهم.',
            'يساهم بشكل إيجابي في تماسك العائلة.',
            'محترم ومقدر من جميع أفراد العائلة.',
            'شخصية قيادية ومؤثرة بشكل إيجابي.',
            null, // Some reviews without comments
            null,
            null,
        ];

        return $comments[array_rand($comments)];
    }
}
