<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
        $user =   User::updateOrCreate([
            "id" => 1,
        ], [
            'name' => 'Admin',
            'email' => '<EMAIL>',
            "is_admin" => true,
            "is_active" => true,
            "password" => Hash::make('25002500'),
        ],);
        $user2 =   User::updateOrCreate([
            "id" => 2,
        ], [
            'name' => 'Admin',
            'email' => '<EMAIL>',
            "is_admin" => true,
            "is_active" => true,
            "password" => Hash::make('123456'),
        ],);
        Role::updateOrCreate([
            "id" => 1,
        ], [
            "name" => "super_admin",
            "guard_name" => "web",
        ]);
        $user->assignRole('super_admin');
        $user2->assignRole('super_admin');
    }
}
