<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_tree_nodes', function (Blueprint $table) {
            $table->id();
            $table->text('name');
            $table->text('nick_name')->nullable();
            $table->text('image')->nullable();
            $table->text('overview')->nullable();
            $table->integer('parent_id')->default(-1)->constrained('family_tree_nodes');
            $table->integer('order')->default(0)->index();
            $table->tinyInteger('gender');
            $table->boolean('alive')->default(1);
            $table->date('birth_date')->nullable();
            $table->date('death_date')->nullable();
            $table->text('birth_place')->nullable();
            $table->text('death_place')->nullable();
            $table->text('job')->nullable();
            $table->text('address')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family_tree_nodes');
    }
};
