<?php

use App\Models\FamilyTreeNode;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $nodes = FamilyTreeNode::all();
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            $table->dropColumn('gender');
        });

        Schema::table('family_tree_nodes', function (Blueprint $table) {
            $table->tinyInteger('gender')->nullable()->default(1);
        });

        foreach ($nodes as $node) {
            FamilyTreeNode::where('id', $node->id)->update(['gender' => $node->gender]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $nodes = FamilyTreeNode::all();
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            $table->dropColumn('gender');
        });
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            $table->tinyInteger('gender');
        });
    }
};
