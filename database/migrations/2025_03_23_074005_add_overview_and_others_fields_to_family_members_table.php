<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('family_members', function (Blueprint $table) {
            $table->string('country_code')
                ->nullable()
                ->default("+966")
                ->after('gender');
            $table->text('overview')
                ->nullable()
                ->after('grandfather_name');
            $table->string('cover_image')
                ->nullable()
                ->after('image');
            $table->string('facebook_link')
                ->nullable()
                ->after('cover_image');
            $table->string('x_link')
                ->nullable()
                ->after('facebook_link');
            $table->string('snapshot_link')->nullable()->after('x_link');
            $table->string('youtube_link')->nullable()->after('snapshot_link');
            $table->string('linkedin_link')->nullable()->after('youtube_link');
            $table->string('instagram_link')->nullable()->after('linkedin_link');
            $table->string('cv_file')->nullable()->after('instagram_link');
            $table->unsignedBigInteger('family_tree_node_id')->nullable()->after('cv_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('family_members', function (Blueprint $table) {
            $table->dropColumn('country_code');
            $table->dropColumn('overview');
            $table->dropColumn('cover_image');
            $table->dropColumn('facebook_link');
            $table->dropColumn('x_link');
            $table->dropColumn('snapshot_link');
            $table->dropColumn('youtube_link');
            $table->dropColumn('linkedin_link');
            $table->dropColumn('instagram_link');
            $table->dropColumn('cv_file');
            $table->dropColumn('family_tree_node_id');
        });
    }
};
