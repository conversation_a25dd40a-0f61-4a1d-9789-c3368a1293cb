<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_tree_requests', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('request_type');
            $table->foreignId('family_member_id')
                ->nullable()
                ->constrained()
                ->noActionOnDelete();
            $table->foreignId('family_tree_node_id')
                ->nullable()
                ->constrained()
                ->noActionOnDelete();
            $table->text('note')
                ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family_tree_requests');
    }
};
