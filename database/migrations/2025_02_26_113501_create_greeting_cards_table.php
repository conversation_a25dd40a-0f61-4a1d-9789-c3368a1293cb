<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('greeting_cards', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('image');
            $table->double('image_width', 10, 2);
            $table->double('image_height', 10, 2);
            $table->integer('text_font_size')->default(40); // Font size
            $table->string('text_font_color')->default('#FFFFFF'); // Font color (hex code)
            $table->string('text_x')->default(50); // X coordinate for text placement
            $table->string('text_y')->default(50); // Y coordinate for text placement
            $table->integer('name_font_size')->default(40); // Font size
            $table->string('name_font_color')->default('#FFFFFF'); // Font color (hex code)
            $table->string('name_x')->default(50); // X coordinate for name placement
            $table->string('name_y')->default(50);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('greeting_cards');
    }
};
