<?php

use App\Models\FamilyMember;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $familyMembers = FamilyMember::all();
        Schema::table('family_members', function (Blueprint $table) {
            $table->string('grandfather_name')->nullable()->change();
            $table->unsignedBigInteger('branch_id')->nullable()->change();
            $table->dropColumn('gender');
        });

        Schema::table('family_members', function (Blueprint $table) {
            $table->tinyInteger('gender')->nullable()->after('branch_id');
        });

        foreach ($familyMembers as $familyMember) {
            FamilyMember::where('id', $familyMember->id)->update(['gender' => $familyMember->gender]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $familyMembers = FamilyMember::all();
        Schema::table('family_members', function (Blueprint $table) {
            $table->string('grandfather_name')->nullable(false)->change();
            $table->unsignedBigInteger('branch_id')->nullable(false)->change();
            $table->dropColumn('gender');
        });

        Schema::table('family_members', function (Blueprint $table) {
            $table->tinyInteger('gender')->after('branch_id');
        });

        foreach ($familyMembers as $familyMember) {
            FamilyMember::where('id', $familyMember->id)->update(['gender' => $familyMember->gender]);
        }
    }
};
