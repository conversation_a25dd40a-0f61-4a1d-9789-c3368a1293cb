<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('family_member_devices', function (Blueprint $table) {
            $table->renameColumn('player_id', 'fcm_token');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('family_member_devices', function (Blueprint $table) {
            $table->renameColumn('fcm_token', 'player_id');
        });
    }
};
