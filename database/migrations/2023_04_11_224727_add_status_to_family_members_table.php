<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('family_members', function (Blueprint $table) {
            $table->tinyInteger('status')->after('image')->default(0);
            $table->date('birth_date')->after('image')->nullable();
            $table->string('birth_place')->after('image')->nullable();
            $table->unsignedBigInteger('country_id')->after('image')->nullable();
            $table->unsignedBigInteger('city_id')->after('image')->nullable();
            $table->string('address')->after('image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('family_members', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('birth_date');
            $table->dropColumn('birth_place');
            $table->dropColumn('country_id');
            $table->dropColumn('city_id');
            $table->dropColumn('address');
        });
    }
};
