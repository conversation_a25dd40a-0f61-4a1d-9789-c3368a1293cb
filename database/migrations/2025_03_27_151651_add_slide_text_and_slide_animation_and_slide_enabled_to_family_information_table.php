<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('family_information', function (Blueprint $table) {
            $table->text('slide_text')->nullable();
            $table->boolean('slide_coffette_enabled')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('family_information', function (Blueprint $table) {
            $table->dropColumn('slide_text');
            $table->dropColumn('slide_coffette_enabled');
        });
    }
};
