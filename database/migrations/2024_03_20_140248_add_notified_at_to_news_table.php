<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('news', function (Blueprint $table) {
            $table->timestamp('notified_at')->nullable();
        });
        Schema::table('albums', function (Blueprint $table) {
            $table->timestamp('notified_at')->nullable();
        });
        Schema::table('excellence_awards', function (Blueprint $table) {
            $table->timestamp('notified_at')->nullable();
        });
        Schema::table('occasions', function (Blueprint $table) {
            $table->timestamp('notified_at')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('news', function (Blueprint $table) {
            $table->dropColumn('notified_at');
        });
        Schema::table('albums', function (Blueprint $table) {
            $table->dropColumn('notified_at');
        });
        Schema::table('excellence_awards', function (Blueprint $table) {
            $table->dropColumn('notified_at');
        });
        Schema::table('occasions', function (Blueprint $table) {
            $table->dropColumn('notified_at');
        });
    }
};
