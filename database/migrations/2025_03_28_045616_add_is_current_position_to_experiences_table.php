<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            $table->dropColumn('start_date');
            $table->dropColumn('end_date');
        });
        Schema::table('experiences', function (Blueprint $table) {
            $table->boolean('is_current_position')->default(false)->after('description');
            $table->date('start_date')->nullable()->after('is_current_position');
            $table->date('end_date')->nullable()->after('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experiences', function (Blueprint $table) {
            $table->dropColumn('is_current_position');
            $table->dropColumn('start_date');
            $table->dropColumn('end_date');
        });
        Schema::table('experiences', function (Blueprint $table) {
            $table->string('start_date')->nullable()->after('description');
            $table->string('end_date')->nullable()->after('start_date');
        });
    }
};
