<?php

use App\Facades\Fcm;
use App\Facades\Whatsapp;
use App\Filament\Pages\DeletionRequestPage;
use App\Filament\Resources\FamilyMemberResource;
use App\Http\Livewire\ConnectWhatsappPage;
use App\Http\Livewire\Pages\GreetingCardsPage;
use App\Models\FamilyMember;
use App\Models\FamilyMemberDevice;
use App\Models\FamilyTreeNode;
use App\Models\GreetingCard;
use App\Models\User;
use Filament\Notifications\Actions\Action;
use Illuminate\Support\Facades\Route;
use Filament\Notifications\Events\DatabaseNotificationsSent;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Messaging\Topic;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/privacy', function () {
    return view('pages.privacy');
});
Route::get('/account/delete', DeletionRequestPage::class);
// Route::get('/test-notification', function () {
//     $recipients = User::where('is_active', true)->get();
//     Notification::make()
//         ->title('Saved successfully')
//         ->actions([
//             Action::make('view')
//                 ->url('https://google.com', shouldOpenInNewTab: true),
//         ])
//         ->sendToDatabase($recipients);
//     foreach ($recipients as $recipient) {
//         event(new DatabaseNotificationsSent($recipient));
//     }
// });
// Route::get('test', function () {
//     return Fcm::subscribeToTopics(['all'], FamilyMemberDevice::pluck('fcm_token')->toArray());
// });

// Route::get('test/notification', function () {
//     return Fcm::sendToTopic(topic: 'all', title: 'test', body: 'test image', imageUrl: 'https://miro.medium.com/v2/resize:fit:1400/1*FDsQBK7k-TC3WURVr8KVRg.jpeg', data: ["screen" => "post"]);
// });
// Route::get('set-alive', function () {
//     FamilyTreeNode::whereNotNull('name')->update(['alive' => true]);
// });
Route::get('/greeting-cards', GreetingCardsPage::class);
Route::get('/test-greeting-cards', function () {
    dd(GreetingCard::first()->text_coordinate);
});

Route::get('/app/{any}', function () {
    return file_get_contents(public_path('app/index.html'));
})->where('any', '.*');

Route::get('/test-wa', function () {
    // $family_member = FamilyMember::where('mobile', '777582069')->first();
    // $code = 131367;
    // $adminMessage = "تم انضمام فرد جديد لتطبيق الاسرة:\n\nاسم الفرد: *" . $family_member->first_name . " " . $family_member->middle_name . "*\nرقم الجوال: *" . $family_member->mobile . "* \n يمكنك تفعيل الحساب من الرابط التالى: \n" . FamilyMemberResource::getUrl('edit', ['record' => $family_member->id]);
    dd(Whatsapp::send("967777582069", "فقط رسالة اختبار"));
});
Route::get('/get-numbers-start-with-05', function () {
    $family_members = FamilyMember::where('mobile', 'like', '05%')->get();
    foreach ($family_members as $value) {
        // delete begining 0 from mobile number and save it
        $mobile = $value->mobile;
        // dump($mobile);
        // replace arabic numbers with english numbers
        $mobile = str_replace(['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'], ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], $mobile);
        $mobile = substr($mobile, 1);
        // dump($mobile);
        $value->update(['mobile' => $mobile, 'status' => 0]);
    }
});
Route::get('/duplicates', function () {
    $duplicateMobileNumbers = FamilyMember::select('mobile', DB::raw('count(*) as total'))
        ->groupBy('mobile')
        ->having('total', '>', 1)
        ->pluck('mobile')
        ->toArray();

    $duplicateAccounts = FamilyMember::whereIn('mobile', $duplicateMobileNumbers)->where('status', 0)->get();
    foreach ($duplicateAccounts as $account) {
        $account->delete();
    }
    dd($duplicateAccounts); // You can then process or display these accounts
});
Route::get('/connect-whatsapp', ConnectWhatsappPage::class);
Route::get('/app', function () {
    return file_get_contents(public_path('app/index.html'));
});
Route::get('/app/{any}', function () {
    return file_get_contents(public_path('app/index.html'));
})->where('any', '.*');
