{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "vcs", "url": "https://github.com/aymanalareqi/filament-users-roles.git"}, {"type": "path", "url": "packages/alareqi/filament-helper"}, {"type": "vcs", "url": "https://github.com/aymanalareqi/filament-fcm.git"}], "require": {"php": "^8.1.0", "aaronfrancis/fast-paginate": "^2.0", "alareqi/filament-users-roles": "*", "appstract/laravel-opcache": "^4.0", "awcodes/light-switch": "^1.0", "codewithdennis/filament-select-tree": "^3.1", "dedoc/scramble": "^0.12.18", "filament/filament": "^3.2", "guzzlehttp/guzzle": "^7.2", "husam-tariq/filament-timepicker": "^3.0", "intervention/image": "^2.7", "intervention/imagecache": "^2.6", "khaled.alshamaa/ar-php": "^6.3", "kreait/laravel-firebase": "^5.8", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.7", "mohamedsabil83/filament-forms-tinyeditor": "^2.2", "plesk/ext-laravel-integration": "*", "pusher/pusher-php-server": "^7.2", "solution-forest/filament-tree": "^2.0"}, "require-dev": {"doctrine/dbal": "^3.6", "fakerphp/faker": "^1.9.1", "filament/upgrade": "^3.2", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan filament:upgrade", "npm run build"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}, "github-oauth": {"github.com": "****************************************"}}, "minimum-stability": "dev", "prefer-stable": true}