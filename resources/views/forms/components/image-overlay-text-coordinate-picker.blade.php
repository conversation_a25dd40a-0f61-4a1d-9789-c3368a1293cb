<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div x-data="{
        state: $wire.{{ $applyStateBindingModifiers("\$entangle('{$getStatePath()}')") }},
        xPos: 50,
        yPos: 50,
        fontSize: {{ $getTextSize() ?? '5' }}, // Font size as a percentage of container width
        dragging: false,
        containerWidth: 0,
        imageWidth: {{ $getImageWidth() ?? 800 }}, // Default image width
        imageHeight: {{ $getImageHeight() ?? 600 }}, // Default im
        init() {
            // Get the container width and update it on resize
            this.updateContainerWidth();
            window.addEventListener('resize', () => this.updateContainerWidth());
            {{-- const image = this.$refs.imageContainer;
            image.style.width = `${this.imageWidth}px`;
            image.style.height = `${this.imageHeight}px`; --}}
        },
    
        updateContainerWidth() {
            const container = this.$refs.imageContainer;
            this.containerWidth = container.offsetWidth;
        },
    
        startDrag(event) {
            this.dragging = true;
            this.updatePosition(event);
        },
        stopDrag() {
            this.dragging = false;
            // Update the state with the new coordinates
            $wire.set('{{ $getStatePath() }}', { x: this.xPos, y: this.yPos });
        },
        updatePosition(event) {
            if (this.dragging) {
                const rect = event.target.parentElement.getBoundingClientRect();
                this.xPos = ((event.clientX - rect.left) / rect.width) * 100;
                this.yPos = ((event.clientY - rect.top) / rect.height) * 100;
            }
        }
    }" x-init="console.log(state);
    xPos = state?.x ?? 50;
    yPos = state?.y ?? 50;">
        <div class="relative" x-ref="imageContainer">
            <!-- Background Image -->
            <img id="image-preview" src="{{ $getImageUrl() ?? 'https://almashalfamily.test/assets/images/design2.jpg' }}"
                class="w-full h-full " x-ref="image" />

            <!-- Draggable Text Overlay -->
            <div class="absolute text-center"
                style="top: 50%; left: 50%; transform: translate(-50%, -50%); color: {{ $getTextColor() ?? '#fff' }};"
                x-bind:style="`top: ${yPos}%; left: ${xPos}%; transform: translate(-50%, -50%); color: {{ $getTextColor() ?? '#fff' }}; font-size: ${fontSize  * (containerWidth/imageWidth)}px;`"
                x-on:mousedown="startDrag($event)" x-on:mousemove="updatePosition($event)" x-on:mouseup="stopDrag()"
                x-on:mouseleave="stopDrag()">
                {{ $getOverlayText() }}
            </div>
        </div>
    </div>
</x-dynamic-component>
