<div dir="rtl" class="font-tajawal bg-gray-50 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4" x-data="{
        cards: @js($this->cards),
        selectedDesign: 0,
        name: '',
        text: 'أهنئكم بمناسبة عيد الفطر المبارك\nتقبل الله منا ومنكم صالح الأعمال',
        cardWidth: 0,
        init() {
            this.updateCardWidth();
            window.addEventListener('resize', () => {
                this.updateCardWidth();
            });
        },
        updateCardWidth() {
            const card = this.$refs.card;
            this.cardWidth = card.offsetWidth;
        },
        selectDesign(index) {
            this.selectedDesign = index;
        },
        downloadCard() {
            const card = document.querySelector('.relative');
            html2canvas(card).then(canvas => {
                const link = document.createElement('a');
                link.href = canvas.toDataURL('image/png');
                link.download = 'greeting-card.png';
                link.click();
            });
        },
    }">
        <!-- Professional Title -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">بطاقات تهنئة عيد الفطر</h1>
            <p class="text-lg text-gray-600">أنشئ بطاقات تهنئة مخصصة بكل سهولة</p>
            <div class="mt-4 w-20 h-1 bg-primary-500 mx-auto rounded-full"></div> <!-- Decorative line -->
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="mt-8 ">
                <div class="mb-6">
                    <label class="block text-gray-700 font-bold mb-2">اختر تصميم:</label>
                    <div class="grid grid-cols-3 lg:grid-cols-3 gap-4">
                        <template x-for="(card, index) in cards" :key="index">
                            <img :src="cards[index].image_url" @click="selectDesign(index)"
                                :class="{ 'border-4 border-primary-500': selectedDesign === index }"
                                class="cursor-pointer rounded-lg shadow-md hover:shadow-lg transition-transform transform hover:scale-105">
                        </template>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 font-bold mb-2">اسمك:</label>
                    <input type="text" x-model="name" placeholder="أدخل اسمك"
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                </div>
                <div>
                    <label class="block text-gray-700 font-bold mb-2">نص التهنئة:</label>
                    <textarea type="text" x-model="text" placeholder="أدخل نص التهنئة"
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                </div>
                {{-- <div class="mb-6 text-center md:hidden">
                    <button wire:loading.attr="disabled" wire:click="downloadCard(cards[selectedDesign].id, name, text)"
                        class="bg-primary-500 text-white font-bold px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors">
                        <!-- Default button text -->
                        <span>تحميل البطاقة</span>

                        <!-- Loading indicator -->
                        <span wire:loading>
                            <svg class="animate-spin h-4 w-4 text-white mr-2" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="1"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                        </span>
                    </button>
                </div> --}}
            </div>

            <div class="mt-0 md:mt-8 order-1 ">
                <label class="block text-gray-700 font-bold mb-2">معاينة التصميم:</label>
                <div class="relative bg-white shadow-md" x-ref="card">
                    <img :src="cards[selectedDesign].image_url" class="w-full">
                    <div x-text="text" class="absolute text-center font-medium"
                        x-bind:style="`top: ${ cards[selectedDesign].text_y }%;left: ${ cards[selectedDesign].text_x }%; transform: translate(-50%, -50%); color: ${ cards[selectedDesign].text_font_color }; font-size: ${ cards[selectedDesign].text_font_size * (cardWidth / cards[selectedDesign].image_width) }px; white-space: pre;`">
                    </div>
                    <div x-text="name" class="absolute text-center w-full font-medium"
                        x-bind:style="`top: ${ cards[selectedDesign].name_y }%;left: ${ cards[selectedDesign].name_x }%; transform: translate(-50%, -50%); color: ${ cards[selectedDesign].name_font_color }; font-size: ${ cards[selectedDesign].name_font_size * (cardWidth / cards[selectedDesign].image_width) }px; white-space: pre;`">
                    </div>
                </div>
                <div class="text-center mt-8 ">
                    <button wire:loading.attr="disabled" wire:click="downloadCard(cards[selectedDesign].id, name, text)"
                        class="bg-primary-500 text-white font-bold px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors">
                        <!-- Default button text -->
                        <span>تحميل البطاقة</span>

                        <!-- Loading indicator -->
                        <span wire:loading>
                            <svg class="animate-spin h-4 w-4 text-white mr-2" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="1"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
