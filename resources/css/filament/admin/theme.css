@import '/vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

.fi-sidebar {
    border-image: linear-gradient(to bottom, transparent 10%, rgba(var(--primary-600), 30%), rgba(var(--primary-500), 30%), transparent 90%) 1;
    border-right-width: 1px;
}

[dir="rtl"] .fi-sidebar {
    border-right-width: unset;
    border-left-width: 1px !important;
}

.fi-btn {
    border-radius: 9999px;
}

.fi-ta-search-field .fi-input-wrp,
.fi-pagination-records-per-page-select .fi-input-wrp {
    border-radius: 9999px !important;
}