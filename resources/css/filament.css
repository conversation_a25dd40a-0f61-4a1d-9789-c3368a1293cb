@import '../../vendor/filament/filament/resources/css/app.css';

@import 'tippy.js/dist/tippy.css';
@import 'tippy.js/themes/light.css';

@tailwind base;
@tailwind components;
@tailwind utilities;
@layer utilities {
    .filament-sidebar-nav{
        scrollbar-width: thin;
    }
    .filament-sidebar-nav::-webkit-scrollbar {
      @apply w-2;
    }
    .filament-sidebar-nav::-webkit-scrollbar:hover {
        @apply w-3;
    }

    .filament-sidebar-nav::-webkit-scrollbar-track {
      @apply w-2 bg-gray-300 ;

    }
    .dark .filament-sidebar-nav::-webkit-scrollbar-track {
      @apply bg-gray-700 ;
    }

    .filament-sidebar-nav::-webkit-scrollbar-thumb {
    @apply bg-gray-400 border-2 border-gray-200 rounded-full p-4;

    }
    .dark .filament-sidebar-nav::-webkit-scrollbar-thumb {
    @apply bg-gray-600 border-2 border-gray-700 rounded-full p-4;

    }

    .filament-sidebar-nav::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-500 w-4;

    }
    .dark .filament-sidebar-nav::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-500 w-4;

    }
  }
