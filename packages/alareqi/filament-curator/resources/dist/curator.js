(()=>{var hi=Object.create,Ut=Object.defineProperty,ci=Object.getPrototypeOf,li=Object.prototype.hasOwnProperty,pi=Object.getOwnPropertyNames,fi=Object.getOwnPropertyDescriptor;var di=b=>Ut(b,"__esModule",{value:!0});var ui=(b,E)=>()=>(E||(E={exports:{}},b(E.exports,E)),E.exports);var gi=(b,E,v)=>{if(E&&typeof E=="object"||typeof E=="function")for(let C of pi(E))!li.call(b,C)&&C!=="default"&&Ut(b,C,{get:()=>E[C],enumerable:!(v=fi(E,C))||v.enumerable});return b},mi=b=>gi(di(Ut(b!=null?hi(ci(b)):{},"default",b&&b.__esModule&&"default"in b?{get:()=>b.default,enumerable:!0}:{value:b,enumerable:!0})),b);var Me=ui((jt,Vt)=>{(function(b,E){typeof jt=="object"&&typeof Vt!="undefined"?Vt.exports=E():typeof define=="function"&&define.amd?define(E):(b=typeof globalThis!="undefined"?globalThis:b||self,b.Cropper=E())})(jt,function(){"use strict";function b(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(a,s).enumerable})),i.push.apply(i,e)}return i}function E(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?b(Object(i),!0).forEach(function(e){j(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):b(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function v(a){return v=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(a)}function C(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function N(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,e.key,e)}}function R(a,t,i){return t&&N(a.prototype,t),i&&N(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function j(a,t,i){return t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}function at(a){return vt(a)||wt(a)||Et(a)||Ct()}function vt(a){if(Array.isArray(a))return ct(a)}function wt(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function Et(a,t){if(!!a){if(typeof a=="string")return ct(a,t);var i=Object.prototype.toString.call(a).slice(8,-1);if(i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set")return Array.from(a);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return ct(a,t)}}function ct(a,t){(t==null||t>a.length)&&(t=a.length);for(var i=0,e=new Array(t);i<t;i++)e[i]=a[i];return e}function Ct(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var yt=typeof window!="undefined"&&typeof window.document!="undefined",X=yt?window:{},Tt=yt&&X.document.documentElement?"ontouchstart"in X.document.documentElement:!1,Ot=yt?"PointerEvent"in X:!1,x="cropper",Nt="all",$t="crop",Ft="move",Gt="zoom",Z="e",K="w",rt="s",F="n",lt="ne",pt="nw",ft="se",dt="sw",At="".concat(x,"-crop"),qt="".concat(x,"-disabled"),k="".concat(x,"-hidden"),Qt="".concat(x,"-hide"),Ce="".concat(x,"-invisible"),bt="".concat(x,"-modal"),St="".concat(x,"-move"),ut="".concat(x,"Action"),xt="".concat(x,"Preview"),Rt="crop",Zt="move",Kt="none",Bt="crop",It="cropend",kt="cropmove",Lt="cropstart",Jt="dblclick",Te=Tt?"touchstart":"mousedown",Oe=Tt?"touchmove":"mousemove",Ne=Tt?"touchend touchcancel":"mouseup",te=Ot?"pointerdown":Te,ee=Ot?"pointermove":Oe,ie=Ot?"pointerup pointercancel":Ne,ae="ready",re="resize",ne="wheel",_t="zoom",se="image/jpeg",Ae=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Se=/^data:/,Re=/^data:image\/jpeg;base64,/,Be=/^img|canvas$/i,oe=200,he=100,ce={viewMode:0,dragMode:Rt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:oe,minContainerHeight:he,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Ie='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',ke=Number.isNaN||X.isNaN;function u(a){return typeof a=="number"&&!ke(a)}var le=function(t){return t>0&&t<Infinity};function Ht(a){return typeof a=="undefined"}function J(a){return v(a)==="object"&&a!==null}var Le=Object.prototype.hasOwnProperty;function nt(a){if(!J(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&Le.call(i,"isPrototypeOf")}catch(e){return!1}}function L(a){return typeof a=="function"}var _e=Array.prototype.slice;function pe(a){return Array.from?Array.from(a):_e.call(a)}function T(a,t){return a&&L(t)&&(Array.isArray(a)||u(a.length)?pe(a).forEach(function(i,e){t.call(a,i,e,a)}):J(a)&&Object.keys(a).forEach(function(i){t.call(a,a[i],i,a)})),a}var D=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),s=1;s<i;s++)e[s-1]=arguments[s];return J(t)&&e.length>0&&e.forEach(function(r){J(r)&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t},He=/\.\d*(?:0|9){12}\d*$/;function st(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return He.test(a)?Math.round(a*t)/t:a}var Pe=/^width|height|left|top|marginLeft|marginTop$/;function G(a,t){var i=a.style;T(t,function(e,s){Pe.test(s)&&u(e)&&(e="".concat(e,"px")),i[s]=e})}function Ye(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function B(a,t){if(!!t){if(u(a.length)){T(a,function(e){B(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function W(a,t){if(!!t){if(u(a.length)){T(a,function(i){W(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function ot(a,t,i){if(!!t){if(u(a.length)){T(a,function(e){ot(e,t,i)});return}i?B(a,t):W(a,t)}}var ze=/([a-z\d])([A-Z])/g;function Pt(a){return a.replace(ze,"$1-$2").toLowerCase()}function Yt(a,t){return J(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(Pt(t)))}function gt(a,t,i){J(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(Pt(t)),i)}function Xe(a,t){if(J(a[t]))try{delete a[t]}catch(i){a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch(i){a.dataset[t]=void 0}else a.removeAttribute("data-".concat(Pt(t)))}var fe=/\s\s*/,de=function(){var a=!1;if(yt){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});X.addEventListener("test",i,e),X.removeEventListener("test",i,e)}return a}();function z(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},s=i;t.trim().split(fe).forEach(function(r){if(!de){var n=a.listeners;n&&n[r]&&n[r][i]&&(s=n[r][i],delete n[r][i],Object.keys(n[r]).length===0&&delete n[r],Object.keys(n).length===0&&delete a.listeners)}a.removeEventListener(r,s,e)})}function P(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},s=i;t.trim().split(fe).forEach(function(r){if(e.once&&!de){var n=a.listeners,o=n===void 0?{}:n;s=function(){delete o[r][i],a.removeEventListener(r,s,e);for(var l=arguments.length,h=new Array(l),c=0;c<l;c++)h[c]=arguments[c];i.apply(a,h)},o[r]||(o[r]={}),o[r][i]&&a.removeEventListener(r,o[r][i],e),o[r][i]=s,a.listeners=o}a.addEventListener(r,s,e)})}function ht(a,t,i){var e;return L(Event)&&L(CustomEvent)?e=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent"),e.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(e)}function ue(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var zt=X.location,We=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function ge(a){var t=a.match(We);return t!==null&&(t[1]!==zt.protocol||t[2]!==zt.hostname||t[3]!==zt.port)}function me(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function mt(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,s=a.translateX,r=a.translateY,n=[];u(s)&&s!==0&&n.push("translateX(".concat(s,"px)")),u(r)&&r!==0&&n.push("translateY(".concat(r,"px)")),u(t)&&t!==0&&n.push("rotate(".concat(t,"deg)")),u(i)&&i!==1&&n.push("scaleX(".concat(i,")")),u(e)&&e!==1&&n.push("scaleY(".concat(e,")"));var o=n.length?n.join(" "):"none";return{WebkitTransform:o,msTransform:o,transform:o}}function Ue(a){var t=E({},a),i=0;return T(a,function(e,s){delete t[s],T(t,function(r){var n=Math.abs(e.startX-r.startX),o=Math.abs(e.startY-r.startY),f=Math.abs(e.endX-r.endX),l=Math.abs(e.endY-r.endY),h=Math.sqrt(n*n+o*o),c=Math.sqrt(f*f+l*l),p=(c-h)/h;Math.abs(p)>Math.abs(i)&&(i=p)})}),i}function Dt(a,t){var i=a.pageX,e=a.pageY,s={endX:i,endY:e};return t?s:E({startX:i,startY:e},s)}function je(a){var t=0,i=0,e=0;return T(a,function(s){var r=s.startX,n=s.startY;t+=r,i+=n,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}function q(a){var t=a.aspectRatio,i=a.height,e=a.width,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=le(e),n=le(i);if(r&&n){var o=i*t;s==="contain"&&o>e||s==="cover"&&o<e?i=e/t:e=i*t}else r?i=e/t:n&&(e=i*t);return{width:e,height:i}}function Ve(a){var t=a.width,i=a.height,e=a.degree;if(e=Math.abs(e)%180,e===90)return{width:i,height:t};var s=e%90*Math.PI/180,r=Math.sin(s),n=Math.cos(s),o=t*n+i*r,f=t*r+i*n;return e>90?{width:f,height:o}:{width:o,height:f}}function $e(a,t,i,e){var s=t.aspectRatio,r=t.naturalWidth,n=t.naturalHeight,o=t.rotate,f=o===void 0?0:o,l=t.scaleX,h=l===void 0?1:l,c=t.scaleY,p=c===void 0?1:c,m=i.aspectRatio,g=i.naturalWidth,M=i.naturalHeight,w=e.fillColor,A=w===void 0?"transparent":w,I=e.imageSmoothingEnabled,O=I===void 0?!0:I,V=e.imageSmoothingQuality,H=V===void 0?"low":V,d=e.maxWidth,y=d===void 0?Infinity:d,S=e.maxHeight,Y=S===void 0?Infinity:S,$=e.minWidth,tt=$===void 0?0:$,et=e.minHeight,Q=et===void 0?0:et,U=document.createElement("canvas"),_=U.getContext("2d"),it=q({aspectRatio:m,width:y,height:Y}),Mt=q({aspectRatio:m,width:tt,height:Q},"cover"),Xt=Math.min(it.width,Math.max(Mt.width,g)),Wt=Math.min(it.height,Math.max(Mt.height,M)),ye=q({aspectRatio:s,width:y,height:Y}),be=q({aspectRatio:s,width:tt,height:Q},"cover"),xe=Math.min(ye.width,Math.max(be.width,r)),De=Math.min(ye.height,Math.max(be.height,n)),si=[-xe/2,-De/2,xe,De];return U.width=st(Xt),U.height=st(Wt),_.fillStyle=A,_.fillRect(0,0,Xt,Wt),_.save(),_.translate(Xt/2,Wt/2),_.rotate(f*Math.PI/180),_.scale(h,p),_.imageSmoothingEnabled=O,_.imageSmoothingQuality=H,_.drawImage.apply(_,[a].concat(at(si.map(function(oi){return Math.floor(st(oi))})))),_.restore(),U}var ve=String.fromCharCode;function Fe(a,t,i){var e="";i+=t;for(var s=t;s<i;s+=1)e+=ve(a.getUint8(s));return e}var Ge=/^data:.*,/;function qe(a){var t=a.replace(Ge,""),i=atob(t),e=new ArrayBuffer(i.length),s=new Uint8Array(e);return T(s,function(r,n){s[n]=i.charCodeAt(n)}),e}function Qe(a,t){for(var i=[],e=8192,s=new Uint8Array(a);s.length>0;)i.push(ve.apply(null,pe(s.subarray(0,e)))),s=s.subarray(e);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function Ze(a){var t=new DataView(a),i;try{var e,s,r;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var n=t.byteLength,o=2;o+1<n;){if(t.getUint8(o)===255&&t.getUint8(o+1)===225){s=o;break}o+=1}if(s){var f=s+4,l=s+10;if(Fe(t,f,4)==="Exif"){var h=t.getUint16(l);if(e=h===18761,(e||h===19789)&&t.getUint16(l+2,e)===42){var c=t.getUint32(l+4,e);c>=8&&(r=l+c)}}}if(r){var p=t.getUint16(r,e),m,g;for(g=0;g<p;g+=1)if(m=r+g*12+2,t.getUint16(m,e)===274){m+=8,i=t.getUint16(m,e),t.setUint16(m,1,e);break}}}catch(M){i=1}return i}function Ke(a){var t=0,i=1,e=1;switch(a){case 2:i=-1;break;case 3:t=-180;break;case 4:e=-1;break;case 5:t=90,e=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:i,scaleY:e}}var Je={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,s=this.cropper,r=Number(i.minContainerWidth),n=Number(i.minContainerHeight);B(s,k),W(t,k);var o={width:Math.max(e.offsetWidth,r>=0?r:oe),height:Math.max(e.offsetHeight,n>=0?n:he)};this.containerData=o,G(s,{width:o.width,height:o.height}),B(t,k),W(s,k)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,s=Math.abs(i.rotate)%180==90,r=s?i.naturalHeight:i.naturalWidth,n=s?i.naturalWidth:i.naturalHeight,o=r/n,f=t.width,l=t.height;t.height*o>t.width?e===3?f=t.height*o:l=t.width/o:e===3?l=t.width/o:f=t.height*o;var h={aspectRatio:o,naturalWidth:r,naturalHeight:n,width:f,height:l};this.canvasData=h,this.limited=e===1||e===2,this.limitCanvas(!0,!0),h.width=Math.min(Math.max(h.width,h.minWidth),h.maxWidth),h.height=Math.min(Math.max(h.height,h.minHeight),h.maxHeight),h.left=(t.width-h.width)/2,h.top=(t.height-h.height)/2,h.oldLeft=h.left,h.oldTop=h.top,this.initialCanvasData=D({},h)},limitCanvas:function(t,i){var e=this.options,s=this.containerData,r=this.canvasData,n=this.cropBoxData,o=e.viewMode,f=r.aspectRatio,l=this.cropped&&n;if(t){var h=Number(e.minCanvasWidth)||0,c=Number(e.minCanvasHeight)||0;o>1?(h=Math.max(h,s.width),c=Math.max(c,s.height),o===3&&(c*f>h?h=c*f:c=h/f)):o>0&&(h?h=Math.max(h,l?n.width:0):c?c=Math.max(c,l?n.height:0):l&&(h=n.width,c=n.height,c*f>h?h=c*f:c=h/f));var p=q({aspectRatio:f,width:h,height:c});h=p.width,c=p.height,r.minWidth=h,r.minHeight=c,r.maxWidth=Infinity,r.maxHeight=Infinity}if(i)if(o>(l?0:1)){var m=s.width-r.width,g=s.height-r.height;r.minLeft=Math.min(0,m),r.minTop=Math.min(0,g),r.maxLeft=Math.max(0,m),r.maxTop=Math.max(0,g),l&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,o===2&&(r.width>=s.width&&(r.minLeft=Math.min(0,m),r.maxLeft=Math.max(0,m)),r.height>=s.height&&(r.minTop=Math.min(0,g),r.maxTop=Math.max(0,g))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=s.width,r.maxTop=s.height},renderCanvas:function(t,i){var e=this.canvasData,s=this.imageData;if(i){var r=Ve({width:s.naturalWidth*Math.abs(s.scaleX||1),height:s.naturalHeight*Math.abs(s.scaleY||1),degree:s.rotate||0}),n=r.width,o=r.height,f=e.width*(n/e.naturalWidth),l=e.height*(o/e.naturalHeight);e.left-=(f-e.width)/2,e.top-=(l-e.height)/2,e.width=f,e.height=l,e.aspectRatio=n/o,e.naturalWidth=n,e.naturalHeight=o,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,G(this.canvas,D({width:e.width,height:e.height},mt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,s=e.naturalWidth*(i.width/i.naturalWidth),r=e.naturalHeight*(i.height/i.naturalHeight);D(e,{width:s,height:r,left:(i.width-s)/2,top:(i.height-r)/2}),G(this.image,D({width:e.width,height:e.height},mt(D({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,s=Number(t.autoCropArea)||.8,r={width:i.width,height:i.height};e&&(i.height*e>i.width?r.height=r.width/e:r.width=r.height*e),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*s),r.height=Math.max(r.minHeight,r.height*s),r.left=i.left+(i.width-r.width)/2,r.top=i.top+(i.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=D({},r)},limitCropBox:function(t,i){var e=this.options,s=this.containerData,r=this.canvasData,n=this.cropBoxData,o=this.limited,f=e.aspectRatio;if(t){var l=Number(e.minCropBoxWidth)||0,h=Number(e.minCropBoxHeight)||0,c=o?Math.min(s.width,r.width,r.width+r.left,s.width-r.left):s.width,p=o?Math.min(s.height,r.height,r.height+r.top,s.height-r.top):s.height;l=Math.min(l,s.width),h=Math.min(h,s.height),f&&(l&&h?h*f>l?h=l/f:l=h*f:l?h=l/f:h&&(l=h*f),p*f>c?p=c/f:c=p*f),n.minWidth=Math.min(l,c),n.minHeight=Math.min(h,p),n.maxWidth=c,n.maxHeight=p}i&&(o?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(s.width,r.left+r.width)-n.width,n.maxTop=Math.min(s.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=s.width-n.width,n.maxTop=s.height-n.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&gt(this.face,ut,e.width>=i.width&&e.height>=i.height?Ft:Nt),G(this.cropBox,D({width:e.width,height:e.height},mt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ht(this.element,Bt,this.getData())}},ti={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,s=i?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",n=document.createElement("img");if(i&&(n.crossOrigin=i),n.src=s,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!e){var o=e;typeof e=="string"?o=t.ownerDocument.querySelectorAll(e):e.querySelector&&(o=[e]),this.previews=o,T(o,function(f){var l=document.createElement("img");gt(f,xt,{width:f.offsetWidth,height:f.offsetHeight,html:f.innerHTML}),i&&(l.crossOrigin=i),l.src=s,l.alt=r,l.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',f.innerHTML="",f.appendChild(l)})}},resetPreview:function(){T(this.previews,function(t){var i=Yt(t,xt);G(t,{width:i.width,height:i.height}),t.innerHTML=i.html,Xe(t,xt)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,s=e.width,r=e.height,n=t.width,o=t.height,f=e.left-i.left-t.left,l=e.top-i.top-t.top;!this.cropped||this.disabled||(G(this.viewBoxImage,D({width:n,height:o},mt(D({translateX:-f,translateY:-l},t)))),T(this.previews,function(h){var c=Yt(h,xt),p=c.width,m=c.height,g=p,M=m,w=1;s&&(w=p/s,M=r*w),r&&M>m&&(w=m/r,g=s*w,M=m),G(h,{width:g,height:M}),G(h.getElementsByTagName("img")[0],D({width:n*w,height:o*w},mt(D({translateX:-f*w,translateY:-l*w},t))))}))}},ei={bind:function(){var t=this.element,i=this.options,e=this.cropper;L(i.cropstart)&&P(t,Lt,i.cropstart),L(i.cropmove)&&P(t,kt,i.cropmove),L(i.cropend)&&P(t,It,i.cropend),L(i.crop)&&P(t,Bt,i.crop),L(i.zoom)&&P(t,_t,i.zoom),P(e,te,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&P(e,ne,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&P(e,Jt,this.onDblclick=this.dblclick.bind(this)),P(t.ownerDocument,ee,this.onCropMove=this.cropMove.bind(this)),P(t.ownerDocument,ie,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&P(window,re,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;L(i.cropstart)&&z(t,Lt,i.cropstart),L(i.cropmove)&&z(t,kt,i.cropmove),L(i.cropend)&&z(t,It,i.cropend),L(i.crop)&&z(t,Bt,i.crop),L(i.zoom)&&z(t,_t,i.zoom),z(e,te,this.onCropStart),i.zoomable&&i.zoomOnWheel&&z(e,ne,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&z(e,Jt,this.onDblclick),z(t.ownerDocument,ee,this.onCropMove),z(t.ownerDocument,ie,this.onCropEnd),i.responsive&&z(window,re,this.onResize)}},ii={resize:function(){if(!this.disabled){var t=this.options,i=this.container,e=this.containerData,s=i.offsetWidth/e.width,r=i.offsetHeight/e.height,n=Math.abs(s-1)>Math.abs(r-1)?s:r;if(n!==1){var o,f;t.restore&&(o=this.getCanvasData(),f=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(T(o,function(l,h){o[h]=l*n})),this.setCropBoxData(T(f,function(l,h){f[h]=l*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===Kt||this.setDragMode(Ye(this.dragBox,At)?Zt:Rt)},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,s=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?s=t.deltaY>0?1:-1:t.wheelDelta?s=-t.wheelDelta/120:t.detail&&(s=t.detail>0?1:-1),this.zoom(-s*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(u(i)&&i!==1||u(e)&&e!==0||t.ctrlKey))){var s=this.options,r=this.pointers,n;t.changedTouches?T(t.changedTouches,function(o){r[o.identifier]=Dt(o)}):r[t.pointerId||0]=Dt(t),Object.keys(r).length>1&&s.zoomable&&s.zoomOnTouch?n=Gt:n=Yt(t.target,ut),!!Ae.test(n)&&ht(this.element,Lt,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===$t&&(this.cropping=!0,B(this.dragBox,bt)))}},cropMove:function(t){var i=this.action;if(!(this.disabled||!i)){var e=this.pointers;t.preventDefault(),ht(this.element,kt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?T(t.changedTouches,function(s){D(e[s.identifier]||{},Dt(s,!0))}):D(e[t.pointerId||0]||{},Dt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?T(t.changedTouches,function(s){delete e[s.identifier]}):delete e[t.pointerId||0],!!i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,ot(this.dragBox,bt,this.cropped&&this.options.modal)),ht(this.element,It,{originalEvent:t,action:i}))}}},ai={change:function(t){var i=this.options,e=this.canvasData,s=this.containerData,r=this.cropBoxData,n=this.pointers,o=this.action,f=i.aspectRatio,l=r.left,h=r.top,c=r.width,p=r.height,m=l+c,g=h+p,M=0,w=0,A=s.width,I=s.height,O=!0,V;!f&&t.shiftKey&&(f=c&&p?c/p:1),this.limited&&(M=r.minLeft,w=r.minTop,A=M+Math.min(s.width,e.width,e.left+e.width),I=w+Math.min(s.height,e.height,e.top+e.height));var H=n[Object.keys(n)[0]],d={x:H.endX-H.startX,y:H.endY-H.startY},y=function(Y){switch(Y){case Z:m+d.x>A&&(d.x=A-m);break;case K:l+d.x<M&&(d.x=M-l);break;case F:h+d.y<w&&(d.y=w-h);break;case rt:g+d.y>I&&(d.y=I-g);break}};switch(o){case Nt:l+=d.x,h+=d.y;break;case Z:if(d.x>=0&&(m>=A||f&&(h<=w||g>=I))){O=!1;break}y(Z),c+=d.x,c<0&&(o=K,c=-c,l-=c),f&&(p=c/f,h+=(r.height-p)/2);break;case F:if(d.y<=0&&(h<=w||f&&(l<=M||m>=A))){O=!1;break}y(F),p-=d.y,h+=d.y,p<0&&(o=rt,p=-p,h-=p),f&&(c=p*f,l+=(r.width-c)/2);break;case K:if(d.x<=0&&(l<=M||f&&(h<=w||g>=I))){O=!1;break}y(K),c-=d.x,l+=d.x,c<0&&(o=Z,c=-c,l-=c),f&&(p=c/f,h+=(r.height-p)/2);break;case rt:if(d.y>=0&&(g>=I||f&&(l<=M||m>=A))){O=!1;break}y(rt),p+=d.y,p<0&&(o=F,p=-p,h-=p),f&&(c=p*f,l+=(r.width-c)/2);break;case lt:if(f){if(d.y<=0&&(h<=w||m>=A)){O=!1;break}y(F),p-=d.y,h+=d.y,c=p*f}else y(F),y(Z),d.x>=0?m<A?c+=d.x:d.y<=0&&h<=w&&(O=!1):c+=d.x,d.y<=0?h>w&&(p-=d.y,h+=d.y):(p-=d.y,h+=d.y);c<0&&p<0?(o=dt,p=-p,c=-c,h-=p,l-=c):c<0?(o=pt,c=-c,l-=c):p<0&&(o=ft,p=-p,h-=p);break;case pt:if(f){if(d.y<=0&&(h<=w||l<=M)){O=!1;break}y(F),p-=d.y,h+=d.y,c=p*f,l+=r.width-c}else y(F),y(K),d.x<=0?l>M?(c-=d.x,l+=d.x):d.y<=0&&h<=w&&(O=!1):(c-=d.x,l+=d.x),d.y<=0?h>w&&(p-=d.y,h+=d.y):(p-=d.y,h+=d.y);c<0&&p<0?(o=ft,p=-p,c=-c,h-=p,l-=c):c<0?(o=lt,c=-c,l-=c):p<0&&(o=dt,p=-p,h-=p);break;case dt:if(f){if(d.x<=0&&(l<=M||g>=I)){O=!1;break}y(K),c-=d.x,l+=d.x,p=c/f}else y(rt),y(K),d.x<=0?l>M?(c-=d.x,l+=d.x):d.y>=0&&g>=I&&(O=!1):(c-=d.x,l+=d.x),d.y>=0?g<I&&(p+=d.y):p+=d.y;c<0&&p<0?(o=lt,p=-p,c=-c,h-=p,l-=c):c<0?(o=ft,c=-c,l-=c):p<0&&(o=pt,p=-p,h-=p);break;case ft:if(f){if(d.x>=0&&(m>=A||g>=I)){O=!1;break}y(Z),c+=d.x,p=c/f}else y(rt),y(Z),d.x>=0?m<A?c+=d.x:d.y>=0&&g>=I&&(O=!1):c+=d.x,d.y>=0?g<I&&(p+=d.y):p+=d.y;c<0&&p<0?(o=pt,p=-p,c=-c,h-=p,l-=c):c<0?(o=dt,c=-c,l-=c):p<0&&(o=lt,p=-p,h-=p);break;case Ft:this.move(d.x,d.y),O=!1;break;case Gt:this.zoom(Ue(n),t),O=!1;break;case $t:if(!d.x||!d.y){O=!1;break}V=ue(this.cropper),l=H.startX-V.left,h=H.startY-V.top,c=r.minWidth,p=r.minHeight,d.x>0?o=d.y>0?ft:lt:d.x<0&&(l-=c,o=d.y>0?dt:pt),d.y<0&&(h-=p),this.cropped||(W(this.cropBox,k),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}O&&(r.width=c,r.height=p,r.left=l,r.top=h,this.action=o,this.renderCropBox()),T(n,function(S){S.startX=S.endX,S.startY=S.endY})}},ri={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&B(this.dragBox,bt),W(this.cropBox,k),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=D({},this.initialImageData),this.canvasData=D({},this.initialCanvasData),this.cropBoxData=D({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(D(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),W(this.dragBox,bt),B(this.cropBox,k)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,T(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,W(this.cropper,qt)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,B(this.cropper,qt)),this},destroy:function(){var t=this.element;return t[x]?(t[x]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,s=e.left,r=e.top;return this.moveTo(Ht(t)?t:s+Number(t),Ht(i)?i:r+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,s=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(u(t)&&(e.left=t,s=!0),u(i)&&(e.top=i,s=!0),s&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var s=this.options,r=this.canvasData,n=r.width,o=r.height,f=r.naturalWidth,l=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&s.zoomable){var h=f*t,c=l*t;if(ht(this.element,_t,{ratio:t,oldRatio:n/f,originalEvent:e})===!1)return this;if(e){var p=this.pointers,m=ue(this.cropper),g=p&&Object.keys(p).length?je(p):{pageX:e.pageX,pageY:e.pageY};r.left-=(h-n)*((g.pageX-m.left-r.left)/n),r.top-=(c-o)*((g.pageY-m.top-r.top)/o)}else nt(i)&&u(i.x)&&u(i.y)?(r.left-=(h-n)*((i.x-r.left)/n),r.top-=(c-o)*((i.y-r.top)/o)):(r.left-=(h-n)/2,r.top-=(c-o)/2);r.width=h,r.height=c,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),u(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,u(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(u(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,s=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(u(t)&&(e.scaleX=t,s=!0),u(i)&&(e.scaleY=i,s=!0),s&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.options,e=this.imageData,s=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-s.left,y:r.top-s.top,width:r.width,height:r.height};var o=e.width/e.naturalWidth;if(T(n,function(h,c){n[c]=h/o}),t){var f=Math.round(n.y+n.height),l=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=l-n.x,n.height=f-n.y}}else n={x:0,y:0,width:0,height:0};return i.rotatable&&(n.rotate=e.rotate||0),i.scalable&&(n.scaleX=e.scaleX||1,n.scaleY=e.scaleY||1),n},setData:function(t){var i=this.options,e=this.imageData,s=this.canvasData,r={};if(this.ready&&!this.disabled&&nt(t)){var n=!1;i.rotatable&&u(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,n=!0),i.scalable&&(u(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,n=!0),u(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var o=e.width/e.naturalWidth;u(t.x)&&(r.left=t.x*o+s.left),u(t.y)&&(r.top=t.y*o+s.top),u(t.width)&&(r.width=t.width*o),u(t.height)&&(r.height=t.height*o),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?D({},this.containerData):{}},getImageData:function(){return this.sized?D({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&T(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&nt(t)&&(u(t.left)&&(i.left=t.left),u(t.top)&&(i.top=t.top),u(t.width)?(i.width=t.width,i.height=t.width/e):u(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,s,r;return this.ready&&this.cropped&&!this.disabled&&nt(t)&&(u(t.left)&&(i.left=t.left),u(t.top)&&(i.top=t.top),u(t.width)&&t.width!==i.width&&(s=!0,i.width=t.width),u(t.height)&&t.height!==i.height&&(r=!0,i.height=t.height),e&&(s?i.height=i.width/e:r&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=$e(this.image,this.imageData,i,t);if(!this.cropped)return e;var s=this.getData(),r=s.x,n=s.y,o=s.width,f=s.height,l=e.width/Math.floor(i.naturalWidth);l!==1&&(r*=l,n*=l,o*=l,f*=l);var h=o/f,c=q({aspectRatio:h,width:t.maxWidth||Infinity,height:t.maxHeight||Infinity}),p=q({aspectRatio:h,width:t.minWidth||0,height:t.minHeight||0},"cover"),m=q({aspectRatio:h,width:t.width||(l!==1?e.width:o),height:t.height||(l!==1?e.height:f)}),g=m.width,M=m.height;g=Math.min(c.width,Math.max(p.width,g)),M=Math.min(c.height,Math.max(p.height,M));var w=document.createElement("canvas"),A=w.getContext("2d");w.width=st(g),w.height=st(M),A.fillStyle=t.fillColor||"transparent",A.fillRect(0,0,g,M);var I=t.imageSmoothingEnabled,O=I===void 0?!0:I,V=t.imageSmoothingQuality;A.imageSmoothingEnabled=O,V&&(A.imageSmoothingQuality=V);var H=e.width,d=e.height,y=r,S=n,Y,$,tt,et,Q,U;y<=-o||y>H?(y=0,Y=0,tt=0,Q=0):y<=0?(tt=-y,y=0,Y=Math.min(H,o+y),Q=Y):y<=H&&(tt=0,Y=Math.min(o,H-y),Q=Y),Y<=0||S<=-f||S>d?(S=0,$=0,et=0,U=0):S<=0?(et=-S,S=0,$=Math.min(d,f+S),U=$):S<=d&&(et=0,$=Math.min(f,d-S),U=$);var _=[y,S,Y,$];if(Q>0&&U>0){var it=g/o;_.push(tt*it,et*it,Q*it,U*it)}return A.drawImage.apply(A,[e].concat(at(_.map(function(Mt){return Math.floor(st(Mt))})))),w},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!Ht(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,s=this.face;if(this.ready&&!this.disabled){var r=t===Rt,n=i.movable&&t===Zt;t=r||n?t:Kt,i.dragMode=t,gt(e,ut,t),ot(e,At,r),ot(e,St,n),i.cropBoxMovable||(gt(s,ut,t),ot(s,At,r),ot(s,St,n))}return this}},ni=X.Cropper,we=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(C(this,a),!t||!Be.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=D({},ce,nt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return R(a,[{key:"init",value:function(){var i=this.element,e=i.tagName.toLowerCase(),s;if(!i[x]){if(i[x]=this,e==="img"){if(this.isImg=!0,s=i.getAttribute("src")||"",this.originalUrl=s,!s)return;s=i.src}else e==="canvas"&&window.HTMLCanvasElement&&(s=i.toDataURL());this.load(s)}}},{key:"load",value:function(i){var e=this;if(!!i){this.url=i,this.imageData={};var s=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Se.test(i)){Re.test(i)?this.read(qe(i)):this.clone();return}var n=new XMLHttpRequest,o=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=o,n.onerror=o,n.ontimeout=o,n.onprogress=function(){n.getResponseHeader("content-type")!==se&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&ge(i)&&s.crossOrigin&&(i=me(i)),n.open("GET",i,!0),n.responseType="arraybuffer",n.withCredentials=s.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(i){var e=this.options,s=this.imageData,r=Ze(i),n=0,o=1,f=1;if(r>1){this.url=Qe(i,se);var l=Ke(r);n=l.rotate,o=l.scaleX,f=l.scaleY}e.rotatable&&(s.rotate=n),e.scalable&&(s.scaleX=o,s.scaleY=f),this.clone()}},{key:"clone",value:function(){var i=this.element,e=this.url,s=i.crossOrigin,r=e;this.options.checkCrossOrigin&&ge(e)&&(s||(s="anonymous"),r=me(e)),this.crossOrigin=s,this.crossOriginUrl=r;var n=document.createElement("img");s&&(n.crossOrigin=s),n.src=r||e,n.alt=i.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),B(n,Qt),i.parentNode.insertBefore(n,i.nextSibling)}},{key:"start",value:function(){var i=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var s=X.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(X.navigator.userAgent),r=function(l,h){D(i.imageData,{naturalWidth:l,naturalHeight:h,aspectRatio:l/h}),i.initialImageData=D({},i.imageData),i.sizing=!1,i.sized=!0,i.build()};if(e.naturalWidth&&!s){r(e.naturalWidth,e.naturalHeight);return}var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),s||o.removeChild(n)},n.src=e.src,s||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}},{key:"stop",value:function(){var i=this.image;i.onload=null,i.onerror=null,i.parentNode.removeChild(i),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var i=this.element,e=this.options,s=this.image,r=i.parentNode,n=document.createElement("div");n.innerHTML=Ie;var o=n.querySelector(".".concat(x,"-container")),f=o.querySelector(".".concat(x,"-canvas")),l=o.querySelector(".".concat(x,"-drag-box")),h=o.querySelector(".".concat(x,"-crop-box")),c=h.querySelector(".".concat(x,"-face"));this.container=r,this.cropper=o,this.canvas=f,this.dragBox=l,this.cropBox=h,this.viewBox=o.querySelector(".".concat(x,"-view-box")),this.face=c,f.appendChild(s),B(i,k),r.insertBefore(o,i.nextSibling),W(s,Qt),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,B(h,k),e.guides||B(h.getElementsByClassName("".concat(x,"-dashed")),k),e.center||B(h.getElementsByClassName("".concat(x,"-center")),k),e.background&&B(o,"".concat(x,"-bg")),e.highlight||B(c,Ce),e.cropBoxMovable&&(B(c,St),gt(c,ut,Nt)),e.cropBoxResizable||(B(h.getElementsByClassName("".concat(x,"-line")),k),B(h.getElementsByClassName("".concat(x,"-point")),k)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),L(e.ready)&&P(i,ae,e.ready,{once:!0}),ht(i,ae)}}},{key:"unbuild",value:function(){if(!!this.ready){this.ready=!1,this.unbind(),this.resetPreview();var i=this.cropper.parentNode;i&&i.removeChild(this.cropper),W(this.element,k)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=ni,a}},{key:"setDefaults",value:function(i){D(ce,nt(i)&&i)}}]),a}();return D(we.prototype,Je,ti,ei,ii,ai,ri),we})});var Ee=mi(Me());document.addEventListener("alpine:init",()=>{Alpine.data("curator",({statePath:b,types:E})=>({statePath:b,types:E,selected:null,files:[],nextPageUrl:null,isFetching:!1,init(){this.getFiles("/curator/media"),new IntersectionObserver(([C])=>{C.isIntersecting&&this.loadMoreFiles()},{rootMargin:"0px",threshold:[0]}).observe(this.$refs.loadMore)},getFiles:async function(v="/curator/media",C=null){if(C){let j=v.includes("?")?"&":"?";v=v+j+"media_id="+C}if(this.types.length>0){let j=v.includes("?")?"&":"?";v=v+j+"types[]="+this.types.join("&types[]=")}this.isFetching=!0;let R=await(await fetch(v)).json();this.files=this.files?this.files.concat(R.data):R.data,this.nextPageUrl=R.next_page_url,this.isFetching=!1},loadMoreFiles:async function(){this.nextPageUrl&&(this.isFetching=!0,await this.getFiles(this.nextPageUrl,this.selected?.id),this.isFetching=!1)},searchFiles:async function(v){this.isFetching=!0;var C="/curator/media/search?q="+v.target.value;if(this.types.length>0){let j=C.includes("?")?"&":"?";C=C+j+"types[]="+this.types.join("&types[]=")}let R=await(await fetch(C)).json();this.files=R.data,this.isFetching=!1},addNewFile:function(v=null){v&&(this.files=[...v,...this.files],this.$nextTick(()=>{this.setSelected(v[0].id)}))},removeFile:function(v=null){v&&(this.files=this.files.filter(C=>C.id!==v.id),this.selected=null)},setSelected:function(v=null){!v||this.selected&&this.selected.id===v?this.selected=null:this.selected=this.files.find(C=>C.id===v),this.$wire.setCurrentFile(this.selected)}})),Alpine.data("curation",({statePath:b,fileName:E,fileType:v,presets:C={}})=>({statePath:b,filename:E,filetype:v,cropper:null,presets:C,preset:"custom",flippedHorizontally:!1,flippedVertically:!1,format:"jpg",quality:60,key:null,finalWidth:0,finalHeight:0,cropBoxData:{left:0,top:0,width:0,height:0},data:{left:0,top:0,width:0,height:0,rotate:0,scaleX:1,scaleY:1},init(){this.destroy(),this.$nextTick(()=>{this.cropper=new Ee.default(this.$refs.image,{background:!1})}),this.$watch("preset",N=>{if(N==="custom")this.cropper.reset(),this.key=null;else{let R=this.cropper.getContainerData(),j=this.cropper.getCropBoxData(),at=this.presets.find(Ct=>Ct.key===N),vt=at.width,wt=at.height,Et=Math.round((R.width-vt)/2),ct=Math.round((R.height-wt)/2);this.cropper.setCropBoxData({...j,left:Et,top:ct,width:vt,height:wt}),this.key=at.key}})},destroy(){this.cropper!=null&&(this.cropper.destroy(),this.cropper=null)},setData(){this.finalWidth=this.data.width,this.finalHeight=this.data.height,this.data=this.cropper.getData(!0),this.cropBoxData=this.cropper.getCropBoxData()},updateData(){this.finalWidth=this.data.width,this.finalHeight=this.data.height,this.data=this.cropper.getData(!0),this.cropBoxData=this.cropper.getCropBoxData()},setCropBoxX(N){let R=this.cropper.getCropBoxData();this.cropper.setCropBoxData({...R,left:parseInt(N.target.value)})},setCropBoxY(N){let R=this.cropper.getCropBoxData();this.cropper.setCropBoxData({...R,top:parseInt(N.target.value)})},setCropBoxWidth(N){let R=this.cropper.getCropBoxData();this.cropper.setCropBoxData({...R,width:parseInt(N.target.value)})},setCropBoxHeight(N){let R=this.cropper.getCropBoxData();this.cropper.setCropBoxData({...R,height:parseInt(N.target.value)})},flipHorizontally(){this.cropper.scaleX(this.flippedHorizontally?1:-1),this.flippedHorizontally=!this.flippedHorizontally},flipVertically(){this.cropper.scaleY(this.flippedVertically?1:-1),this.flippedVertically=!this.flippedVertically},saveCuration(){let N=this.cropper.getData(!0);N={...N,containerData:this.cropper.getContainerData(),imageData:this.cropper.getImageData(),canvasData:this.cropper.getCanvasData(),croppedCanvasData:this.cropper.getCroppedCanvas(),format:this.format,quality:this.quality,preset:this.preset,key:this.key??this.preset},this.$wire.saveCuration(N)}}))});})();
/*!
 * Cropper.js v1.5.13
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2022-11-20T05:30:46.114Z
 */
