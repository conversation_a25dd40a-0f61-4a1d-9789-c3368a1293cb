<?php

return [
    'details' => [
        'uploaded_on' => 'Uploaded On',
        'file_type' => 'File Type',
        'file_size' => 'File Size',
        'dimensions' => 'Dimensions',
        'disk' => 'Disk',
        'directory' => 'Directory',
        'file_url' => 'File URL',
        'file' => 'File',
        'ext' => 'Ext',
        'copy_url' => 'Copy URL',
        'url_copied' => 'Copied!',
    ],
    'picker' => [
        'button' => 'Add media',
        'view' => 'View',
        'edit' => 'Edit',
        'download' => 'Download',
        'remove' => 'Remove',
    ],
    'panel' => [
        'button' => 'Add media',
        'heading' => 'Media Picker',
        'search_label' => 'Search',
        'search_placeholder' => 'Search',
        'upload_tab' => 'Upload Media',
        'media_library_tab' => 'Media Library',
        'deselect' => 'Deselect',
        'load_more' => 'Load More',
        'empty' => 'No Files in the library or nothing found for your search.',
        'edit_media' => 'Edit Media',
        'edit_save' => 'Save',
        'edit_delete' => 'Delete',
        'edit_cancel' => 'Cancel',
        'use_selected_image' => 'Insert',
        'add_files' => 'Add Files',
        'view' => 'View',
        'edit' => 'Edit',
        'download' => 'Download',
        'remove' => 'Remove',
    ],
    'curation' => [
        'heading' => 'Curating',
        'adjustments' => 'Adjustments',
        'custom' => 'Custom',
        'key' => 'Key',
        'key_helper' => 'This is the reference used to retrieve your curation for display.',
        'zoom_in' => 'Zoom In',
        'zoom_out' => 'Zoom Out',
        'flip_horizontally' => 'Flip Horizontally',
        'flip_vertically' => 'Flip Vertically',
        'drag_mode' => 'Drag Mode',
        'crop_mode' => 'Crop Mode',
        'reset' => 'Reset',
        'save_curation' => 'Save Curation',
        'height' => 'Height',
        'width' => 'Width',
        'format' => 'Format',
        'quality' => 'Quality',
        'rotate' => 'Rotate',
        'rotate_deg' => 'deg',
    ],
];
