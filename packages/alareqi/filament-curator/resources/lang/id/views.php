<?php

return [
    'details' => [
        'uploaded_on' => 'Diunggah',
        'file_type' => 'Tipe',
        'file_size' => 'Ukuran',
        'dimensions' => 'Dimensi',
        'disk' => 'Disk',
        'directory' => 'Lokasi',
        'file_url' => 'URL Berkas',
        'file' => 'Berkas',
        'ext' => 'Ekstensi',
        'copy_url' => 'Salin URL',
        'url_copied' => 'Disalin!',
    ],
    'picker' => [
        'button' => 'Tambah media',
        'view' => 'Lihat',
        'edit' => 'Ubah',
        'download' => 'Unduh',
        'remove' => 'Hapus',
    ],
    'panel' => [
        'button' => 'Tambah Media',
        'heading' => 'Media Picker',
        'search_label' => 'Cari',
        'search_placeholder' => 'Cari',
        'upload_tab' => 'Unggah Media',
        'media_library_tab' => 'Pustaka Media',
        'deselect' => 'Batalkan pilihan',
        'load_more' => 'Muat lebih banyak',
        'empty' => 'Tidak ada berkas dalam Pustaka atau tidak ada yang sesuai dengan pencarian.',
        'edit_media' => 'Ubah Media',
        'edit_save' => 'Simpan',
        'edit_delete' => 'Hapus',
        'edit_cancel' => 'Batal',
        'use_selected_image' => 'Gunakan',
        'add_files' => 'Tambah Berkas',
        'view' => 'Lihat',
        'edit' => 'Ubah',
        'download' => 'Unduh',
        'remove' => 'Hapus',
    ],
    'curation' => [
        'heading' => 'Mengkurasi',
        'adjustments' => 'Penyesuaian',
        'custom' => 'Kustom',
        'key' => 'Key',
        'key_helper' => 'Kunci (key) yang dapat digunakan untuk memilih kurasi mana yang akan ditampilkan.',
        'zoom_in' => 'Perbesar',
        'zoom_out' => 'Perkecil',
        'flip_horizontally' => 'Balik Horisontal',
        'flip_vertically' => 'Balik Vertikal',
        'drag_mode' => 'Geser',
        'crop_mode' => 'Potong/Crop',
        'reset' => 'Reset',
        'save_curation' => 'Simpan Kurasi',
        'height' => 'Tinggi',
        'width' => 'Lebar',
        'format' => 'Format',
        'quality' => 'Kualitas',
        'rotate' => 'Putar',
        'rotate_deg' => '°',
    ],
];
