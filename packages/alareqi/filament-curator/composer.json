{"name": "awcodes/filament-curator", "description": "A media picker plugin for FilamentPHP.", "keywords": ["awcodes", "laravel", "curator", "filament"], "homepage": "https://github.com/awcodes/filament-curator", "license": "MIT", "authors": [{"name": "awcodes", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0", "filament/filament": "^2.0", "illuminate/contracts": "^9.0|^10.0", "intervention/image": "^2.7", "league/glide-laravel": "^1.0", "spatie/laravel-package-tools": "^1.13.5"}, "require-dev": {"laravel/pint": "^1.0", "nunomaduro/collision": "^6.0|^7.0", "nunomaduro/larastan": "^2.0.1", "orchestra/testbench": "^7.0|^8.0", "pestphp/pest": "^1.21", "pestphp/pest-plugin-laravel": "^1.1", "pestphp/pest-plugin-livewire": "^1.0", "pestphp/pest-plugin-parallel": "^0.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5|^10.0", "spatie/laravel-ray": "^1.26"}, "autoload": {"psr-4": {"Awcodes\\Curator\\": "src", "Awcodes\\Curator\\Database\\Factories\\": "database/factories"}}, "autoload-dev": {"psr-4": {"Awcodes\\Curator\\Tests\\": "tests"}}, "scripts": {"pint": "vendor/bin/pint", "test:pest": "vendor/bin/pest --parallel", "test:phpstan": "vendor/bin/phpstan analyse", "test": ["@test:pest", "@test:phpstan"]}, "config": {"sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["Awcodes\\Curator\\CuratorServiceProvider"], "aliases": {"Curator": "Awcodes\\Curator\\Facades\\Curator"}}}, "minimum-stability": "dev", "prefer-stable": true}