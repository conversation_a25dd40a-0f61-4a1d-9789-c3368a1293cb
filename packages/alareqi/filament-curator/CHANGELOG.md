# Changelog

All notable changes to `curator` will be documented in this file.

## v2.2.8 - 2023-03-11

### What's Changed

- Fix references to filename by @howdu in https://github.com/awcodes/filament-curator/pull/127

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.7...v2.2.8

## v2.2.7 - 2023-03-10

### What's Changed

- Fix: exif orientation and flipping by @awcodes in https://github.com/awcodes/filament-curator/pull/125

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.6...v2.2.7

## v2.2.6 - 2023-03-07

### What's Changed

- Chore: make assets versionalble for cache busting by @awcodes in https://github.com/awcodes/filament-curator/pull/122

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.5...v2.2.6

## v2.2.5 - 2023-03-06

### What's Changed

- Chore: update preview icons for non-image formats by @awcodes in https://github.com/awcodes/filament-curator/pull/120
- Chore: move picker modal to dedicated Alpine component by @awcodes in https://github.com/awcodes/filament-curator/pull/121

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.4...v2.2.5

## v2.2.4 - 2023-03-06

### What's Changed

- Persian Language Added by @shayan100 in https://github.com/awcodes/filament-curator/pull/119

### New Contributors

- @shayan100 made their first contribution in https://github.com/awcodes/filament-curator/pull/119

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.3...v2.2.4

## v2.2.3 - 2023-02-22

### What's Changed

- Fix: Facade resource docblock and readme by @awcodes in https://github.com/awcodes/filament-curator/pull/116

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.2...v2.2.3

## v2.2.2 - 2023-02-22

### What's Changed

- Fix: thumbnail and preview in panel by @awcodes in https://github.com/awcodes/filament-curator/pull/114
- Feat: driver config by @awcodes in https://github.com/awcodes/filament-curator/pull/115

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.1...v2.2.2

## v2.2.1 - 2023-02-17

### What's Changed

- Fix: add nextTick to help with image loading race condition by @awcodes in https://github.com/awcodes/filament-curator/pull/112

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.2.0...v2.2.1

## v2.2.0 - 2023-02-16

### What's Changed

- Laravel 10 compatibility by @howdu in https://github.com/awcodes/filament-curator/pull/109

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.1.3...v2.2.0

## v2.1.3 - 2023-02-15

### What's Changed

- Update Factory by @martin-ro in https://github.com/awcodes/filament-curator/pull/108

### New Contributors

- @martin-ro made their first contribution in https://github.com/awcodes/filament-curator/pull/108

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.1.2...v2.1.3

## v2.1.2 - 2023-02-13

### What's Changed

- Fixes resource navigation naming and list title naming by @archilex in https://github.com/awcodes/filament-curator/pull/107

### New Contributors

- @archilex made their first contribution in https://github.com/awcodes/filament-curator/pull/107

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.1.1...v2.1.2

## v2.1.1 - 2023-02-08

### What's Changed

- Fix/media list previews by @awcodes in https://github.com/awcodes/filament-curator/pull/106

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.1.0...v2.1.1

## v2.1.0 - 2023-02-08

### What's Changed

- Feat: support video preview by @awcodes in https://github.com/awcodes/filament-curator/pull/104
- Feat: implement signed urls by @awcodes in https://github.com/awcodes/filament-curator/pull/105

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.15...v2.1.0

## v2.0.15 - 2023-02-07

### What's Changed

- Feat: add support for navigation sort, group and registration by @awcodes in https://github.com/awcodes/filament-curator/pull/103

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.14...v2.0.15

## v2.0.14 - 2023-02-01

### What's Changed

- Fix: tiptap integrations bugs by @awcodes in https://github.com/awcodes/filament-curator/pull/98
- Fix: file title errors in panel thumbnails by @awcodes in https://github.com/awcodes/filament-curator/pull/99

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.13...v2.0.14

## v2.0.13 - 2023-01-31

### What's Changed

- Small update to Media Resource by @margarizaldi in https://github.com/awcodes/filament-curator/pull/96

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.12...v2.0.13

## v2.0.12 - 2023-01-31

### What's Changed

- Add Indonesian (id) language by @margarizaldi in https://github.com/awcodes/filament-curator/pull/95

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.11...v2.0.12

## v2.0.11 - 2023-01-30

### What's Changed

- Add more translation keys by @margarizaldi in https://github.com/awcodes/filament-curator/pull/93
- Fix: checkered class opacity by @awcodes in https://github.com/awcodes/filament-curator/pull/94

### New Contributors

- @margarizaldi made their first contribution in https://github.com/awcodes/filament-curator/pull/93

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.10...v2.0.11

## v2.0.10 - 2023-01-30

### What's Changed

- Chore(deps): Bump dependabot/fetch-metadata from 1.3.5 to 1.3.6 by @dependabot in https://github.com/awcodes/filament-curator/pull/86
- Fix: update links in issue template by @awcodes in https://github.com/awcodes/filament-curator/pull/89
- Chore: update workflows for 2.x by @awcodes in https://github.com/awcodes/filament-curator/pull/90
- Feature: Make glide configurable by @awcodes in https://github.com/awcodes/filament-curator/pull/91

### New Contributors

- @dependabot made their first contribution in https://github.com/awcodes/filament-curator/pull/86

**Full Changelog**: https://github.com/awcodes/filament-curator/compare/v2.0.9...v2.0.10

## 1.0.0 - 202X-XX-XX

- initial release
