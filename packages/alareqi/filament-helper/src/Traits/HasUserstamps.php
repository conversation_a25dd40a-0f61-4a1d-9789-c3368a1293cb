<?php

namespace Alareqi\FilamentHelper\Traits;

trait HasCreatedBy
{
    public static function bootHasCreatedBy()
    {
        // updating created_by when model is created
        static::creating(function ($model) {
            if (! $model->isDirty('created_by')) {
                $model->created_by = auth()->user()?->id ?? null;
            }
            if (! $model->isDirty('updated_by')) {
                $model->updated_by = auth()->user()?->id ?? null;
            }
        });
    }

    // relation to user who created the model
    public function creator()
    {
        return $this->belongsTo('App\Models\User', 'created_by');
    }
}

trait HasUpdatedBy
{
    public static function bootHasUpdatedBy()
    {
        // updating updated_by when model is updated
        static::updating(function ($model) {
            if (! $model->isDirty('updated_by')) {
                $model->updated_by = auth()->user()?->id ?? null;
            }
        });
    }

    // relation to user who updated the model
    public function updater()
    {
        return $this->belongsTo('App\Models\User', 'updated_by');
    }
}
trait HasDeletedBy
{
    public static function bootHasDeletedBy()
    {
        // updating deleted_by when model is deleted
        static::deleting(function ($model) {
            if (! $model->isDirty('deleted_by')) {
                $model->deleted_by = auth()->user()?->id ?? null;
                $model->save();
            }
        });
    }

    // relation to user who deleted the model
    public function deleter()
    {
        return $this->belongsTo('App\Models\User', 'deleted_by');
    }
}
trait HasCreatedUpdatedBy
{
    use HasCreatedBy;
    use HasUpdatedBy;
}
trait HasUserstamps
{
    use HasCreatedBy;
    use HasUpdatedBy;
    use HasDeletedBy;
}
