<?php

namespace Alareqi\FilamentHelper\Commands;

use Illuminate\Console\Command;

class MakeResourceAPICommand extends Command
{
    use Concerns\CanGenerateResources;
    use Concerns\CanIndentStrings;
    use Concerns\CanManipulateFiles;
    use Concerns\CanValidateInput;

    protected $description = 'Creates a API';

    protected $signature = 'filament:make-resource-api';

    public function handle(): int
    {
        $models = [
            'Announcement',
            'Album',
            'Video',
            'Career',
            'Poll',
            'Awareness',
            'ProductService',
            'CorporateCulture',
            'TrainingEducation',
            'Header',
            'Slider',
        ];
        foreach ($models as $model) {
            $this->call('filament:make-api', [
                'name' => $model,
            ]);
        }
        $this->info('Successfully created ');

        return static::SUCCESS;
    }
}
