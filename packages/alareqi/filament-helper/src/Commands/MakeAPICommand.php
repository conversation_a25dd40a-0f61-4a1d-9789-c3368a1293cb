<?php

namespace Alareqi\FilamentHelper\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class MakeAPICommand extends Command
{
    use Concerns\CanGenerateResources;
    use Concerns\CanIndentStrings;
    use Concerns\CanManipulateFiles;
    use Concerns\CanValidateInput;

    protected $description = 'Creates a API';

    protected $signature = 'filament:make-api {name?} {--F|force}';

    public function handle(): int
    {
        $model = (string) Str::of($this->argument('name') ?? $this->askRequired('Model (e.g. `BlogPost`)', 'name'))
            ->studly()
            ->beforeLast('JsonResource')
            ->trim('/')
            ->trim('\\')
            ->trim(' ')
            ->studly()
            ->replace('/', '\\');

        if (blank($model)) {
            $model = 'JsonResource';
        }

        $jsonResourcePath = app_path('Http/Resources/Api/'.$model.'JsonResource.php');
        $modelClass = $model;
        $modelPlural = Str::of($model)->pluralStudly();
        $modelPluralKebab = Str::of($model)->pluralStudly()->kebab();

        $this->copyStubToApp('JsonResource', $jsonResourcePath, [
            'modelClass' => $modelClass,
        ]);

        $collectionPath = app_path('Http/Resources/Api/Collections/'.$model.'Collection.php');

        $this->copyStubToApp('Collection', $collectionPath, [
            'modelClass' => $modelClass,
        ]);

        $apiControllersPath = app_path('Http/Controllers/Api/'.$model.'Controller.php');

        $this->copyStubToApp('ApiController', $apiControllersPath, [
            'modelClass' => $modelClass,
            'modelPlural' => $modelPlural,
            'modelPluralKebab' => $modelPluralKebab,
        ]);

        // $this->info("Successfully created {$resource}!");

        return static::SUCCESS;
    }
}
