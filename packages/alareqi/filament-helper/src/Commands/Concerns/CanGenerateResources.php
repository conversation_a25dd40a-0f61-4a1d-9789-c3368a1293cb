<?php

namespace Alareqi\FilamentHelper\Commands\Concerns;

use Doctrine\DBAL\Schema\Table;
use Doctrine\DBAL\Types;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Support\Str;
use Throwable;

trait CanGenerateResources
{
    protected function getResourceFormSchema(string $model): string
    {
        $directory =  Str::snake(Str::afterLast($model, "\\"));
        $table = $this->getModelTable($model);

        if (!$table) {
            return $this->indentString('//', 4);
        }

        $components = [];

        foreach ($table->getColumns() as $column) {
            if ($column->getAutoincrement()) {
                continue;
            }
            if (Str::of($column->getName())->is([
                'created_at',
                'created_by',
                'deleted_at',
                'deleted_by',
                'updated_at',
                'updated_by',
                '*_token',
            ])) {
                continue;
            }

            $componentData = [];

            $componentData['type'] = $type = match ($column->getType()::class) {
                Types\BooleanType::class => Forms\Components\Toggle::class,
                Types\DateType::class => Forms\Components\DatePicker::class,
                Types\DateTimeType::class => Forms\Components\DateTimePicker::class,
                Types\TextType::class => Forms\Components\Textarea::class,
                default => Forms\Components\TextInput::class,
            };
            $componentData['type'] = $type = match ($column->getName()) {
                'title' => Forms\Components\TextInput::class,
                'content' => Forms\Components\RichEditor::class,
                'status' => Forms\Components\Toggle::class,
                'image' => Forms\Components\FileUpload::class,
                'images' => Forms\Components\FileUpload::class,
                'file' => Forms\Components\FileUpload::class,
                'files' => Forms\Components\FileUpload::class,
                'youtube_video' => Forms\Components\TextInput::class,
                'youtube_videos' => Forms\Components\Repeater::class,
                'branch_id' => Forms\Components\Select::class,
                'department_id' => Forms\Components\Select::class,
                default => $type,
            };

            if ($type === Forms\Components\TextInput::class) {
                if (Str::of($column->getName())->contains(['email'])) {
                    $componentData['email'] = [];
                }

                if (Str::of($column->getName())->contains(['password'])) {
                    $componentData['password'] = [];
                }

                if (Str::of($column->getName())->contains(['phone', 'tel'])) {
                    $componentData['tel'] = [];
                }
                if (Str::of($column->getName())->contains(['sort'])) {
                    $componentData['default'] = ['\\' . $model . "::max('sort')+1"];
                    $componentData['numeric'] = [];
                }
            }

            if ($type === Forms\Components\Select::class) {
                $componentData['options'] = [$this->getSelectOptions($column->getName())];
                $componentData['required'] = [];
            }
            if ($type === Forms\Components\FileUpload::class) {
                $componentData['previewable'] = [];
                $componentData['downloadable'] = [];
            }

            if ($column->getNotnull()) {
                $componentData['required'] = [];
            }

            if (in_array($column->getName(), ['images', 'files'])) {
                $componentData['multiple'] = [];
            }

            if (in_array($column->getName(), ['youtube_video'])) {
                $componentData['reactive'] = [];
                $componentData['afterStateUpdated'] = ['function (\Closure $set, $state) {' . "\n" .
                    '                            $re = \'/(?im)\\b(?:https?:\\/\\/)?(?:w{3}.)?youtu(?:be)?\\.(?:com|be)\\/(?:(?:\\??v=?i?=?\\/?)|watch\\?vi?=|watch\\?.*?&v=|embed\\/|)([A-Z0-9_-]{11})\\S*(?=\\s|$)/\';' . "\n" .
                    '                            if (preg_match($re, $state, $matches)) {' . "\n" .
                    '                                $set(\'youtube_video\', $matches[1]);' . "\n" .
                    '                            }' . "\n" .
                    '                        }',];
            }

            if (in_array($column->getName(), ['images', 'image'])) {
                $componentData['image'] = [];
                $componentData['directory'] = ["'" . $directory . '/' . 'images' . "'"];
            }
            if (in_array($column->getName(), ['file', 'files'])) {
                $componentData['directory'] = ["'" . $directory . '/' . 'files' . "'"];
            }

            if (in_array($column->getName(), ['name', 'title'])) {
            }

            if (in_array($type, [Forms\Components\TextInput::class, Forms\Components\Textarea::class]) && ($length = $column->getLength())) {
                $componentData['maxLength'] = [$length];
            }
            if (in_array($column->getName(), ['youtube_videos'])) {
                /*
                ->schema([
                        Forms\Components\TextInput::make('youtube_video')
                            ->live()                            ->afterStateUpdated(function (\Closure $set, $state) {
                                $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                                if (preg_match($re, $state, $matches)) {
                                    $set('youtube_video', $matches[1]);
                                }else{
                                    $set('youtube_video', null);
                                }
                            })
                            ->maxLength(255),
                        Forms\Components\Placeholder::make('youtube_video_preview')
                            ->content(function (\Closure $get) {
                                return new HtmlString("<iframe class='w-full aspect-video rounded-lg'  src=\"https://www.youtube.com/embed/{$get('youtube_video')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                            })
                            ->hidden(function (\Closure $get) {
                                return $get('youtube_video') == null;
                            }),
                        ])
                */
                $componentData['schema'] = [
                    "[
                        Forms\Components\TextInput::make('youtube_video')
                            ->live()                            ->afterStateUpdated(function (\Closure \$set, \$state) {
                                \$re = '/(?im)\b(?:https?:\/\/)?(?:w{3}.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                                if (preg_match(\$re, \$state, \$matches)) {
                                    \$set('youtube_video', \$matches[1]);
                                }else{
                                    \$set('youtube_video', null);
                                }
                            })
                            ->maxLength(255),
                        Forms\Components\Placeholder::make('youtube_video_preview')
                            ->content(function (\Closure \$get) {
                                return new HtmlString(\"<iframe class='w-full aspect-video rounded-lg'  src=\\\"https://www.youtube.com/embed/{\$get('youtube_video')}\\\" frameborder=\\\"0\\\" allow=\\\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\\\" allowfullscreen></iframe>\");
                            })
                            ->hidden(function (\Closure \$get) {
                                return \$get('youtube_video') == null;
                            }),
                    ]
                  ",
                ];
            }
            $components[$column->getName()] = $componentData;
            if (in_array($column->getName(), ['youtube_video'])) {
                $components['youtube_video_preview'] = [
                    'type' => Forms\Components\Placeholder::class,
                    'dehydrated' => ['false'],
                    'content' => ['function (\Closure $get) {' . "\n" .
                        '                            return new HtmlString("<iframe width=\'100%\' src=\"https://www.youtube.com/embed/{$get(\'youtube_video\')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");' . "\n" .
                        '                        }',],
                    'hidden' => ['function (\Closure $get) {' . "\n" .
                        '                            return $get(\'youtube_video\') == null;' . "\n" .
                        '                        }',],
                ];
            }
        }

        $output = count($components) ? '' : '//';

        foreach ($components as $componentName => $componentData) {
            // Constructor
            $output .= (string) Str::of($componentData['type'])->after('Filament\\');
            $output .= '::make(\'';
            $output .= $componentName;
            $output .= '\')';
            unset($componentData['type']);

            // Configuration
            foreach ($componentData as $methodName => $parameters) {
                $output .= PHP_EOL;
                $output .= '    ->';
                $output .= $methodName;
                $output .= '(';
                $output .= implode('\', \'', $parameters);
                $output .= ')';
            }

            // Termination
            $output .= ',';

            if (!(array_key_last($components) === $componentName)) {
                $output .= PHP_EOL;
            }
        }

        return $this->indentString($output, 5);
    }

    // getSelectOptions
    protected function getSelectOptions(string $name): string
    {
        $model = "\App\Models\\" . Str::of($name)->before('_id')->ucfirst()->singular();
        $output = 'fn () => ' . $model . "::all()->pluck('name', 'id')";

        return $output;
    }

    protected function getResourceTableColumns(string $model): string
    {
        $table = $this->getModelTable($model);

        if (!$table) {
            return $this->indentString('//', 4);
        }

        $columns = [];

        foreach ($table->getColumns() as $column) {
            // if ($column->getAutoincrement()) {
            //     continue;
            // }

            if (Str::of($column->getName())->endsWith([
                '_token',
            ])) {
                continue;
            }

            if (Str::of($column->getName())->contains([
                'password',
                'content'
            ])) {
                continue;
            }
            if (Str::of($column->getName())->contains([
                'deleted_at',
                'deleted_by',
                'created_by',
                'updated_by',
            ])) {
                continue;
            }

            $columnData = [];

            $columnData['type'] = $type = match ($column->getType()::class) {
                Types\BooleanType::class => Tables\Columns\IconColumn::class,
                default => Tables\Columns\TextColumn::class,
            };
            if ($type === Tables\Columns\IconColumn::class) {
                $columnData['boolean'] = [];
            }
            if ($type === Tables\Columns\TextColumn::class) {
                if ($column->getType()::class === Types\DateType::class) {
                    $columnData['date'] = [];
                }

                if ($column->getType()::class === Types\DateTimeType::class) {
                    $columnData['dateTime'] = ["'Y-m-d h:i A'"];
                }
            }
            if (in_array($column->getName(), ['youtube_video'])) {
                $columnData['type'] = Tables\Columns\ImageColumn::class;
                $columnData['getStateUsing'] = ['function ($record) {' . "\n" .
                    '                            return "https://img.youtube.com/vi/{$record->youtube_video}/0.jpg";' . "\n" .
                    '                        }',];
            }
            if (in_array($column->getName(), ['image'])) {
                $columnData['type'] = Tables\Columns\ImageColumn::class;
            }
            if (in_array($column->getName(), ['content'])) {
                $columnData['limit'] = [];
            }
            if (in_array($column->getName(), ['title', 'content', 'name', 'description',])) {
                $columnData['searchable'] = [];
            }

            if (in_array($column->getName(), ['youtube_videos', 'files', 'images'])) {
                $columnData['type'] = Tables\Columns\TextColumn::class;
                $columnData['getStateUsing'] = ['function ($record) {' . "\n" .
                    '                            return $record->' . $column->getName() . ' == null? 0 :  count($record->' . $column->getName() . ');' . "\n" .
                    '                        }',];
                $columns[$column->getName() . '_count'] = $columnData;
            } else {
                if (in_array($column->getName(), ['updated_by', 'deleted_by'])) {
                    $columns[Str::of($column->getName())->before('d_by') . 'r.name'] = $columnData;
                } elseif (in_array($column->getName(), ['created_by'])) {
                    $columns['creator.name'] = $columnData;
                } elseif (Str::of($column->getName())->endsWith('_id')) {
                    $columns[Str::of($column->getName())->before('_id') . '.name'] = $columnData;
                } else {
                    $columns[$column->getName()] = $columnData;
                }
            }
        }

        $output = count($columns) ? '' : '//';

        foreach ($columns as $columnName => $columnData) {
            // Constructor
            $output .= (string) Str::of($columnData['type'])->after('Filament\\');
            $output .= '::make(\'';
            $output .= $columnName;
            $output .= '\')';
            // $output .= PHP_EOL;
            unset($columnData['type']);

            // Configuration
            foreach ($columnData as $methodName => $parameters) {
                $output .= PHP_EOL;
                $output .= '    ->';
                $output .= $methodName;
                $output .= '(';
                $output .= implode('\', \'', $parameters);
                $output .= ')';
            }

            // Termination
            $output .= ',';

            if (!(array_key_last($columns) === $columnName)) {
                $output .= PHP_EOL;
            }
        }

        return $this->indentString($output, 4);
    }

    protected function getModelTable(string $model): ?Table
    {
        if ((!class_exists($model)) && (!class_exists($model = "App\\Models\\{$model}"))) {
            return null;
        }

        $model = app($model);

        try {
            return $model
                ->getConnection()
                ->getDoctrineSchemaManager()
                ->listTableDetails($model->getTable());
        } catch (Throwable $exception) {
            return null;
        }
    }
}
