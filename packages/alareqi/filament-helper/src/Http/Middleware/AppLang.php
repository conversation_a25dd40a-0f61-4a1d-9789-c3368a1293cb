<?php

namespace Alareqi\FilamentHelper\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AppLang
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $header = $request->header('lang') ?? 'ar';
        app()->setLocale($header);

        return $next($request);
    }
}
