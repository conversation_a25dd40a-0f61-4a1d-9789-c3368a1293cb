<?php

namespace <PERSON>areqi\FilamentHelper;

use Alareqi\FilamentHelper\Commands\MakeAPICommand;
use Alareqi\FilamentHelper\Commands\MakeResourceAPICommand;
use Alareqi\FilamentHelper\Http\Middleware\ApiKey;
use Alareqi\FilamentHelper\Http\Middleware\AppLang;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;
use Filament\PluginServiceProvider;
use function Filament\Support\get_model_label;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Translation\FileLoader;
use Spatie\LaravelPackageTools\Package;

class FilamentHelperServiceProvider extends PluginServiceProvider
{
    public function configurePackage(Package $package): void
    {
        /*
         * This class is a Package Service Provider
         *
         * More info: https://github.com/spatie/laravel-package-tools
         */
        $package
            ->name('filament-helper')
            ->hasCommands([
                \Alareqi\FilamentHelper\Commands\MakeResourceCommand::class,
                MakeAPICommand::class,
                MakeResourceAPICommand::class,
            ]);
    }

    public function packageBooted(): void
    {
    }

    public function bootingPackage()
    {
        app('router')->aliasMiddleware('apiKey', ApiKey::class);
        app('router')->aliasMiddleware('lang', AppLang::class);
        Filament::serving(
            function () {

                // $this->addMacroToFormComponents();
                // $this->addMacroToTableComponents();
            }
        );
        $this->addUserstampsMacroBlueprint();
    }

    public function registeringPackage()
    {
        $this->registerTranslation();
    }

    public static function addMacroToTableComponents()
    {
        // TextColumn
        \Filament\Tables\Columns\Column::macro('columnTranslatedLabel', function () {
            $this->label(function ($livewire, $column) {
                if (
                    $column->getName() == 'id'
                ) {
                    return '#';
                }
                if (
                    $column->getName() == 'created_at'
                ) {
                    return __('common.fields.created_at.label');
                }
                if (
                    $column->getName() == 'updated_at'
                ) {
                    return __('common.fields.updated_at.label');
                }
                if (
                    $column->getName() == 'deleted_at'
                ) {
                    return __('common.fields.deleted_at.label');
                }
                if (
                    $column->getName() == 'creater'
                ) {
                    return __('common.fields.creater.label');
                }
                if (
                    $column->getName() == 'updater'
                ) {
                    return __('common.fields.updater.label');
                }
                $suffix = '';
                if (in_array($column->getName(), (app($livewire->getModel()))->getTranslatableAttributes())) {
                    $suffix = ' ['.__('common.options.languages.'.$livewire->activeLocale).']';
                }

                return __(str_replace(' ', '_', str_replace(' ', '_', get_model_label($livewire->getModel()))).'.fields.'.str_replace('.', '_', $column->getName()).'.label').$suffix;
            });

            return $this;
        });
    }

    public static function addMacroToFormComponents()
    {
        \Filament\Forms\Components\Component::macro('translatedLabel', function () {
            $this->label(function ($livewire, $component) {
                if (
                    $component->getName() == 'id'
                ) {
                    return '#';
                }
                if (
                    $component->getName() == 'created_at'
                ) {
                    return __('common.fields.created_at.label');
                }
                if (
                    $component->getName() == 'updated_at'
                ) {
                    return __('common.fields.updated_at.label');
                }

                if (
                    $component->getName() == 'creater'
                ) {
                    return __('common.fields.creater.label');
                }
                if (
                    $component->getName() == 'updater'
                ) {
                    return __('common.fields.updater.label');
                }
                $suffix = '';
                if (in_array($component->getName(), (app($livewire->getModel()))->getTranslatableAttributes())) {
                    $suffix = ' ['.__('common.options.languages.'.$livewire->activeFormLocale).']';
                }

                return __(str_replace(' ', '_', get_model_label($livewire->getModel())).'.fields.'.str_replace('.', '_', $component->getName()).'.label').$suffix;
            });

            return $this;
        });
        \Filament\Forms\Components\Component::macro('translatedPlaceholder', function () {
            $this->placeholder(function ($livewire, $component) {
                return __(str_replace(' ', '_', get_model_label($livewire->getModel())).'.fields.'.str_replace('.', '_', $component->getName()).'.placeholder');
            });

            return $this;
        });
        \Filament\Forms\Components\Component::macro('translatable', function () {
            $this->hint(function () {
                return __('common.translatable_hint');
            });
            $this->hintIcon(function () {
                return __('heroicon-s-translate');
            });

            return $this;
        });
    }

    public static function addUserstampsMacroBlueprint()
    {
        Blueprint::macro('userstamps', function () {
            $this->foreignId('created_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
            $this->foreignId('updated_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
            $this->foreignId('deleted_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
        });
        Blueprint::macro('createdBy', function () {
            $this->foreignId('created_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
        });
        Blueprint::macro('updatedBy', function () {
            $this->foreignId('updated_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
        });
        Blueprint::macro('deletedBy', function () {
            $this->foreignId('deleted_by')->nullable()
                ->constrained('users')
                ->nullOnDelete();
        });
    }

    private function registerTranslation()
    {
        $this->app->extend('translator', function ($translator, $app) {
            $trans = new TranslatorWithFallback(new FileLoader($app['files'], $app['path.lang']), $app['config']['app.locale']);

            $trans->setFallback($app['config']['app.fallback_locale']);

            return $trans;
        });
    }
}
