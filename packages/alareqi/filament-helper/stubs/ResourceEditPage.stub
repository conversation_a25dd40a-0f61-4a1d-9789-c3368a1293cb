<?php

namespace {{ namespace }};

use App\Filament\Resources\{{ resource }};
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use function Filament\Support\get_model_label;

class {{ resourcePageClass }} extends EditRecord
{

    protected static string $resource = {{ resourceClass }}::class;

    protected function getActions(): array
    {
        return [
{{ actions }}
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationMessage(): ?string
    {
        return __(str_replace(' ', '_', get_model_label($this->getModel())) . '.messages.updated');
    }
}
