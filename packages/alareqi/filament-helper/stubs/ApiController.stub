<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Collections\{{ modelClass }}Collection;
use App\Models\{{ modelClass }};

class {{ modelClass }}Controller extends Controller
{
    /**
     * {{ modelPluralKebab }}
     *
     * Get all {{ modelPlural }}
     *
     * @unauthenticated
     *
     * @queryParam per_page int per page default 15 Example: 15
     * @queryParam page int page default 1 Example: 1
     *
     * @response scenario=success
     * {
     *    "total": 1,
     *    "current_page": 1,
     *    "last_page": 1,
     *    "per_page": 15,
     *    "data":[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "content": "",
     *              "image": "",
     *              "images": [],
     *              "youtube_videos": [],
     *              "created_at": "",
     *            }
     *      ]
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $data = {{ modelClass }}::orderBy('id','desc')->fastPaginate($inputs['per_page']??15,"*",'page',$inputs['page']??1);
        return response()->json(new {{ modelClass }}Collection($data));
    }
}
