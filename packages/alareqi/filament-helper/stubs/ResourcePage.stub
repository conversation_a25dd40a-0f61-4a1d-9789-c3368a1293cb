<?php

namespace {{ namespace }};

use App\Filament\Resources\{{ resource }};
use Filament\Actions;
use {{ baseResourcePage }};
use function Filament\Support\get_model_label;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class {{ resourcePageClass }} extends {{ baseResourcePageClass }}
{
    protected static string $resource = {{ resourceClass }}::class;
    protected function getActions(): array
    {
        return [
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationMessage(): ?string
    {
        return __(str_replace(' ', '_', get_model_label($this->getModel())) . '.messages.created');
    }
}
