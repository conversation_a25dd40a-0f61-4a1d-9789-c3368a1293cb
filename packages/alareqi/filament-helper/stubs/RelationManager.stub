<?php

namespace {{ namespace }};

use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class {{ managerClass }} extends RelationManager
{
    protected static string $relationship = '{{ relationship }}';

    protected static ?string $recordTitleAttribute = '{{ recordTitleAttribute }}';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('{{ recordTitleAttribute }}')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('{{ recordTitleAttribute }}'),
            ])
            ->filters([
{{ tableFilters }}
            ])
            ->headerActions([
{{ tableHeaderActions }}
            ])
            ->actions([
{{ tableActions }}
            ])
            ->bulkActions([
{{ tableBulkActions }}
            ]);
    }{{ eloquentQuery }}
}
