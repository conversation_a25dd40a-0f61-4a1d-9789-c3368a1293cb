<?php

namespace {{ namespace }};

use App\Filament\Resources\{{ resource }}\Pages;
use App\Filament\Resources\{{ resource }}\RelationManagers;
use App\Models\{{ model }};
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasLabelTranslation;
use Illuminate\Support\HtmlString;

class {{ resourceClass }} extends Resource
{

    use HasLabelTranslation;

    protected static ?string $model = {{ modelClass }}::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    protected static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.cms");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
{{ formSchema }}
                    ])->columnSpan(
                        [
                            "sm" => 2
                        ]
                    ),
                Forms\Components\Section::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->content(fn ($record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->content(fn ($record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('deleted_at')
                        ->visible(fn ($record): bool => $record?->deleted_at? true : false)
                        ->content(fn ($record): string => $record ? $record->deleted_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
{{ tableColumns }}
            ])
            ->filters([
{{ tableFilters }}
            ])
            ->actions([
{{ tableActions }}
            ])
            ->bulkActions([
{{ tableBulkActions }}
            ]);
    }
{{ relations }}
    public static function getPages(): array
    {
        return [
{{ pages }}
        ];
    }{{ eloquentQuery }}

    protected function shouldPersistTableFiltersInSession(): bool
    {
        return false;
    }

    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        'restore',
        'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
    ];

}
