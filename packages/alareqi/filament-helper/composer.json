{"name": "alareqi/filament-helper", "description": "my helping code for filament", "keywords": ["<PERSON><PERSON><PERSON>", "laravel", "filament-helper"], "homepage": "https://github.com/aymanalareqi/filament-helper", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "ayman.a.alare<PERSON>@gmail.com", "role": "Developer"}], "require": {"php": "^8.1"}, "require-dev": {}, "autoload": {"psr-4": {"Alareqi\\FilamentHelper\\": "src", "Alareqi\\FilamentHelper\\Database\\Factories\\": "database/factories"}}, "autoload-dev": {"psr-4": {"Alareqi\\FilamentHelper\\Tests\\": "tests"}}, "scripts": {}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["Alareqi\\FilamentHelper\\FilamentHelperServiceProvider"], "aliases": {"FilamentHelper": "Alareqi\\FilamentHelper\\Facades\\FilamentHelper"}}}, "minimum-stability": "dev", "prefer-stable": true}