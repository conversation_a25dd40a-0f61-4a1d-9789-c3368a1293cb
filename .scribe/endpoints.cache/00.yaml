## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: Auth
description: |-

  APIs for employee authentication
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/login
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: Login
      description: |-
        login employee
        this is the second step in the login process
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'employee mobile number. يجب أن يكون value عنوان بريد إلكتروني صحيح البُنية.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      password:
        name: password
        description: password.
        required: true
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      device_type:
        name: device_type
        description: 'device type. [1: android, 2: ios].'
        required: true
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      device_id:
        name: device_id
        description: 'device id.'
        required: true
        example: '123456789'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      fcm_token:
        name: fcm_token
        description: 'one signal player id as XXXXXX-XXXX-XXX-XXXX-XXXXXXXXX.'
        required: true
        example: effe8f7c-8c8c-4e8c-8c8c-8c8c8c8c8c8c
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      password: '2500'
      device_type: '1'
      device_id: '123456789'
      fcm_token: effe8f7c-8c8c-4e8c-8c8c-8c8c8c8c8c8c
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "message": "تم تسجيل الدخول بنجاح",
           "data": {
           "id": 1,
           "name": "موظف تجريبي",
           "code": "1",
           "date_of_birth": null,
           "marital_status": 1,
           "gender": 1,
           "qualification": "Bachlor",
           "specialization": "Computer Science",
           "mobile_number": "777777777",
           "dial_code": "+967",
           "email": "<EMAIL>",
           "branch_id": 1,
           "branch_name": "الزبيري",
           "department_id": null,
           "department_name": null,
           "designation": "مدير",
           "image": "profiles/fsfagc-2022-10-15-05-50.png",
           "hobbies": "",
           "interests": "",
           "occasions": "",
           "other": "",
           "is_manager": 1
          },
          "token":"1|bwaIJskKEUxfJ7aUmPDa8NqfkEpwxlYSlFHaeuPn"
          }
        headers: []
        description: success
        custom: []
      -
        status: 400
        content: |-
          {
          "message": "عذرأ , كود التحقق الذي أدخلته غير صحيح",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 403
        content: |-
          {
          "message": "عذراً, حسابك غير مفعل",
          }
        headers: []
        description: 'employee account not active'
        custom: []
      -
        status: 404
        content: |-
          {
           "message": "عذراً, البيانات الذي أدخلتها غير صحيحة",
          }
        headers: []
        description: 'not found'
        custom: []
      -
        status: 410
        content: |-
          {
          "message": "عذرأ , انتهى الوقت المسموح لإدخال كود التحقق",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 422
        content: |-
          {
          "message": "خطأ في البيانات",
          "errors": {
             },
          }
        headers: []
        description: 'validation error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/register
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: Register
      description: |-
        Register
        this is the second step in the login process
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: first_name.
        required: true
        example: Ayman
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      middle_name:
        name: middle_name
        description: middle_name.
        required: true
        example: Ameen
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      last_name:
        name: last_name
        description: 'last name.'
        required: true
        example: Shaef
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      grandfather_name:
        name: grandfather_name
        description: grandfather_name.
        required: true
        example: Naji
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      branch_id:
        name: branch_id
        description: branch_id.
        required: true
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      gender:
        name: gender
        description: gender.
        required: true
        example: '1'
        type: string
        enumValues:
          - '1'
          - '2'
        exampleWasSpecified: false
        custom: []
      mobile:
        name: mobile
        description: mobile.
        required: true
        example: '777582069'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      email:
        name: email
        description: 'email. يجب أن يكون value عنوان بريد إلكتروني صحيح البُنية.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      password:
        name: password
        description: password.
        required: true
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      password_confirmation:
        name: password_confirmation
        description: password_confirmation.
        required: true
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      birth_date:
        name: birth_date
        description: birth_date.
        required: true
        example: '1996-12-12'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      birth_place:
        name: birth_place
        description: birth_place.
        required: true
        example: 'Sanaa City - Yemen'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      country_id:
        name: country_id
        description: country_id.
        required: false
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      city_id:
        name: city_id
        description: city_id.
        required: false
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      address:
        name: address
        description: address.
        required: false
        example: 'Sanaa - Yemen'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      first_name: Ayman
      middle_name: Ameen
      last_name: Shaef
      grandfather_name: Naji
      branch_id: '1'
      gender: '1'
      mobile: '777582069'
      email: <EMAIL>
      password: '2500'
      password_confirmation: '2500'
      birth_date: '1996-12-12'
      birth_place: 'Sanaa City - Yemen'
      country_id: '1'
      city_id: '1'
      address: 'Sanaa - Yemen'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "message": "تم التسجيل بنجاح",
           "data": {
           "id": 1,
           "name": "موظف تجريبي",
           "code": "1",
           "date_of_birth": null,
           "marital_status": 1,
           "gender": 1,
           "qualification": "Bachlor",
           "specialization": "Computer Science",
           "mobile_number": "777777777",
           "dial_code": "+967",
           "email": "<EMAIL>",
           "branch_id": 1,
           "branch_name": "الزبيري",
           "department_id": null,
           "department_name": null,
           "designation": "مدير",
           "image": "profiles/fsfagc-2022-10-15-05-50.png",
           "hobbies": "",
           "interests": "",
           "occasions": "",
           "other": "",
           "is_manager": 1
          },
          "token":"1|bwaIJskKEUxfJ7aUmPDa8NqfkEpwxlYSlFHaeuPn"
          }
        headers: []
        description: success
        custom: []
      -
        status: 400
        content: |-
          {
          "message": "عذرأ , كود التحقق الذي أدخلته غير صحيح",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 403
        content: |-
          {
          "message": "عذراً, حسابك غير مفعل",
          }
        headers: []
        description: 'employee account not active'
        custom: []
      -
        status: 404
        content: |-
          {
           "message": "عذراً, البيانات الذي أدخلتها غير صحيحة",
          }
        headers: []
        description: 'not found'
        custom: []
      -
        status: 410
        content: |-
          {
          "message": "عذرأ , انتهى الوقت المسموح لإدخال كود التحقق",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 422
        content: |-
          {
          "message": "خطأ في البيانات",
          "errors": {
             },
          }
        headers: []
        description: 'validation error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/forget-password
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: 'Forget password'
      description: |-
        Forget password
        this is the second step in the login process
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'employee mobile number. يجب أن يكون value عنوان بريد إلكتروني صحيح البُنية.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "message": "تم ارسال رمز إعادة التعيين الى بريدك الالكتروني",
          }
        headers: []
        description: success
        custom: []
      -
        status: 400
        content: |-
          {
          "message": "عذرأ , كود التحقق الذي أدخلته غير صحيح",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 403
        content: |-
          {
          "message": "عذراً, حسابك غير مفعل",
          }
        headers: []
        description: 'employee account not active'
        custom: []
      -
        status: 404
        content: |-
          {
           "message": "عذراً, البيانات الذي أدخلتها غير صحيحة",
          }
        headers: []
        description: 'not found'
        custom: []
      -
        status: 410
        content: |-
          {
          "message": "عذرأ , انتهى الوقت المسموح لإدخال كود التحقق",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 422
        content: |-
          {
          "message": "خطأ في البيانات",
          "errors": {
             },
          }
        headers: []
        description: 'validation error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/reset-password
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password'
      description: |-
        Reset password
        this is the second step in the login process
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'employee mobile number. يجب أن يكون value عنوان بريد إلكتروني صحيح البُنية.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      reset_password_code:
        name: reset_password_code
        description: 'reset password code number.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      new_password:
        name: new_password
        description: 'new password.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      new_password_confirmation:
        name: new_password_confirmation
        description: 'confirm password. The value and <code>new_password</code> must match.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      reset_password_code: '123456'
      new_password: '123456'
      new_password_confirmation: '123456'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "message": "تم ارسال رمز إعادة التعيين الى بريدك الالكتروني",
          }
        headers: []
        description: success
        custom: []
      -
        status: 400
        content: |-
          {
          "message": "عذرأ , كود التحقق الذي أدخلته غير صحيح",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 403
        content: |-
          {
          "message": "عذراً, حسابك غير مفعل",
          }
        headers: []
        description: 'employee account not active'
        custom: []
      -
        status: 404
        content: |-
          {
           "message": "عذراً, البيانات الذي أدخلتها غير صحيحة",
          }
        headers: []
        description: 'not found'
        custom: []
      -
        status: 410
        content: |-
          {
          "message": "عذرأ , انتهى الوقت المسموح لإدخال كود التحقق",
          }
        headers: []
        description: 'verification code expired'
        custom: []
      -
        status: 422
        content: |-
          {
          "message": "خطأ في البيانات",
          "errors": {
             },
          }
        headers: []
        description: 'validation error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/device/register
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 400
        content: '{"message":"family_member.messages.validation_error","errors":{"device_id":["\u062d\u0642\u0644 device id \u0645\u0637\u0644\u0648\u0628."],"device_type":["\u062d\u0642\u0644 device type \u0645\u0637\u0644\u0648\u0628."],"fcm_token":["\u062d\u0642\u0644 fcm token \u0645\u0637\u0644\u0648\u0628."]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '59'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/profile
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/logout
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: Logout
      description: 'logout family_member'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
          "message": "تم تسجيل الخروج بنجاح"
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
          "message": "unauthorized",
          }
        headers: []
        description: unauthorized
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/change-password
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: ''
        required: true
        example: corporis
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      new_password:
        name: new_password
        description: ''
        required: true
        example: minima
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      current_password: corporis
      new_password: minima
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/update-profile
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
      Content-Type: multipart/form-data
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: first_name.
        required: true
        example: Ayman
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      middle_name:
        name: middle_name
        description: middle_name.
        required: true
        example: Ameen
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      last_name:
        name: last_name
        description: 'last name.'
        required: true
        example: Shaef
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      grandfather_name:
        name: grandfather_name
        description: grandfather_name.
        required: true
        example: Naji
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      branch_id:
        name: branch_id
        description: branch_id.
        required: true
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      gender:
        name: gender
        description: 'gender. 1 for male, 2 for female.'
        required: true
        example: '1'
        type: string
        enumValues:
          - '1'
          - '2'
        exampleWasSpecified: false
        custom: []
      mobile:
        name: mobile
        description: mobile.
        required: true
        example: '777582069'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      email:
        name: email
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      image:
        name: image
        description: 'image file. يجب أن يكون حقل value صورةً.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        custom: []
      birth_date:
        name: birth_date
        description: birth_date.
        required: true
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      birth_place:
        name: birth_place
        description: birth_place.
        required: true
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      country_id:
        name: country_id
        description: country_id.
        required: false
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      city_id:
        name: city_id
        description: city_id.
        required: false
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      address:
        name: address
        description: address.
        required: false
        example: '2500'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      first_name: Ayman
      middle_name: Ameen
      last_name: Shaef
      grandfather_name: Naji
      branch_id: '1'
      gender: '1'
      mobile: '777582069'
      birth_date: '2500'
      birth_place: '2500'
      country_id: '2500'
      city_id: '2500'
      address: '2500'
    fileParameters:
      image: null
    responses:
      -
        status: 422
        content: '{"message":"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a","errors":{"email":["\u062d\u0642\u0644 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0627\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0645\u0637\u0644\u0648\u0628."]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '59'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/update-profile-image
    metadata:
      groupName: Auth
      groupDescription: |-

        APIs for employee authentication
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      image:
        name: image
        description: 'يجب أن يكون حقل value صورةً.'
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters: []
    fileParameters:
      image: null
    responses:
      -
        status: 200
        content: '{"message":"family_member.messages.image_updated","data":{"id":1,"first_name":"\u0627\u064a\u0645\u0646","middle_name":"\u0627\u0645\u064a\u0646","last_name":"\u0634\u0627\u0626\u0641","grandfather_name":"\u0646\u0627\u062c\u064a","branch":"\u0645\u062d\u0645\u062f","branch_id":1,"gender":1,"mobile":"+967777582069","email":"<EMAIL>","image":"https:\/\/almashalfamily.test\/images\/thumb\/\/private\/var\/folders\/90\/86_l6r9x30l1ttj73dblbzhc0000gn\/T\/php8KGPWz","birth_date":"1996-05-25","birth_place":null,"country":null,"country_id":null,"city":null,"city_id":null,"address":null}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '60'
          x-ratelimit-remaining: '58'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
