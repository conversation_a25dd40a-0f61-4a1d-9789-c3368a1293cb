## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/branches
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Family Branches'
      description: 'Get all Family Branches'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "name": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/social-media-links
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'social media links'
      description: 'Get all social media links'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "name": "",
                       "icon": "",
                       "url": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/countries
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: Countries
      description: 'Get all Countries'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "name": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/cities/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: Cities
      description: 'Get all Cities'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the country.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "name": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/news
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all News'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/news/search
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'search in News'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'search key default'
        required: false
        example: '"key"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      search: '"key"'
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/news/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get News by id'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the news.'
        required: true
        example: excepturi
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: excepturi
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

                       "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "created_at": "",

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/contact
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: contact
      description: contact
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'name of sender.'
        required: true
        example: 'ايمن العريقي'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      email:
        name: email
        description: 'email of sender.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      mobile:
        name: mobile
        description: 'mobile of sender.'
        required: true
        example: '12365465'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      subject:
        name: subject
        description: 'subject of message.'
        required: true
        example: 'موضوع جديد'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      message:
        name: message
        description: message.
        required: true
        example: رسالة
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      name: 'ايمن العريقي'
      email: <EMAIL>
      mobile: '12365465'
      subject: 'موضوع جديد'
      message: رسالة
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "message": "تم إضافة الرسالة بنجاح",
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/albums
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all Albums'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/album/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Album by id'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the album.'
        required: true
        example: expedita
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: expedita
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": ""

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/albums/search
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'search in albums'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'search key default'
        required: false
        example: '"key"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      search: '"key"'
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/occasions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all Occasions'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      type:
        name: type
        description: 'occasion type 0 : past ,1 : future default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
      type: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "date": "",
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/occasions/search
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all Occasions'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'search key default test'
        required: false
        example: test
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      type:
        name: type
        description: 'occasion type 0 : past ,1 : future default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      search: test
      per_page: 15
      page: 1
      type: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "date": "",
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/occasions/count
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: occasions
      description: 'Get Occasions count'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "future_count" : 10,
              "past_count" : 20,
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/occasion/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: occasions
      description: 'Get Occasion by id'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the occasion.'
        required: true
        example: voluptatem
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: voluptatem
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

                      "id": 1,
                       "title": "",
                       "content": "",
                       "image": "",
                       "images": [],
                       "youtube_videos": [],
                       "date": "",
                       "created_at": "",

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/excellence-awards
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all Excellence Award'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/excellence-awards/search
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'search in Excellence Award'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'search key default test'
        required: false
        example: test
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      search: test
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "content": "",
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/excellence-award/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'excellence awards'
      description: 'Get ExcellenceAward by id'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the excellence award.'
        required: true
        example: reprehenderit
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: reprehenderit
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

                      "id": 1,
                       "title": "",
                       "content": "",
                       "created_at": "",

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/committees
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: committees
      description: 'Get all Committees'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "name": "",
                       "content": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/family-information/tree-image
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'tree image'
      description: 'Get tree image'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                       "tree_image": "",
                     }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tree/nodes
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Family Tree Nodes'
      description: 'Get all Nodes'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          [
                    {
                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": "",
                     }
               ]
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/family-information/about
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'About Family'
      description: 'Get about Family'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                       "about": "",
                       "images": [],
                       "youtube_videos": [],
                     }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/comments/{id}/{type}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'get comment base on type'
      description: 'get all comment'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the comment.'
        required: true
        example: fugit
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: quis
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: fugit
      type: quis
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/comment
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: comment
      description: 'Add comment'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      body:
        name: body
        description: 'body comment.'
        required: true
        example: 'test body'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      commentable_id:
        name: commentable_id
        description: 'id of news or occasion.'
        required: true
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      commentable_type:
        name: commentable_type
        description: 'type of news or occasion.'
        required: true
        example: news
        type: string
        enumValues:
          - news
          - occasion
          - award
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      body: 'test body'
      commentable_id: '1'
      commentable_type: news
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "message": "تم إضافة الرد بنجاح",
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tree/nodes/request
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add Family Tree Change Request'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      request_type:
        name: request_type
        description: 'request type 0 delete 1 update 2 add.'
        required: true
        example: '0'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      family_tree_node_id:
        name: family_tree_node_id
        description: 'family tree node id.'
        required: true
        example: '1'
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      note:
        name: note
        description: 'type of news or occasion.'
        required: false
        example: news
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      request_type: '0'
      family_tree_node_id: '1'
      note: news
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "message": "تم إضافة الطلب بنجاح",
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 22|VjR7JBXoM8m6OJ6DveaAiT3eQp8JokBnb8nbWFyE009948d1'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v2/albums
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all Albums'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      per_page:
        name: per_page
        description: 'per page default 15'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      page:
        name: page
        description: 'page default 1'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
             "total": 1,
             "current_page": 1,
             "last_page": 1,
             "per_page": 15,
             "data":[
                    {
                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": "",
                     }
               ]
          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v2/album/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Album by id'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
      api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
    urlParameters:
      id:
        name: id
        description: 'The ID of the album.'
        required: true
        example: ut
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanUrlParameters:
      id: ut
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {

                       "id": 1,
                       "title": "",
                       "image": "",
                       "media": [],
                       "created_at": ""

          }
        headers: []
        description: success
        custom: []
      -
        status: 401
        content: |-
          {
             "message": "Unauthenticated."
          }
        headers: []
        description: unauthenticated
        custom: []
      -
        status: 500
        content: ''
        headers: []
        description: 'server error'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
