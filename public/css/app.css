/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary {
    display: block
}

audio,canvas,progress,video {
    display: inline-block;
    vertical-align: baseline
}

audio:not([controls]) {
    display: none;
    height: 0
}

[hidden],template {
    display: none
}

a {
    background-color: transparent
}

a:active,a:hover {
    outline: 0
}

abbr[title] {
    border-bottom: 1px dotted
}

b,strong {
    font-weight: 700
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

mark {
    background: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0
}

pre {
    overflow: auto
}

code,kbd,pre,samp {
    font-family: monospace,monospace;
    font-size: 1em
}

button,input,optgroup,select,textarea {
    color: inherit;
    font: inherit;
    margin: 0
}

button {
    overflow: visible
}

button,select {
    text-transform: none
}

button,html input[type=button],input[type=reset],input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],html input[disabled] {
    cursor: default
}

button::-moz-focus-inner,input::-moz-focus-inner {
    border: 0;
    padding: 0
}

input {
    line-height: normal
}

input[type=checkbox],input[type=radio] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button {
    height: auto
}

input[type=search] {
    -webkit-appearance: textfield;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: 700
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

td,th {
    padding: 0
}

/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
    *,:after,:before {
        background: transparent!important;
        color: #000!important;
        -webkit-box-shadow: none!important;
        box-shadow: none!important;
        text-shadow: none!important
    }

    a,a:visited {
        text-decoration: underline
    }

    a[href]:after {
        content: " (" attr(href) ")"
    }

    abbr[title]:after {
        content: " (" attr(title) ")"
    }

    a[href^="#"]:after,a[href^="javascript:"]:after {
        content: ""
    }

    blockquote,pre {
        border: 1px solid #999;
        page-break-inside: avoid
    }

    thead {
        display: table-header-group
    }

    img,tr {
        page-break-inside: avoid
    }

    img {
        max-width: 100%!important
    }

    h2,h3,p {
        orphans: 3;
        widows: 3
    }

    h2,h3 {
        page-break-after: avoid
    }

    .navbar {
        display: none
    }

    .btn>.caret,.dropup>.btn>.caret {
        border-top-color: #000!important
    }

    .label {
        border: 1px solid #000
    }

    .table {
        border-collapse: collapse!important
    }

    .table td,.table th {
        background-color: #fff!important
    }

    .table-bordered td,.table-bordered th {
        border: 1px solid #ddd!important
    }
}

@font-face {
    font-family: Glyphicons Halflings;
    src: url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.eot?f4769f9bdb7466be65088239c12046d1);
    src: url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.eot?f4769f9bdb7466be65088239c12046d1?#iefix) format("embedded-opentype"),url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.woff2?448c34a56d699c29117adc64c43affeb) format("woff2"),url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.woff?fa2772327f55d8198301fdb8bcfc8158) format("woff"),url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.ttf?e18bbf611f2a2e43afc071aa2f4e1512) format("truetype"),url(/fonts/vendor/bootstrap-sass/bootstrap/glyphicons-halflings-regular.svg?89889688147bd7575d6327160d64e760#glyphicons_halflingsregular) format("svg")
}

.glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: Glyphicons Halflings;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.glyphicon-asterisk:before {
    content: "*"
}

.glyphicon-plus:before {
    content: "+"
}

.glyphicon-eur:before,.glyphicon-euro:before {
    content: "\20AC"
}

.glyphicon-minus:before {
    content: "\2212"
}

.glyphicon-cloud:before {
    content: "\2601"
}

.glyphicon-envelope:before {
    content: "\2709"
}

.glyphicon-pencil:before {
    content: "\270F"
}

.glyphicon-glass:before {
    content: "\E001"
}

.glyphicon-music:before {
    content: "\E002"
}

.glyphicon-search:before {
    content: "\E003"
}

.glyphicon-heart:before {
    content: "\E005"
}

.glyphicon-star:before {
    content: "\E006"
}

.glyphicon-star-empty:before {
    content: "\E007"
}

.glyphicon-user:before {
    content: "\E008"
}

.glyphicon-film:before {
    content: "\E009"
}

.glyphicon-th-large:before {
    content: "\E010"
}

.glyphicon-th:before {
    content: "\E011"
}

.glyphicon-th-list:before {
    content: "\E012"
}

.glyphicon-ok:before {
    content: "\E013"
}

.glyphicon-remove:before {
    content: "\E014"
}

.glyphicon-zoom-in:before {
    content: "\E015"
}

.glyphicon-zoom-out:before {
    content: "\E016"
}

.glyphicon-off:before {
    content: "\E017"
}

.glyphicon-signal:before {
    content: "\E018"
}

.glyphicon-cog:before {
    content: "\E019"
}

.glyphicon-trash:before {
    content: "\E020"
}

.glyphicon-home:before {
    content: "\E021"
}

.glyphicon-file:before {
    content: "\E022"
}

.glyphicon-time:before {
    content: "\E023"
}

.glyphicon-road:before {
    content: "\E024"
}

.glyphicon-download-alt:before {
    content: "\E025"
}

.glyphicon-download:before {
    content: "\E026"
}

.glyphicon-upload:before {
    content: "\E027"
}

.glyphicon-inbox:before {
    content: "\E028"
}

.glyphicon-play-circle:before {
    content: "\E029"
}

.glyphicon-repeat:before {
    content: "\E030"
}

.glyphicon-refresh:before {
    content: "\E031"
}

.glyphicon-list-alt:before {
    content: "\E032"
}

.glyphicon-lock:before {
    content: "\E033"
}

.glyphicon-flag:before {
    content: "\E034"
}

.glyphicon-headphones:before {
    content: "\E035"
}

.glyphicon-volume-off:before {
    content: "\E036"
}

.glyphicon-volume-down:before {
    content: "\E037"
}

.glyphicon-volume-up:before {
    content: "\E038"
}

.glyphicon-qrcode:before {
    content: "\E039"
}

.glyphicon-barcode:before {
    content: "\E040"
}

.glyphicon-tag:before {
    content: "\E041"
}

.glyphicon-tags:before {
    content: "\E042"
}

.glyphicon-book:before {
    content: "\E043"
}

.glyphicon-bookmark:before {
    content: "\E044"
}

.glyphicon-print:before {
    content: "\E045"
}

.glyphicon-camera:before {
    content: "\E046"
}

.glyphicon-font:before {
    content: "\E047"
}

.glyphicon-bold:before {
    content: "\E048"
}

.glyphicon-italic:before {
    content: "\E049"
}

.glyphicon-text-height:before {
    content: "\E050"
}

.glyphicon-text-width:before {
    content: "\E051"
}

.glyphicon-align-left:before {
    content: "\E052"
}

.glyphicon-align-center:before {
    content: "\E053"
}

.glyphicon-align-right:before {
    content: "\E054"
}

.glyphicon-align-justify:before {
    content: "\E055"
}

.glyphicon-list:before {
    content: "\E056"
}

.glyphicon-indent-left:before {
    content: "\E057"
}

.glyphicon-indent-right:before {
    content: "\E058"
}

.glyphicon-facetime-video:before {
    content: "\E059"
}

.glyphicon-picture:before {
    content: "\E060"
}

.glyphicon-map-marker:before {
    content: "\E062"
}

.glyphicon-adjust:before {
    content: "\E063"
}

.glyphicon-tint:before {
    content: "\E064"
}

.glyphicon-edit:before {
    content: "\E065"
}

.glyphicon-share:before {
    content: "\E066"
}

.glyphicon-check:before {
    content: "\E067"
}

.glyphicon-move:before {
    content: "\E068"
}

.glyphicon-step-backward:before {
    content: "\E069"
}

.glyphicon-fast-backward:before {
    content: "\E070"
}

.glyphicon-backward:before {
    content: "\E071"
}

.glyphicon-play:before {
    content: "\E072"
}

.glyphicon-pause:before {
    content: "\E073"
}

.glyphicon-stop:before {
    content: "\E074"
}

.glyphicon-forward:before {
    content: "\E075"
}

.glyphicon-fast-forward:before {
    content: "\E076"
}

.glyphicon-step-forward:before {
    content: "\E077"
}

.glyphicon-eject:before {
    content: "\E078"
}

.glyphicon-chevron-left:before {
    content: "\E079"
}

.glyphicon-chevron-right:before {
    content: "\E080"
}

.glyphicon-plus-sign:before {
    content: "\E081"
}

.glyphicon-minus-sign:before {
    content: "\E082"
}

.glyphicon-remove-sign:before {
    content: "\E083"
}

.glyphicon-ok-sign:before {
    content: "\E084"
}

.glyphicon-question-sign:before {
    content: "\E085"
}

.glyphicon-info-sign:before {
    content: "\E086"
}

.glyphicon-screenshot:before {
    content: "\E087"
}

.glyphicon-remove-circle:before {
    content: "\E088"
}

.glyphicon-ok-circle:before {
    content: "\E089"
}

.glyphicon-ban-circle:before {
    content: "\E090"
}

.glyphicon-arrow-left:before {
    content: "\E091"
}

.glyphicon-arrow-right:before {
    content: "\E092"
}

.glyphicon-arrow-up:before {
    content: "\E093"
}

.glyphicon-arrow-down:before {
    content: "\E094"
}

.glyphicon-share-alt:before {
    content: "\E095"
}

.glyphicon-resize-full:before {
    content: "\E096"
}

.glyphicon-resize-small:before {
    content: "\E097"
}

.glyphicon-exclamation-sign:before {
    content: "\E101"
}

.glyphicon-gift:before {
    content: "\E102"
}

.glyphicon-leaf:before {
    content: "\E103"
}

.glyphicon-fire:before {
    content: "\E104"
}

.glyphicon-eye-open:before {
    content: "\E105"
}

.glyphicon-eye-close:before {
    content: "\E106"
}

.glyphicon-warning-sign:before {
    content: "\E107"
}

.glyphicon-plane:before {
    content: "\E108"
}

.glyphicon-calendar:before {
    content: "\E109"
}

.glyphicon-random:before {
    content: "\E110"
}

.glyphicon-comment:before {
    content: "\E111"
}

.glyphicon-magnet:before {
    content: "\E112"
}

.glyphicon-chevron-up:before {
    content: "\E113"
}

.glyphicon-chevron-down:before {
    content: "\E114"
}

.glyphicon-retweet:before {
    content: "\E115"
}

.glyphicon-shopping-cart:before {
    content: "\E116"
}

.glyphicon-folder-close:before {
    content: "\E117"
}

.glyphicon-folder-open:before {
    content: "\E118"
}

.glyphicon-resize-vertical:before {
    content: "\E119"
}

.glyphicon-resize-horizontal:before {
    content: "\E120"
}

.glyphicon-hdd:before {
    content: "\E121"
}

.glyphicon-bullhorn:before {
    content: "\E122"
}

.glyphicon-bell:before {
    content: "\E123"
}

.glyphicon-certificate:before {
    content: "\E124"
}

.glyphicon-thumbs-up:before {
    content: "\E125"
}

.glyphicon-thumbs-down:before {
    content: "\E126"
}

.glyphicon-hand-right:before {
    content: "\E127"
}

.glyphicon-hand-left:before {
    content: "\E128"
}

.glyphicon-hand-up:before {
    content: "\E129"
}

.glyphicon-hand-down:before {
    content: "\E130"
}

.glyphicon-circle-arrow-right:before {
    content: "\E131"
}

.glyphicon-circle-arrow-left:before {
    content: "\E132"
}

.glyphicon-circle-arrow-up:before {
    content: "\E133"
}

.glyphicon-circle-arrow-down:before {
    content: "\E134"
}

.glyphicon-globe:before {
    content: "\E135"
}

.glyphicon-wrench:before {
    content: "\E136"
}

.glyphicon-tasks:before {
    content: "\E137"
}

.glyphicon-filter:before {
    content: "\E138"
}

.glyphicon-briefcase:before {
    content: "\E139"
}

.glyphicon-fullscreen:before {
    content: "\E140"
}

.glyphicon-dashboard:before {
    content: "\E141"
}

.glyphicon-paperclip:before {
    content: "\E142"
}

.glyphicon-heart-empty:before {
    content: "\E143"
}

.glyphicon-link:before {
    content: "\E144"
}

.glyphicon-phone:before {
    content: "\E145"
}

.glyphicon-pushpin:before {
    content: "\E146"
}

.glyphicon-usd:before {
    content: "\E148"
}

.glyphicon-gbp:before {
    content: "\E149"
}

.glyphicon-sort:before {
    content: "\E150"
}

.glyphicon-sort-by-alphabet:before {
    content: "\E151"
}

.glyphicon-sort-by-alphabet-alt:before {
    content: "\E152"
}

.glyphicon-sort-by-order:before {
    content: "\E153"
}

.glyphicon-sort-by-order-alt:before {
    content: "\E154"
}

.glyphicon-sort-by-attributes:before {
    content: "\E155"
}

.glyphicon-sort-by-attributes-alt:before {
    content: "\E156"
}

.glyphicon-unchecked:before {
    content: "\E157"
}

.glyphicon-expand:before {
    content: "\E158"
}

.glyphicon-collapse-down:before {
    content: "\E159"
}

.glyphicon-collapse-up:before {
    content: "\E160"
}

.glyphicon-log-in:before {
    content: "\E161"
}

.glyphicon-flash:before {
    content: "\E162"
}

.glyphicon-log-out:before {
    content: "\E163"
}

.glyphicon-new-window:before {
    content: "\E164"
}

.glyphicon-record:before {
    content: "\E165"
}

.glyphicon-save:before {
    content: "\E166"
}

.glyphicon-open:before {
    content: "\E167"
}

.glyphicon-saved:before {
    content: "\E168"
}

.glyphicon-import:before {
    content: "\E169"
}

.glyphicon-export:before {
    content: "\E170"
}

.glyphicon-send:before {
    content: "\E171"
}

.glyphicon-floppy-disk:before {
    content: "\E172"
}

.glyphicon-floppy-saved:before {
    content: "\E173"
}

.glyphicon-floppy-remove:before {
    content: "\E174"
}

.glyphicon-floppy-save:before {
    content: "\E175"
}

.glyphicon-floppy-open:before {
    content: "\E176"
}

.glyphicon-credit-card:before {
    content: "\E177"
}

.glyphicon-transfer:before {
    content: "\E178"
}

.glyphicon-cutlery:before {
    content: "\E179"
}

.glyphicon-header:before {
    content: "\E180"
}

.glyphicon-compressed:before {
    content: "\E181"
}

.glyphicon-earphone:before {
    content: "\E182"
}

.glyphicon-phone-alt:before {
    content: "\E183"
}

.glyphicon-tower:before {
    content: "\E184"
}

.glyphicon-stats:before {
    content: "\E185"
}

.glyphicon-sd-video:before {
    content: "\E186"
}

.glyphicon-hd-video:before {
    content: "\E187"
}

.glyphicon-subtitles:before {
    content: "\E188"
}

.glyphicon-sound-stereo:before {
    content: "\E189"
}

.glyphicon-sound-dolby:before {
    content: "\E190"
}

.glyphicon-sound-5-1:before {
    content: "\E191"
}

.glyphicon-sound-6-1:before {
    content: "\E192"
}

.glyphicon-sound-7-1:before {
    content: "\E193"
}

.glyphicon-copyright-mark:before {
    content: "\E194"
}

.glyphicon-registration-mark:before {
    content: "\E195"
}

.glyphicon-cloud-download:before {
    content: "\E197"
}

.glyphicon-cloud-upload:before {
    content: "\E198"
}

.glyphicon-tree-conifer:before {
    content: "\E199"
}

.glyphicon-tree-deciduous:before {
    content: "\E200"
}

.glyphicon-cd:before {
    content: "\E201"
}

.glyphicon-save-file:before {
    content: "\E202"
}

.glyphicon-open-file:before {
    content: "\E203"
}

.glyphicon-level-up:before {
    content: "\E204"
}

.glyphicon-copy:before {
    content: "\E205"
}

.glyphicon-paste:before {
    content: "\E206"
}

.glyphicon-alert:before {
    content: "\E209"
}

.glyphicon-equalizer:before {
    content: "\E210"
}

.glyphicon-king:before {
    content: "\E211"
}

.glyphicon-queen:before {
    content: "\E212"
}

.glyphicon-pawn:before {
    content: "\E213"
}

.glyphicon-bishop:before {
    content: "\E214"
}

.glyphicon-knight:before {
    content: "\E215"
}

.glyphicon-baby-formula:before {
    content: "\E216"
}

.glyphicon-tent:before {
    content: "\26FA"
}

.glyphicon-blackboard:before {
    content: "\E218"
}

.glyphicon-bed:before {
    content: "\E219"
}

.glyphicon-apple:before {
    content: "\F8FF"
}

.glyphicon-erase:before {
    content: "\E221"
}

.glyphicon-hourglass:before {
    content: "\231B"
}

.glyphicon-lamp:before {
    content: "\E223"
}

.glyphicon-duplicate:before {
    content: "\E224"
}

.glyphicon-piggy-bank:before {
    content: "\E225"
}

.glyphicon-scissors:before {
    content: "\E226"
}

.glyphicon-bitcoin:before,.glyphicon-btc:before,.glyphicon-xbt:before {
    content: "\E227"
}

.glyphicon-jpy:before,.glyphicon-yen:before {
    content: "\A5"
}

.glyphicon-rub:before,.glyphicon-ruble:before {
    content: "\20BD"
}

.glyphicon-scale:before {
    content: "\E230"
}

.glyphicon-ice-lolly:before {
    content: "\E231"
}

.glyphicon-ice-lolly-tasted:before {
    content: "\E232"
}

.glyphicon-education:before {
    content: "\E233"
}

.glyphicon-option-horizontal:before {
    content: "\E234"
}

.glyphicon-option-vertical:before {
    content: "\E235"
}

.glyphicon-menu-hamburger:before {
    content: "\E236"
}

.glyphicon-modal-window:before {
    content: "\E237"
}

.glyphicon-oil:before {
    content: "\E238"
}

.glyphicon-grain:before {
    content: "\E239"
}

.glyphicon-sunglasses:before {
    content: "\E240"
}

.glyphicon-text-size:before {
    content: "\E241"
}

.glyphicon-text-color:before {
    content: "\E242"
}

.glyphicon-text-background:before {
    content: "\E243"
}

.glyphicon-object-align-top:before {
    content: "\E244"
}

.glyphicon-object-align-bottom:before {
    content: "\E245"
}

.glyphicon-object-align-horizontal:before {
    content: "\E246"
}

.glyphicon-object-align-left:before {
    content: "\E247"
}

.glyphicon-object-align-vertical:before {
    content: "\E248"
}

.glyphicon-object-align-right:before {
    content: "\E249"
}

.glyphicon-triangle-right:before {
    content: "\E250"
}

.glyphicon-triangle-left:before {
    content: "\E251"
}

.glyphicon-triangle-bottom:before {
    content: "\E252"
}

.glyphicon-triangle-top:before {
    content: "\E253"
}

.glyphicon-console:before {
    content: "\E254"
}

.glyphicon-superscript:before {
    content: "\E255"
}

.glyphicon-subscript:before {
    content: "\E256"
}

.glyphicon-menu-left:before {
    content: "\E257"
}

.glyphicon-menu-right:before {
    content: "\E258"
}

.glyphicon-menu-down:before {
    content: "\E259"
}

.glyphicon-menu-up:before {
    content: "\E260"
}

*,:after,:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

html {
    font-size: 10px;
    -webkit-tap-highlight-color: transparent
}

body {
    font-family: Raleway,sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #636b6f;
    background-color: #f5f8fa
}

button,input,select,textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

a {
    color: #3097d1;
    text-decoration: none
}

a:focus,a:hover {
    color: #216a94;
    text-decoration: underline
}

a:focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

figure {
    margin: 0
}

img {
    vertical-align: middle
}

.img-responsive {
    display: block;
    max-width: 100%;
    height: auto
}

.img-rounded {
    border-radius: 6px
}

.img-thumbnail {
    padding: 4px;
    line-height: 1.6;
    background-color: #f5f8fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    display: inline-block;
    max-width: 100%;
    height: auto
}

.img-circle {
    border-radius: 50%
}

hr {
    margin-top: 22px;
    margin-bottom: 22px;
    border: 0;
    border-top: 1px solid #eee
}

[role=button] {
    cursor: pointer
}

.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit
}

.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small {
    font-weight: 400;
    line-height: 1;
    color: #777
}

.h1,.h2,.h3,h1,h2,h3 {
    margin-top: 22px;
    margin-bottom: 11px
}

.h1 .small,.h1 small,.h2 .small,.h2 small,.h3 .small,.h3 small,h1 .small,h1 small,h2 .small,h2 small,h3 .small,h3 small {
    font-size: 65%
}

.h4,.h5,.h6,h4,h5,h6 {
    margin-top: 11px;
    margin-bottom: 11px
}

.h4 .small,.h4 small,.h5 .small,.h5 small,.h6 .small,.h6 small,h4 .small,h4 small,h5 .small,h5 small,h6 .small,h6 small {
    font-size: 75%
}

.h1,h1 {
    font-size: 36px
}

.h2,h2 {
    font-size: 30px
}

.h3,h3 {
    font-size: 24px
}

.h4,h4 {
    font-size: 18px
}

.h5,h5 {
    font-size: 14px
}

.h6,h6 {
    font-size: 12px
}

p {
    margin: 0 0 11px
}

.lead {
    margin-bottom: 22px;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.4
}

@media (min-width: 768px) {
    .lead {
        font-size:21px
    }
}

.small,small {
    font-size: 85%
}

.mark,mark {
    background-color: #fcf8e3;
    padding: .2em
}

.text-left {
    text-align: left
}

.text-right {
    text-align: right
}

.text-center {
    text-align: center
}

.text-justify {
    text-align: justify
}

.text-nowrap {
    white-space: nowrap
}

.text-lowercase {
    text-transform: lowercase
}

.initialism,.text-uppercase {
    text-transform: uppercase
}

.text-capitalize {
    text-transform: capitalize
}

.text-muted {
    color: #777
}

.text-primary {
    color: #3097d1
}

a.text-primary:focus,a.text-primary:hover {
    color: #2579a9
}

.text-success {
    color: #4caf50
}

a.text-success:focus,a.text-success:hover {
    color: #3d8b40
}

.text-info {
    color: #31708f
}

a.text-info:focus,a.text-info:hover {
    color: #245269
}

.text-warning {
    color: #a18c74
}

a.text-warning:focus,a.text-warning:hover {
    color: #87725b
}

.text-danger {
    color: #a94442
}

a.text-danger:focus,a.text-danger:hover {
    color: #843534
}

.bg-primary {
    color: #fff;
    background-color: #3097d1
}

a.bg-primary:focus,a.bg-primary:hover {
    background-color: #2579a9
}

.bg-success {
    background-color: #dff0d8
}

a.bg-success:focus,a.bg-success:hover {
    background-color: #c1e2b3
}

.bg-info {
    background-color: #d9edf7
}

a.bg-info:focus,a.bg-info:hover {
    background-color: #afd9ee
}

.bg-warning {
    background-color: #fcf8e3
}

a.bg-warning:focus,a.bg-warning:hover {
    background-color: #f7ecb5
}

.bg-danger {
    background-color: #f2dede
}

a.bg-danger:focus,a.bg-danger:hover {
    background-color: #e4b9b9
}

.page-header {
    padding-bottom: 10px;
    margin: 44px 0 22px;
    border-bottom: 1px solid #eee
}

ol,ul {
    margin-top: 0;
    margin-bottom: 11px
}

ol ol,ol ul,ul ol,ul ul {
    margin-bottom: 0
}

.list-inline,.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    margin-left: -5px
}

.list-inline>li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px
}

dl {
    margin-top: 0;
    margin-bottom: 22px
}

dd,dt {
    line-height: 1.6
}

dt {
    font-weight: 700
}

dd {
    margin-left: 0
}

.dl-horizontal dd:after,.dl-horizontal dd:before {
    content: " ";
    display: table
}

.dl-horizontal dd:after {
    clear: both
}

@media (min-width: 768px) {
    .dl-horizontal dt {
        float:left;
        width: 160px;
        clear: left;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap
    }

    .dl-horizontal dd {
        margin-left: 180px
    }
}

abbr[data-original-title],abbr[title] {
    cursor: help;
    border-bottom: 1px dotted #777
}

.initialism {
    font-size: 90%
}

blockquote {
    padding: 11px 22px;
    margin: 0 0 22px;
    font-size: 17.5px;
    border-left: 5px solid #eee
}

blockquote ol:last-child,blockquote p:last-child,blockquote ul:last-child {
    margin-bottom: 0
}

blockquote .small,blockquote footer,blockquote small {
    display: block;
    font-size: 80%;
    line-height: 1.6;
    color: #777
}

blockquote .small:before,blockquote footer:before,blockquote small:before {
    content: "\2014   \A0"
}

.blockquote-reverse,blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #eee;
    border-left: 0;
    text-align: right
}

.blockquote-reverse .small:before,.blockquote-reverse footer:before,.blockquote-reverse small:before,blockquote.pull-right .small:before,blockquote.pull-right footer:before,blockquote.pull-right small:before {
    content: ""
}

.blockquote-reverse .small:after,.blockquote-reverse footer:after,.blockquote-reverse small:after,blockquote.pull-right .small:after,blockquote.pull-right footer:after,blockquote.pull-right small:after {
    content: "\A0   \2014"
}

address {
    margin-bottom: 22px;
    font-style: normal;
    line-height: 1.6
}

code,kbd,pre,samp {
    font-family: Menlo,Monaco,Consolas,Courier New,monospace
}

code {
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px
}

code,kbd {
    padding: 2px 4px;
    font-size: 90%
}

kbd {
    color: #fff;
    background-color: #333;
    border-radius: 3px;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0,0,0,.25);
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.25)
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 700;
    -webkit-box-shadow: none;
    box-shadow: none
}

pre {
    display: block;
    padding: 10.5px;
    margin: 0 0 11px;
    font-size: 13px;
    line-height: 1.6;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

.container:after,.container:before {
    content: " ";
    display: table
}

.container:after {
    clear: both
}

@media (min-width: 768px) {
    .container {
        width:750px
    }
}

@media (min-width: 992px) {
    .container {
        width:970px
    }
}

@media (min-width: 1200px) {
    .container {
        width:1170px
    }
}

.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

.container-fluid:after,.container-fluid:before {
    content: " ";
    display: table
}

.container-fluid:after {
    clear: both
}

.row {
    margin-left: -15px;
    margin-right: -15px
}

.row:after,.row:before {
    content: " ";
    display: table
}

.row:after {
    clear: both
}

.col-lg-1,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-lg-10,.col-lg-11,.col-lg-12,.col-md-1,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-md-10,.col-md-11,.col-md-12,.col-sm-1,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-sm-10,.col-sm-11,.col-sm-12,.col-xs-1,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9,.col-xs-10,.col-xs-11,.col-xs-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px
}

.col-xs-1,.col-xs-2,.col-xs-3,.col-xs-4,.col-xs-5,.col-xs-6,.col-xs-7,.col-xs-8,.col-xs-9,.col-xs-10,.col-xs-11,.col-xs-12 {
    float: left
}

.col-xs-1 {
    width: 8.33333333%
}

.col-xs-2 {
    width: 16.66666667%
}

.col-xs-3 {
    width: 25%
}

.col-xs-4 {
    width: 33.33333333%
}

.col-xs-5 {
    width: 41.66666667%
}

.col-xs-6 {
    width: 50%
}

.col-xs-7 {
    width: 58.33333333%
}

.col-xs-8 {
    width: 66.66666667%
}

.col-xs-9 {
    width: 75%
}

.col-xs-10 {
    width: 83.33333333%
}

.col-xs-11 {
    width: 91.66666667%
}

.col-xs-12 {
    width: 100%
}

.col-xs-pull-0 {
    right: auto
}

.col-xs-pull-1 {
    right: 8.33333333%
}

.col-xs-pull-2 {
    right: 16.66666667%
}

.col-xs-pull-3 {
    right: 25%
}

.col-xs-pull-4 {
    right: 33.33333333%
}

.col-xs-pull-5 {
    right: 41.66666667%
}

.col-xs-pull-6 {
    right: 50%
}

.col-xs-pull-7 {
    right: 58.33333333%
}

.col-xs-pull-8 {
    right: 66.66666667%
}

.col-xs-pull-9 {
    right: 75%
}

.col-xs-pull-10 {
    right: 83.33333333%
}

.col-xs-pull-11 {
    right: 91.66666667%
}

.col-xs-pull-12 {
    right: 100%
}

.col-xs-push-0 {
    left: auto
}

.col-xs-push-1 {
    left: 8.33333333%
}

.col-xs-push-2 {
    left: 16.66666667%
}

.col-xs-push-3 {
    left: 25%
}

.col-xs-push-4 {
    left: 33.33333333%
}

.col-xs-push-5 {
    left: 41.66666667%
}

.col-xs-push-6 {
    left: 50%
}

.col-xs-push-7 {
    left: 58.33333333%
}

.col-xs-push-8 {
    left: 66.66666667%
}

.col-xs-push-9 {
    left: 75%
}

.col-xs-push-10 {
    left: 83.33333333%
}

.col-xs-push-11 {
    left: 91.66666667%
}

.col-xs-push-12 {
    left: 100%
}

.col-xs-offset-0 {
    margin-left: 0
}

.col-xs-offset-1 {
    margin-left: 8.33333333%
}

.col-xs-offset-2 {
    margin-left: 16.66666667%
}

.col-xs-offset-3 {
    margin-left: 25%
}

.col-xs-offset-4 {
    margin-left: 33.33333333%
}

.col-xs-offset-5 {
    margin-left: 41.66666667%
}

.col-xs-offset-6 {
    margin-left: 50%
}

.col-xs-offset-7 {
    margin-left: 58.33333333%
}

.col-xs-offset-8 {
    margin-left: 66.66666667%
}

.col-xs-offset-9 {
    margin-left: 75%
}

.col-xs-offset-10 {
    margin-left: 83.33333333%
}

.col-xs-offset-11 {
    margin-left: 91.66666667%
}

.col-xs-offset-12 {
    margin-left: 100%
}

@media (min-width: 768px) {
    .col-sm-1,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-sm-10,.col-sm-11,.col-sm-12 {
        float:left
    }

    .col-sm-1 {
        width: 8.33333333%
    }

    .col-sm-2 {
        width: 16.66666667%
    }

    .col-sm-3 {
        width: 25%
    }

    .col-sm-4 {
        width: 33.33333333%
    }

    .col-sm-5 {
        width: 41.66666667%
    }

    .col-sm-6 {
        width: 50%
    }

    .col-sm-7 {
        width: 58.33333333%
    }

    .col-sm-8 {
        width: 66.66666667%
    }

    .col-sm-9 {
        width: 75%
    }

    .col-sm-10 {
        width: 83.33333333%
    }

    .col-sm-11 {
        width: 91.66666667%
    }

    .col-sm-12 {
        width: 100%
    }

    .col-sm-pull-0 {
        right: auto
    }

    .col-sm-pull-1 {
        right: 8.33333333%
    }

    .col-sm-pull-2 {
        right: 16.66666667%
    }

    .col-sm-pull-3 {
        right: 25%
    }

    .col-sm-pull-4 {
        right: 33.33333333%
    }

    .col-sm-pull-5 {
        right: 41.66666667%
    }

    .col-sm-pull-6 {
        right: 50%
    }

    .col-sm-pull-7 {
        right: 58.33333333%
    }

    .col-sm-pull-8 {
        right: 66.66666667%
    }

    .col-sm-pull-9 {
        right: 75%
    }

    .col-sm-pull-10 {
        right: 83.33333333%
    }

    .col-sm-pull-11 {
        right: 91.66666667%
    }

    .col-sm-pull-12 {
        right: 100%
    }

    .col-sm-push-0 {
        left: auto
    }

    .col-sm-push-1 {
        left: 8.33333333%
    }

    .col-sm-push-2 {
        left: 16.66666667%
    }

    .col-sm-push-3 {
        left: 25%
    }

    .col-sm-push-4 {
        left: 33.33333333%
    }

    .col-sm-push-5 {
        left: 41.66666667%
    }

    .col-sm-push-6 {
        left: 50%
    }

    .col-sm-push-7 {
        left: 58.33333333%
    }

    .col-sm-push-8 {
        left: 66.66666667%
    }

    .col-sm-push-9 {
        left: 75%
    }

    .col-sm-push-10 {
        left: 83.33333333%
    }

    .col-sm-push-11 {
        left: 91.66666667%
    }

    .col-sm-push-12 {
        left: 100%
    }

    .col-sm-offset-0 {
        margin-left: 0
    }

    .col-sm-offset-1 {
        margin-left: 8.33333333%
    }

    .col-sm-offset-2 {
        margin-left: 16.66666667%
    }

    .col-sm-offset-3 {
        margin-left: 25%
    }

    .col-sm-offset-4 {
        margin-left: 33.33333333%
    }

    .col-sm-offset-5 {
        margin-left: 41.66666667%
    }

    .col-sm-offset-6 {
        margin-left: 50%
    }

    .col-sm-offset-7 {
        margin-left: 58.33333333%
    }

    .col-sm-offset-8 {
        margin-left: 66.66666667%
    }

    .col-sm-offset-9 {
        margin-left: 75%
    }

    .col-sm-offset-10 {
        margin-left: 83.33333333%
    }

    .col-sm-offset-11 {
        margin-left: 91.66666667%
    }

    .col-sm-offset-12 {
        margin-left: 100%
    }
}

@media (min-width: 992px) {
    .col-md-1,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-md-10,.col-md-11,.col-md-12 {
        float:left
    }

    .col-md-1 {
        width: 8.33333333%
    }

    .col-md-2 {
        width: 16.66666667%
    }

    .col-md-3 {
        width: 25%
    }

    .col-md-4 {
        width: 33.33333333%
    }

    .col-md-5 {
        width: 41.66666667%
    }

    .col-md-6 {
        width: 50%
    }

    .col-md-7 {
        width: 58.33333333%
    }

    .col-md-8 {
        width: 66.66666667%
    }

    .col-md-9 {
        width: 75%
    }

    .col-md-10 {
        width: 83.33333333%
    }

    .col-md-11 {
        width: 91.66666667%
    }

    .col-md-12 {
        width: 100%
    }

    .col-md-pull-0 {
        right: auto
    }

    .col-md-pull-1 {
        right: 8.33333333%
    }

    .col-md-pull-2 {
        right: 16.66666667%
    }

    .col-md-pull-3 {
        right: 25%
    }

    .col-md-pull-4 {
        right: 33.33333333%
    }

    .col-md-pull-5 {
        right: 41.66666667%
    }

    .col-md-pull-6 {
        right: 50%
    }

    .col-md-pull-7 {
        right: 58.33333333%
    }

    .col-md-pull-8 {
        right: 66.66666667%
    }

    .col-md-pull-9 {
        right: 75%
    }

    .col-md-pull-10 {
        right: 83.33333333%
    }

    .col-md-pull-11 {
        right: 91.66666667%
    }

    .col-md-pull-12 {
        right: 100%
    }

    .col-md-push-0 {
        left: auto
    }

    .col-md-push-1 {
        left: 8.33333333%
    }

    .col-md-push-2 {
        left: 16.66666667%
    }

    .col-md-push-3 {
        left: 25%
    }

    .col-md-push-4 {
        left: 33.33333333%
    }

    .col-md-push-5 {
        left: 41.66666667%
    }

    .col-md-push-6 {
        left: 50%
    }

    .col-md-push-7 {
        left: 58.33333333%
    }

    .col-md-push-8 {
        left: 66.66666667%
    }

    .col-md-push-9 {
        left: 75%
    }

    .col-md-push-10 {
        left: 83.33333333%
    }

    .col-md-push-11 {
        left: 91.66666667%
    }

    .col-md-push-12 {
        left: 100%
    }

    .col-md-offset-0 {
        margin-left: 0
    }

    .col-md-offset-1 {
        margin-left: 8.33333333%
    }

    .col-md-offset-2 {
        margin-left: 16.66666667%
    }

    .col-md-offset-3 {
        margin-left: 25%
    }

    .col-md-offset-4 {
        margin-left: 33.33333333%
    }

    .col-md-offset-5 {
        margin-left: 41.66666667%
    }

    .col-md-offset-6 {
        margin-left: 50%
    }

    .col-md-offset-7 {
        margin-left: 58.33333333%
    }

    .col-md-offset-8 {
        margin-left: 66.66666667%
    }

    .col-md-offset-9 {
        margin-left: 75%
    }

    .col-md-offset-10 {
        margin-left: 83.33333333%
    }

    .col-md-offset-11 {
        margin-left: 91.66666667%
    }

    .col-md-offset-12 {
        margin-left: 100%
    }
}

@media (min-width: 1200px) {
    .col-lg-1,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-lg-10,.col-lg-11,.col-lg-12 {
        float:left
    }

    .col-lg-1 {
        width: 8.33333333%
    }

    .col-lg-2 {
        width: 16.66666667%
    }

    .col-lg-3 {
        width: 25%
    }

    .col-lg-4 {
        width: 33.33333333%
    }

    .col-lg-5 {
        width: 41.66666667%
    }

    .col-lg-6 {
        width: 50%
    }

    .col-lg-7 {
        width: 58.33333333%
    }

    .col-lg-8 {
        width: 66.66666667%
    }

    .col-lg-9 {
        width: 75%
    }

    .col-lg-10 {
        width: 83.33333333%
    }

    .col-lg-11 {
        width: 91.66666667%
    }

    .col-lg-12 {
        width: 100%
    }

    .col-lg-pull-0 {
        right: auto
    }

    .col-lg-pull-1 {
        right: 8.33333333%
    }

    .col-lg-pull-2 {
        right: 16.66666667%
    }

    .col-lg-pull-3 {
        right: 25%
    }

    .col-lg-pull-4 {
        right: 33.33333333%
    }

    .col-lg-pull-5 {
        right: 41.66666667%
    }

    .col-lg-pull-6 {
        right: 50%
    }

    .col-lg-pull-7 {
        right: 58.33333333%
    }

    .col-lg-pull-8 {
        right: 66.66666667%
    }

    .col-lg-pull-9 {
        right: 75%
    }

    .col-lg-pull-10 {
        right: 83.33333333%
    }

    .col-lg-pull-11 {
        right: 91.66666667%
    }

    .col-lg-pull-12 {
        right: 100%
    }

    .col-lg-push-0 {
        left: auto
    }

    .col-lg-push-1 {
        left: 8.33333333%
    }

    .col-lg-push-2 {
        left: 16.66666667%
    }

    .col-lg-push-3 {
        left: 25%
    }

    .col-lg-push-4 {
        left: 33.33333333%
    }

    .col-lg-push-5 {
        left: 41.66666667%
    }

    .col-lg-push-6 {
        left: 50%
    }

    .col-lg-push-7 {
        left: 58.33333333%
    }

    .col-lg-push-8 {
        left: 66.66666667%
    }

    .col-lg-push-9 {
        left: 75%
    }

    .col-lg-push-10 {
        left: 83.33333333%
    }

    .col-lg-push-11 {
        left: 91.66666667%
    }

    .col-lg-push-12 {
        left: 100%
    }

    .col-lg-offset-0 {
        margin-left: 0
    }

    .col-lg-offset-1 {
        margin-left: 8.33333333%
    }

    .col-lg-offset-2 {
        margin-left: 16.66666667%
    }

    .col-lg-offset-3 {
        margin-left: 25%
    }

    .col-lg-offset-4 {
        margin-left: 33.33333333%
    }

    .col-lg-offset-5 {
        margin-left: 41.66666667%
    }

    .col-lg-offset-6 {
        margin-left: 50%
    }

    .col-lg-offset-7 {
        margin-left: 58.33333333%
    }

    .col-lg-offset-8 {
        margin-left: 66.66666667%
    }

    .col-lg-offset-9 {
        margin-left: 75%
    }

    .col-lg-offset-10 {
        margin-left: 83.33333333%
    }

    .col-lg-offset-11 {
        margin-left: 91.66666667%
    }

    .col-lg-offset-12 {
        margin-left: 100%
    }
}

table {
    background-color: transparent
}

caption {
    padding-top: 8px;
    padding-bottom: 8px;
    color: #777
}

caption,th {
    text-align: left
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 22px
}

.table>tbody>tr>td,.table>tbody>tr>th,.table>tfoot>tr>td,.table>tfoot>tr>th,.table>thead>tr>td,.table>thead>tr>th {
    padding: 8px;
    line-height: 1.6;
    vertical-align: top;
    border-top: 1px solid #ddd
}

.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd
}

.table>caption+thead>tr:first-child>td,.table>caption+thead>tr:first-child>th,.table>colgroup+thead>tr:first-child>td,.table>colgroup+thead>tr:first-child>th,.table>thead:first-child>tr:first-child>td,.table>thead:first-child>tr:first-child>th {
    border-top: 0
}

.table>tbody+tbody {
    border-top: 2px solid #ddd
}

.table .table {
    background-color: #f5f8fa
}

.table-condensed>tbody>tr>td,.table-condensed>tbody>tr>th,.table-condensed>tfoot>tr>td,.table-condensed>tfoot>tr>th,.table-condensed>thead>tr>td,.table-condensed>thead>tr>th {
    padding: 5px
}

.table-bordered,.table-bordered>tbody>tr>td,.table-bordered>tbody>tr>th,.table-bordered>tfoot>tr>td,.table-bordered>tfoot>tr>th,.table-bordered>thead>tr>td,.table-bordered>thead>tr>th {
    border: 1px solid #ddd
}

.table-bordered>thead>tr>td,.table-bordered>thead>tr>th {
    border-bottom-width: 2px
}

.table-striped>tbody>tr:nth-of-type(odd) {
    background-color: #f9f9f9
}

.table-hover>tbody>tr:hover {
    background-color: #f5f5f5
}

table col[class*=col-] {
    position: static;
    float: none;
    display: table-column
}

table td[class*=col-],table th[class*=col-] {
    position: static;
    float: none;
    display: table-cell
}

.table>tbody>tr.active>td,.table>tbody>tr.active>th,.table>tbody>tr>td.active,.table>tbody>tr>th.active,.table>tfoot>tr.active>td,.table>tfoot>tr.active>th,.table>tfoot>tr>td.active,.table>tfoot>tr>th.active,.table>thead>tr.active>td,.table>thead>tr.active>th,.table>thead>tr>td.active,.table>thead>tr>th.active {
    background-color: #f5f5f5
}

.table-hover>tbody>tr.active:hover>td,.table-hover>tbody>tr.active:hover>th,.table-hover>tbody>tr:hover>.active,.table-hover>tbody>tr>td.active:hover,.table-hover>tbody>tr>th.active:hover {
    background-color: #e8e8e8
}

.table>tbody>tr.success>td,.table>tbody>tr.success>th,.table>tbody>tr>td.success,.table>tbody>tr>th.success,.table>tfoot>tr.success>td,.table>tfoot>tr.success>th,.table>tfoot>tr>td.success,.table>tfoot>tr>th.success,.table>thead>tr.success>td,.table>thead>tr.success>th,.table>thead>tr>td.success,.table>thead>tr>th.success {
    background-color: #dff0d8
}

.table-hover>tbody>tr.success:hover>td,.table-hover>tbody>tr.success:hover>th,.table-hover>tbody>tr:hover>.success,.table-hover>tbody>tr>td.success:hover,.table-hover>tbody>tr>th.success:hover {
    background-color: #d0e9c6
}

.table>tbody>tr.info>td,.table>tbody>tr.info>th,.table>tbody>tr>td.info,.table>tbody>tr>th.info,.table>tfoot>tr.info>td,.table>tfoot>tr.info>th,.table>tfoot>tr>td.info,.table>tfoot>tr>th.info,.table>thead>tr.info>td,.table>thead>tr.info>th,.table>thead>tr>td.info,.table>thead>tr>th.info {
    background-color: #d9edf7
}

.table-hover>tbody>tr.info:hover>td,.table-hover>tbody>tr.info:hover>th,.table-hover>tbody>tr:hover>.info,.table-hover>tbody>tr>td.info:hover,.table-hover>tbody>tr>th.info:hover {
    background-color: #c4e3f3
}

.table>tbody>tr.warning>td,.table>tbody>tr.warning>th,.table>tbody>tr>td.warning,.table>tbody>tr>th.warning,.table>tfoot>tr.warning>td,.table>tfoot>tr.warning>th,.table>tfoot>tr>td.warning,.table>tfoot>tr>th.warning,.table>thead>tr.warning>td,.table>thead>tr.warning>th,.table>thead>tr>td.warning,.table>thead>tr>th.warning {
    background-color: #fcf8e3
}

.table-hover>tbody>tr.warning:hover>td,.table-hover>tbody>tr.warning:hover>th,.table-hover>tbody>tr:hover>.warning,.table-hover>tbody>tr>td.warning:hover,.table-hover>tbody>tr>th.warning:hover {
    background-color: #faf2cc
}

.table>tbody>tr.danger>td,.table>tbody>tr.danger>th,.table>tbody>tr>td.danger,.table>tbody>tr>th.danger,.table>tfoot>tr.danger>td,.table>tfoot>tr.danger>th,.table>tfoot>tr>td.danger,.table>tfoot>tr>th.danger,.table>thead>tr.danger>td,.table>thead>tr.danger>th,.table>thead>tr>td.danger,.table>thead>tr>th.danger {
    background-color: #f2dede
}

.table-hover>tbody>tr.danger:hover>td,.table-hover>tbody>tr.danger:hover>th,.table-hover>tbody>tr:hover>.danger,.table-hover>tbody>tr>td.danger:hover,.table-hover>tbody>tr>th.danger:hover {
    background-color: #ebcccc
}

.table-responsive {
    overflow-x: auto;
    min-height: .01%
}

@media screen and (max-width: 767px) {
    .table-responsive {
        width:100%;
        margin-bottom: 16.5px;
        overflow-y: hidden;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: 1px solid #ddd
    }

    .table-responsive>.table {
        margin-bottom: 0
    }

    .table-responsive>.table>tbody>tr>td,.table-responsive>.table>tbody>tr>th,.table-responsive>.table>tfoot>tr>td,.table-responsive>.table>tfoot>tr>th,.table-responsive>.table>thead>tr>td,.table-responsive>.table>thead>tr>th {
        white-space: nowrap
    }

    .table-responsive>.table-bordered {
        border: 0
    }

    .table-responsive>.table-bordered>tbody>tr>td:first-child,.table-responsive>.table-bordered>tbody>tr>th:first-child,.table-responsive>.table-bordered>tfoot>tr>td:first-child,.table-responsive>.table-bordered>tfoot>tr>th:first-child,.table-responsive>.table-bordered>thead>tr>td:first-child,.table-responsive>.table-bordered>thead>tr>th:first-child {
        border-left: 0
    }

    .table-responsive>.table-bordered>tbody>tr>td:last-child,.table-responsive>.table-bordered>tbody>tr>th:last-child,.table-responsive>.table-bordered>tfoot>tr>td:last-child,.table-responsive>.table-bordered>tfoot>tr>th:last-child,.table-responsive>.table-bordered>thead>tr>td:last-child,.table-responsive>.table-bordered>thead>tr>th:last-child {
        border-right: 0
    }

    .table-responsive>.table-bordered>tbody>tr:last-child>td,.table-responsive>.table-bordered>tbody>tr:last-child>th,.table-responsive>.table-bordered>tfoot>tr:last-child>td,.table-responsive>.table-bordered>tfoot>tr:last-child>th {
        border-bottom: 0
    }
}

fieldset {
    margin: 0;
    min-width: 0
}

fieldset,legend {
    padding: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    margin-bottom: 22px;
    font-size: 21px;
    line-height: inherit;
    color: #333;
    border-bottom: 1px solid #e5e5e5
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 700
}

input[type=search] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

input[type=checkbox],input[type=radio] {
    margin: 4px 0 0;
    margin-top: 1px\9;
    line-height: normal
}

input[type=file] {
    display: block
}

input[type=range] {
    display: block;
    width: 100%
}

select[multiple],select[size] {
    height: auto
}

input[type=checkbox]:focus,input[type=file]:focus,input[type=radio]:focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

output {
    padding-top: 7px
}

.form-control,output {
    display: block;
    font-size: 14px;
    line-height: 1.6;
    color: #555
}

.form-control {
    width: 100%;
    height: 36px;
    padding: 6px 12px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccd0d2;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

.form-control:focus {
    border-color: #98cbe8;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(152,203,232,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(152,203,232,.6)
}

.form-control::-moz-placeholder {
    color: #b1b7ba;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #b1b7ba
}

.form-control::-webkit-input-placeholder {
    color: #b1b7ba
}

.form-control::-ms-expand {
    border: 0;
    background-color: transparent
}

.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control {
    background-color: #eee;
    opacity: 1
}

.form-control[disabled],fieldset[disabled] .form-control {
    cursor: not-allowed
}

textarea.form-control {
    height: auto
}

input[type=search] {
    -webkit-appearance: none
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type=date].form-control,input[type=datetime-local].form-control,input[type=month].form-control,input[type=time].form-control {
        line-height:36px
    }

    .input-group-sm>.input-group-btn>input[type=date].btn,.input-group-sm>.input-group-btn>input[type=datetime-local].btn,.input-group-sm>.input-group-btn>input[type=month].btn,.input-group-sm>.input-group-btn>input[type=time].btn,.input-group-sm>input[type=date].form-control,.input-group-sm>input[type=date].input-group-addon,.input-group-sm>input[type=datetime-local].form-control,.input-group-sm>input[type=datetime-local].input-group-addon,.input-group-sm>input[type=month].form-control,.input-group-sm>input[type=month].input-group-addon,.input-group-sm>input[type=time].form-control,.input-group-sm>input[type=time].input-group-addon,.input-group-sm input[type=date],.input-group-sm input[type=datetime-local],.input-group-sm input[type=month],.input-group-sm input[type=time],input[type=date].input-sm,input[type=datetime-local].input-sm,input[type=month].input-sm,input[type=time].input-sm {
        line-height: 30px
    }

    .input-group-lg>.input-group-btn>input[type=date].btn,.input-group-lg>.input-group-btn>input[type=datetime-local].btn,.input-group-lg>.input-group-btn>input[type=month].btn,.input-group-lg>.input-group-btn>input[type=time].btn,.input-group-lg>input[type=date].form-control,.input-group-lg>input[type=date].input-group-addon,.input-group-lg>input[type=datetime-local].form-control,.input-group-lg>input[type=datetime-local].input-group-addon,.input-group-lg>input[type=month].form-control,.input-group-lg>input[type=month].input-group-addon,.input-group-lg>input[type=time].form-control,.input-group-lg>input[type=time].input-group-addon,.input-group-lg input[type=date],.input-group-lg input[type=datetime-local],.input-group-lg input[type=month],.input-group-lg input[type=time],input[type=date].input-lg,input[type=datetime-local].input-lg,input[type=month].input-lg,input[type=time].input-lg {
        line-height: 46px
    }
}

.form-group {
    margin-bottom: 15px
}

.checkbox,.radio {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px
}

.checkbox label,.radio label {
    min-height: 22px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: 400;
    cursor: pointer
}

.checkbox-inline input[type=checkbox],.checkbox input[type=checkbox],.radio-inline input[type=radio],.radio input[type=radio] {
    position: absolute;
    margin-left: -20px;
    margin-top: 4px\9
}

.checkbox+.checkbox,.radio+.radio {
    margin-top: -5px
}

.checkbox-inline,.radio-inline {
    position: relative;
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    vertical-align: middle;
    font-weight: 400;
    cursor: pointer
}

.checkbox-inline+.checkbox-inline,.radio-inline+.radio-inline {
    margin-top: 0;
    margin-left: 10px
}

.checkbox-inline.disabled,.checkbox.disabled label,.radio-inline.disabled,.radio.disabled label,fieldset[disabled] .checkbox-inline,fieldset[disabled] .checkbox label,fieldset[disabled] .radio-inline,fieldset[disabled] .radio label,fieldset[disabled] input[type=checkbox],fieldset[disabled] input[type=radio],input[type=checkbox].disabled,input[type=checkbox][disabled],input[type=radio].disabled,input[type=radio][disabled] {
    cursor: not-allowed
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 36px
}

.form-control-static.input-lg,.form-control-static.input-sm,.input-group-lg>.form-control-static.form-control,.input-group-lg>.form-control-static.input-group-addon,.input-group-lg>.input-group-btn>.form-control-static.btn,.input-group-sm>.form-control-static.form-control,.input-group-sm>.form-control-static.input-group-addon,.input-group-sm>.input-group-btn>.form-control-static.btn {
    padding-left: 0;
    padding-right: 0
}

.input-group-sm>.form-control,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.btn,.input-sm {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.input-group-sm>.input-group-btn>select.btn,.input-group-sm>select.form-control,.input-group-sm>select.input-group-addon,select.input-sm {
    height: 30px;
    line-height: 30px
}

.input-group-sm>.input-group-btn>select[multiple].btn,.input-group-sm>.input-group-btn>textarea.btn,.input-group-sm>select[multiple].form-control,.input-group-sm>select[multiple].input-group-addon,.input-group-sm>textarea.form-control,.input-group-sm>textarea.input-group-addon,select[multiple].input-sm,textarea.input-sm {
    height: auto
}

.form-group-sm .form-control {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.form-group-sm select.form-control {
    height: 30px;
    line-height: 30px
}

.form-group-sm select[multiple].form-control,.form-group-sm textarea.form-control {
    height: auto
}

.form-group-sm .form-control-static {
    height: 30px;
    min-height: 34px;
    padding: 6px 10px;
    font-size: 12px;
    line-height: 1.5
}

.input-group-lg>.form-control,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.btn,.input-lg {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px
}

.input-group-lg>.input-group-btn>select.btn,.input-group-lg>select.form-control,.input-group-lg>select.input-group-addon,select.input-lg {
    height: 46px;
    line-height: 46px
}

.input-group-lg>.input-group-btn>select[multiple].btn,.input-group-lg>.input-group-btn>textarea.btn,.input-group-lg>select[multiple].form-control,.input-group-lg>select[multiple].input-group-addon,.input-group-lg>textarea.form-control,.input-group-lg>textarea.input-group-addon,select[multiple].input-lg,textarea.input-lg {
    height: auto
}

.form-group-lg .form-control {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px
}

.form-group-lg select.form-control {
    height: 46px;
    line-height: 46px
}

.form-group-lg select[multiple].form-control,.form-group-lg textarea.form-control {
    height: auto
}

.form-group-lg .form-control-static {
    height: 46px;
    min-height: 40px;
    padding: 11px 16px;
    font-size: 18px;
    line-height: 1.3333333
}

.has-feedback {
    position: relative
}

.has-feedback .form-control {
    padding-right: 45px
}

.form-control-feedback {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    pointer-events: none
}

.form-group-lg .form-control+.form-control-feedback,.input-group-lg+.form-control-feedback,.input-group-lg>.form-control+.form-control-feedback,.input-group-lg>.input-group-addon+.form-control-feedback,.input-group-lg>.input-group-btn>.btn+.form-control-feedback,.input-lg+.form-control-feedback {
    width: 46px;
    height: 46px;
    line-height: 46px
}

.form-group-sm .form-control+.form-control-feedback,.input-group-sm+.form-control-feedback,.input-group-sm>.form-control+.form-control-feedback,.input-group-sm>.input-group-addon+.form-control-feedback,.input-group-sm>.input-group-btn>.btn+.form-control-feedback,.input-sm+.form-control-feedback {
    width: 30px;
    height: 30px;
    line-height: 30px
}

.has-success .checkbox,.has-success .checkbox-inline,.has-success.checkbox-inline label,.has-success.checkbox label,.has-success .control-label,.has-success .help-block,.has-success .radio,.has-success .radio-inline,.has-success.radio-inline label,.has-success.radio label {
    color: #4caf50
}

.has-success .form-control {
    border-color: #4caf50;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

.has-success .form-control:focus {
    border-color: #3d8b40;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #92cf94;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #92cf94
}

.has-success .input-group-addon {
    color: #4caf50;
    border-color: #4caf50;
    background-color: #dff0d8
}

.has-success .form-control-feedback {
    color: #4caf50
}

.has-warning .checkbox,.has-warning .checkbox-inline,.has-warning.checkbox-inline label,.has-warning.checkbox label,.has-warning .control-label,.has-warning .help-block,.has-warning .radio,.has-warning .radio-inline,.has-warning.radio-inline label,.has-warning.radio label {
    color: #a18c74
}

.has-warning .form-control {
    border-color: #a18c74;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

.has-warning .form-control:focus {
    border-color: #87725b;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #cabeb1;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #cabeb1
}

.has-warning .input-group-addon {
    color: #a18c74;
    border-color: #a18c74;
    background-color: #fcf8e3
}

.has-warning .form-control-feedback {
    color: #a18c74
}

.has-error .checkbox,.has-error .checkbox-inline,.has-error.checkbox-inline label,.has-error.checkbox label,.has-error .control-label,.has-error .help-block,.has-error .radio,.has-error .radio-inline,.has-error.radio-inline label,.has-error.radio label {
    color: #a94442
}

.has-error .form-control {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

.has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483
}

.has-error .input-group-addon {
    color: #a94442;
    border-color: #a94442;
    background-color: #f2dede
}

.has-error .form-control-feedback {
    color: #a94442
}

.has-feedback label~.form-control-feedback {
    top: 27px
}

.has-feedback label.sr-only~.form-control-feedback {
    top: 0
}

.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #a4aaae
}

@media (min-width: 768px) {
    .form-inline .form-group {
        display:inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .form-inline .form-control-static {
        display: inline-block
    }

    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle
    }

    .form-inline .input-group .form-control,.form-inline .input-group .input-group-addon,.form-inline .input-group .input-group-btn {
        width: auto
    }

    .form-inline .input-group>.form-control {
        width: 100%
    }

    .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .checkbox,.form-inline .radio {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .checkbox label,.form-inline .radio label {
        padding-left: 0
    }

    .form-inline .checkbox input[type=checkbox],.form-inline .radio input[type=radio] {
        position: relative;
        margin-left: 0
    }

    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.form-horizontal .checkbox,.form-horizontal .checkbox-inline,.form-horizontal .radio,.form-horizontal .radio-inline {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px
}

.form-horizontal .checkbox,.form-horizontal .radio {
    min-height: 29px
}

.form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px
}

.form-horizontal .form-group:after,.form-horizontal .form-group:before {
    content: " ";
    display: table
}

.form-horizontal .form-group:after {
    clear: both
}

@media (min-width: 768px) {
    .form-horizontal .control-label {
        text-align:right;
        margin-bottom: 0;
        padding-top: 7px
    }
}

.form-horizontal .has-feedback .form-control-feedback {
    right: 15px
}

@media (min-width: 768px) {
    .form-horizontal .form-group-lg .control-label {
        padding-top:11px;
        font-size: 18px
    }
}

@media (min-width: 768px) {
    .form-horizontal .form-group-sm .control-label {
        padding-top:6px;
        font-size: 12px
    }
}

.btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.6;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.btn.active.focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn:active:focus,.btn:focus {
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.btn.focus,.btn:focus,.btn:hover {
    color: #636b6f;
    text-decoration: none
}

.btn.active,.btn:active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125)
}

.btn.disabled,.btn[disabled],fieldset[disabled] .btn {
    cursor: not-allowed;
    opacity: .65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none
}

a.btn.disabled,fieldset[disabled] a.btn {
    pointer-events: none
}

.btn-default {
    color: #636b6f;
    background-color: #fff;
    border-color: #ccc
}

.btn-default.focus,.btn-default:focus {
    color: #636b6f;
    background-color: #e6e5e5;
    border-color: #8c8c8c
}

.btn-default.active,.btn-default:active,.btn-default:hover,.open>.btn-default.dropdown-toggle {
    color: #636b6f;
    background-color: #e6e5e5;
    border-color: #adadad
}

.btn-default.active.focus,.btn-default.active:focus,.btn-default.active:hover,.btn-default:active.focus,.btn-default:active:focus,.btn-default:active:hover,.open>.btn-default.dropdown-toggle.focus,.open>.btn-default.dropdown-toggle:focus,.open>.btn-default.dropdown-toggle:hover {
    color: #636b6f;
    background-color: #d4d4d4;
    border-color: #8c8c8c
}

.btn-default.active,.btn-default:active,.open>.btn-default.dropdown-toggle {
    background-image: none
}

.btn-default.disabled.focus,.btn-default.disabled:focus,.btn-default.disabled:hover,.btn-default[disabled].focus,.btn-default[disabled]:focus,.btn-default[disabled]:hover,fieldset[disabled] .btn-default.focus,fieldset[disabled] .btn-default:focus,fieldset[disabled] .btn-default:hover {
    background-color: #fff;
    border-color: #ccc
}

.btn-default .badge {
    color: #fff;
    background-color: #636b6f
}

.btn-primary {
    color: #fff;
    background-color: #3097d1;
    border-color: #2a88bd
}

.btn-primary.focus,.btn-primary:focus {
    color: #fff;
    background-color: #2579a9;
    border-color: #133d55
}

.btn-primary.active,.btn-primary:active,.btn-primary:hover,.open>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #2579a9;
    border-color: #1f648b
}

.btn-primary.active.focus,.btn-primary.active:focus,.btn-primary.active:hover,.btn-primary:active.focus,.btn-primary:active:focus,.btn-primary:active:hover,.open>.btn-primary.dropdown-toggle.focus,.open>.btn-primary.dropdown-toggle:focus,.open>.btn-primary.dropdown-toggle:hover {
    color: #fff;
    background-color: #1f648b;
    border-color: #133d55
}

.btn-primary.active,.btn-primary:active,.open>.btn-primary.dropdown-toggle {
    background-image: none
}

.btn-primary.disabled.focus,.btn-primary.disabled:focus,.btn-primary.disabled:hover,.btn-primary[disabled].focus,.btn-primary[disabled]:focus,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary.focus,fieldset[disabled] .btn-primary:focus,fieldset[disabled] .btn-primary:hover {
    background-color: #3097d1;
    border-color: #2a88bd
}

.btn-primary .badge {
    color: #3097d1;
    background-color: #fff
}

.btn-success {
    color: #fff;
    background-color: #2ab27b;
    border-color: #259d6d
}

.btn-success.focus,.btn-success:focus {
    color: #fff;
    background-color: #20895e;
    border-color: #0d3625
}

.btn-success.active,.btn-success:active,.btn-success:hover,.open>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #20895e;
    border-color: #196c4b
}

.btn-success.active.focus,.btn-success.active:focus,.btn-success.active:hover,.btn-success:active.focus,.btn-success:active:focus,.btn-success:active:hover,.open>.btn-success.dropdown-toggle.focus,.open>.btn-success.dropdown-toggle:focus,.open>.btn-success.dropdown-toggle:hover {
    color: #fff;
    background-color: #196c4b;
    border-color: #0d3625
}

.btn-success.active,.btn-success:active,.open>.btn-success.dropdown-toggle {
    background-image: none
}

.btn-success.disabled.focus,.btn-success.disabled:focus,.btn-success.disabled:hover,.btn-success[disabled].focus,.btn-success[disabled]:focus,.btn-success[disabled]:hover,fieldset[disabled] .btn-success.focus,fieldset[disabled] .btn-success:focus,fieldset[disabled] .btn-success:hover {
    background-color: #2ab27b;
    border-color: #259d6d
}

.btn-success .badge {
    color: #2ab27b;
    background-color: #fff
}

.btn-info {
    color: #fff;
    background-color: #8eb4cb;
    border-color: #7da8c3
}

.btn-info.focus,.btn-info:focus {
    color: #fff;
    background-color: #6b9dbb;
    border-color: #3d6983
}

.btn-info.active,.btn-info:active,.btn-info:hover,.open>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #6b9dbb;
    border-color: #538db0
}

.btn-info.active.focus,.btn-info.active:focus,.btn-info.active:hover,.btn-info:active.focus,.btn-info:active:focus,.btn-info:active:hover,.open>.btn-info.dropdown-toggle.focus,.open>.btn-info.dropdown-toggle:focus,.open>.btn-info.dropdown-toggle:hover {
    color: #fff;
    background-color: #538db0;
    border-color: #3d6983
}

.btn-info.active,.btn-info:active,.open>.btn-info.dropdown-toggle {
    background-image: none
}

.btn-info.disabled.focus,.btn-info.disabled:focus,.btn-info.disabled:hover,.btn-info[disabled].focus,.btn-info[disabled]:focus,.btn-info[disabled]:hover,fieldset[disabled] .btn-info.focus,fieldset[disabled] .btn-info:focus,fieldset[disabled] .btn-info:hover {
    background-color: #8eb4cb;
    border-color: #7da8c3
}

.btn-info .badge {
    color: #8eb4cb;
    background-color: #fff
}

.btn-warning {
    color: #fff;
    background-color: #feab3a;
    border-color: #fea021
}

.btn-warning.focus,.btn-warning:focus {
    color: #fff;
    background-color: #fe9507;
    border-color: #9e5c01
}

.btn-warning.active,.btn-warning:active,.btn-warning:hover,.open>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #fe9507;
    border-color: #e08201
}

.btn-warning.active.focus,.btn-warning.active:focus,.btn-warning.active:hover,.btn-warning:active.focus,.btn-warning:active:focus,.btn-warning:active:hover,.open>.btn-warning.dropdown-toggle.focus,.open>.btn-warning.dropdown-toggle:focus,.open>.btn-warning.dropdown-toggle:hover {
    color: #fff;
    background-color: #e08201;
    border-color: #9e5c01
}

.btn-warning.active,.btn-warning:active,.open>.btn-warning.dropdown-toggle {
    background-image: none
}

.btn-warning.disabled.focus,.btn-warning.disabled:focus,.btn-warning.disabled:hover,.btn-warning[disabled].focus,.btn-warning[disabled]:focus,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning.focus,fieldset[disabled] .btn-warning:focus,fieldset[disabled] .btn-warning:hover {
    background-color: #feab3a;
    border-color: #fea021
}

.btn-warning .badge {
    color: #feab3a;
    background-color: #fff
}

.btn-danger {
    color: #fff;
    background-color: #bf5329;
    border-color: #aa4a24
}

.btn-danger.focus,.btn-danger:focus {
    color: #fff;
    background-color: #954120;
    border-color: #411c0e
}

.btn-danger.active,.btn-danger:active,.btn-danger:hover,.open>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #954120;
    border-color: #78341a
}

.btn-danger.active.focus,.btn-danger.active:focus,.btn-danger.active:hover,.btn-danger:active.focus,.btn-danger:active:focus,.btn-danger:active:hover,.open>.btn-danger.dropdown-toggle.focus,.open>.btn-danger.dropdown-toggle:focus,.open>.btn-danger.dropdown-toggle:hover {
    color: #fff;
    background-color: #78341a;
    border-color: #411c0e
}

.btn-danger.active,.btn-danger:active,.open>.btn-danger.dropdown-toggle {
    background-image: none
}

.btn-danger.disabled.focus,.btn-danger.disabled:focus,.btn-danger.disabled:hover,.btn-danger[disabled].focus,.btn-danger[disabled]:focus,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger.focus,fieldset[disabled] .btn-danger:focus,fieldset[disabled] .btn-danger:hover {
    background-color: #bf5329;
    border-color: #aa4a24
}

.btn-danger .badge {
    color: #bf5329;
    background-color: #fff
}

.btn-link {
    color: #3097d1;
    font-weight: 400;
    border-radius: 0
}

.btn-link,.btn-link.active,.btn-link:active,.btn-link[disabled],fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.btn-link,.btn-link:active,.btn-link:focus,.btn-link:hover {
    border-color: transparent
}

.btn-link:focus,.btn-link:hover {
    color: #216a94;
    text-decoration: underline;
    background-color: transparent
}

.btn-link[disabled]:focus,.btn-link[disabled]:hover,fieldset[disabled] .btn-link:focus,fieldset[disabled] .btn-link:hover {
    color: #777;
    text-decoration: none
}

.btn-group-lg>.btn,.btn-lg {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px
}

.btn-group-sm>.btn,.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.btn-group-xs>.btn,.btn-xs {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: 5px
}

input[type=button].btn-block,input[type=reset].btn-block,input[type=submit].btn-block {
    width: 100%
}

.fade {
    opacity: 0;
    -webkit-transition: opacity .15s linear;
    transition: opacity .15s linear
}

.fade.in {
    opacity: 1
}

.collapse {
    display: none
}

.collapse.in {
    display: block
}

tr.collapse.in {
    display: table-row
}

tbody.collapse.in {
    display: table-row-group
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-property: height,visibility;
    transition-property: height,visibility;
    -webkit-transition-duration: .35s;
    transition-duration: .35s;
    -webkit-transition-timing-function: ease;
    transition-timing-function: ease
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid\9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.dropdown,.dropup {
    position: relative
}

.dropdown-toggle:focus {
    outline: 0
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
    background-clip: padding-box
}

.dropdown-menu.pull-right {
    right: 0;
    left: auto
}

.dropdown-menu .divider {
    height: 1px;
    margin: 10px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.6;
    color: #333;
    white-space: nowrap
}

.dropdown-menu>li>a:focus,.dropdown-menu>li>a:hover {
    text-decoration: none;
    color: #262626;
    background-color: #f5f5f5
}

.dropdown-menu>.active>a,.dropdown-menu>.active>a:focus,.dropdown-menu>.active>a:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    background-color: #3097d1
}

.dropdown-menu>.disabled>a,.dropdown-menu>.disabled>a:focus,.dropdown-menu>.disabled>a:hover {
    color: #777
}

.dropdown-menu>.disabled>a:focus,.dropdown-menu>.disabled>a:hover {
    text-decoration: none;
    background-color: transparent;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    cursor: not-allowed
}

.open>.dropdown-menu {
    display: block
}

.open>a {
    outline: 0
}

.dropdown-menu-right {
    left: auto;
    right: 0
}

.dropdown-menu-left {
    left: 0;
    right: auto
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 12px;
    line-height: 1.6;
    color: #777;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 990
}

.pull-right>.dropdown-menu {
    right: 0;
    left: auto
}

.dropup .caret,.navbar-fixed-bottom .dropdown .caret {
    border-top: 0;
    border-bottom: 4px dashed;
    border-bottom: 4px solid\9;
    content: ""
}

.dropup .dropdown-menu,.navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 2px
}

@media (min-width: 768px) {
    .navbar-right .dropdown-menu {
        right:0;
        left: auto
    }

    .navbar-right .dropdown-menu-left {
        left: 0;
        right: auto
    }
}

.btn-group,.btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.btn-group-vertical>.btn,.btn-group>.btn {
    position: relative;
    float: left
}

.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:hover,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus,.btn-group>.btn:hover {
    z-index: 2
}

.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    margin-left: -5px
}

.btn-toolbar:after,.btn-toolbar:before {
    content: " ";
    display: table
}

.btn-toolbar:after {
    clear: both
}

.btn-toolbar .btn,.btn-toolbar .btn-group,.btn-toolbar .input-group {
    float: left
}

.btn-toolbar>.btn,.btn-toolbar>.btn-group,.btn-toolbar>.input-group {
    margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child {
    margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group .dropdown-toggle:active,.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn-group>.btn+.dropdown-toggle {
    padding-left: 8px;
    padding-right: 8px
}

.btn-group-lg.btn-group>.btn+.dropdown-toggle,.btn-group>.btn-lg+.dropdown-toggle {
    padding-left: 12px;
    padding-right: 12px
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125)
}

.btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none
}

.btn .caret {
    margin-left: 0
}

.btn-group-lg>.btn .caret,.btn-lg .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0
}

.dropup .btn-group-lg>.btn .caret,.dropup .btn-lg .caret {
    border-width: 0 5px 5px
}

.btn-group-vertical>.btn,.btn-group-vertical>.btn-group,.btn-group-vertical>.btn-group>.btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%
}

.btn-group-vertical>.btn-group:after,.btn-group-vertical>.btn-group:before {
    content: " ";
    display: table
}

.btn-group-vertical>.btn-group:after {
    clear: both
}

.btn-group-vertical>.btn-group>.btn {
    float: none
}

.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate
}

.btn-group-justified>.btn,.btn-group-justified>.btn-group {
    float: none;
    display: table-cell;
    width: 1%
}

.btn-group-justified>.btn-group .btn {
    width: 100%
}

.btn-group-justified>.btn-group .dropdown-menu {
    left: auto
}

[data-toggle=buttons]>.btn-group>.btn input[type=checkbox],[data-toggle=buttons]>.btn-group>.btn input[type=radio],[data-toggle=buttons]>.btn input[type=checkbox],[data-toggle=buttons]>.btn input[type=radio] {
    position: absolute;
    clip: rect(0,0,0,0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate
}

.input-group[class*=col-] {
    float: none;
    padding-left: 0;
    padding-right: 0
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0
}

.input-group .form-control:focus {
    z-index: 3
}

.input-group-addon,.input-group-btn,.input-group .form-control {
    display: table-cell
}

.input-group-addon:not(:first-child):not(:last-child),.input-group-btn:not(:first-child):not(:last-child),.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0
}

.input-group-addon,.input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: #555;
    text-align: center;
    background-color: #eee;
    border: 1px solid #ccd0d2;
    border-radius: 4px
}

.input-group-addon.input-sm,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.input-group-addon.btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px
}

.input-group-addon.input-lg,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.input-group-addon.btn {
    padding: 10px 16px;
    font-size: 18px;
    border-radius: 6px
}

.input-group-addon input[type=checkbox],.input-group-addon input[type=radio] {
    margin-top: 0
}

.input-group-addon:first-child,.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group>.btn,.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn-group:not(:last-child)>.btn,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group .form-control:first-child {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group-addon:first-child {
    border-right: 0
}

.input-group-addon:last-child,.input-group-btn:first-child>.btn-group:not(:first-child)>.btn,.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group>.btn,.input-group-btn:last-child>.dropdown-toggle,.input-group .form-control:last-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group-addon:last-child {
    border-left: 0
}

.input-group-btn {
    font-size: 0;
    white-space: nowrap
}

.input-group-btn,.input-group-btn>.btn {
    position: relative
}

.input-group-btn>.btn+.btn {
    margin-left: -1px
}

.input-group-btn>.btn:active,.input-group-btn>.btn:focus,.input-group-btn>.btn:hover {
    z-index: 2
}

.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group {
    margin-right: -1px
}

.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group {
    z-index: 2;
    margin-left: -1px
}

.nav {
    margin-bottom: 0;
    padding-left: 0;
    list-style: none
}

.nav:after,.nav:before {
    content: " ";
    display: table
}

.nav:after {
    clear: both
}

.nav>li,.nav>li>a {
    position: relative;
    display: block
}

.nav>li>a {
    padding: 10px 15px
}

.nav>li>a:focus,.nav>li>a:hover {
    text-decoration: none;
    background-color: #eee
}

.nav>li.disabled>a {
    color: #777
}

.nav>li.disabled>a:focus,.nav>li.disabled>a:hover {
    color: #777;
    text-decoration: none;
    background-color: transparent;
    cursor: not-allowed
}

.nav .open>a,.nav .open>a:focus,.nav .open>a:hover {
    background-color: #eee;
    border-color: #3097d1
}

.nav .nav-divider {
    height: 1px;
    margin: 10px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.nav>li>a>img {
    max-width: none
}

.nav-tabs {
    border-bottom: 1px solid #ddd
}

.nav-tabs>li {
    float: left;
    margin-bottom: -1px
}

.nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.6;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0
}

.nav-tabs>li>a:hover {
    border-color: #eee #eee #ddd
}

.nav-tabs>li.active>a,.nav-tabs>li.active>a:focus,.nav-tabs>li.active>a:hover {
    color: #555;
    background-color: #f5f8fa;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
    cursor: default
}

.nav-pills>li {
    float: left
}

.nav-pills>li>a {
    border-radius: 4px
}

.nav-pills>li+li {
    margin-left: 2px
}

.nav-pills>li.active>a,.nav-pills>li.active>a:focus,.nav-pills>li.active>a:hover {
    color: #fff;
    background-color: #3097d1
}

.nav-stacked>li {
    float: none
}

.nav-stacked>li+li {
    margin-top: 2px;
    margin-left: 0
}

.nav-justified,.nav-tabs.nav-justified {
    width: 100%
}

.nav-justified>li,.nav-tabs.nav-justified>li {
    float: none
}

.nav-justified>li>a,.nav-tabs.nav-justified>li>a {
    text-align: center;
    margin-bottom: 5px
}

.nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto
}

@media (min-width: 768px) {
    .nav-justified>li,.nav-tabs.nav-justified>li {
        display:table-cell;
        width: 1%
    }

    .nav-justified>li>a,.nav-tabs.nav-justified>li>a {
        margin-bottom: 0
    }
}

.nav-tabs-justified,.nav-tabs.nav-justified {
    border-bottom: 0
}

.nav-tabs-justified>li>a,.nav-tabs.nav-justified>li>a {
    margin-right: 0;
    border-radius: 4px
}

.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:focus,.nav-tabs-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:focus,.nav-tabs.nav-justified>.active>a:hover {
    border: 1px solid #ddd
}

@media (min-width: 768px) {
    .nav-tabs-justified>li>a,.nav-tabs.nav-justified>li>a {
        border-bottom:1px solid #ddd;
        border-radius: 4px 4px 0 0
    }

    .nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:focus,.nav-tabs-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:focus,.nav-tabs.nav-justified>.active>a:hover {
        border-bottom-color: #f5f8fa
    }
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.navbar {
    position: relative;
    min-height: 50px;
    margin-bottom: 22px;
    border: 1px solid transparent
}

.navbar:after,.navbar:before {
    content: " ";
    display: table
}

.navbar:after {
    clear: both
}

@media (min-width: 768px) {
    .navbar {
        border-radius:4px
    }
}

.navbar-header:after,.navbar-header:before {
    content: " ";
    display: table
}

.navbar-header:after {
    clear: both
}

@media (min-width: 768px) {
    .navbar-header {
        float:left
    }
}

.navbar-collapse {
    overflow-x: visible;
    padding-right: 15px;
    padding-left: 15px;
    border-top: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1);
    box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1);
    -webkit-overflow-scrolling: touch
}

.navbar-collapse:after,.navbar-collapse:before {
    content: " ";
    display: table
}

.navbar-collapse:after {
    clear: both
}

.navbar-collapse.in {
    overflow-y: auto
}

@media (min-width: 768px) {
    .navbar-collapse {
        width:auto;
        border-top: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .navbar-collapse.collapse {
        display: block!important;
        height: auto!important;
        padding-bottom: 0;
        overflow: visible!important
    }

    .navbar-collapse.in {
        overflow-y: visible
    }

    .navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse,.navbar-static-top .navbar-collapse {
        padding-left: 0;
        padding-right: 0
    }
}

.navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse {
    max-height: 340px
}

@media (max-device-width: 480px) and (orientation:landscape) {
    .navbar-fixed-bottom .navbar-collapse,.navbar-fixed-top .navbar-collapse {
        max-height:200px
    }
}

.container-fluid>.navbar-collapse,.container-fluid>.navbar-header,.container>.navbar-collapse,.container>.navbar-header {
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width: 768px) {
    .container-fluid>.navbar-collapse,.container-fluid>.navbar-header,.container>.navbar-collapse,.container>.navbar-header {
        margin-right:0;
        margin-left: 0
    }
}

.navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px
}

@media (min-width: 768px) {
    .navbar-static-top {
        border-radius:0
    }
}

.navbar-fixed-bottom,.navbar-fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030
}

@media (min-width: 768px) {
    .navbar-fixed-bottom,.navbar-fixed-top {
        border-radius:0
    }
}

.navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px
}

.navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0
}

.navbar-brand {
    float: left;
    padding: 14px 15px;
    font-size: 18px;
    line-height: 22px;
    height: 50px
}

.navbar-brand:focus,.navbar-brand:hover {
    text-decoration: none
}

.navbar-brand>img {
    display: block
}

@media (min-width: 768px) {
    .navbar>.container-fluid .navbar-brand,.navbar>.container .navbar-brand {
        margin-left:-15px
    }
}

.navbar-toggle {
    position: relative;
    float: right;
    margin-right: 15px;
    padding: 9px 10px;
    margin-top: 8px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px
}

.navbar-toggle:focus {
    outline: 0
}

.navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px
}

.navbar-toggle .icon-bar+.icon-bar {
    margin-top: 4px
}

@media (min-width: 768px) {
    .navbar-toggle {
        display:none
    }
}

.navbar-nav {
    margin: 7px -15px
}

.navbar-nav>li>a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 22px
}

@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {
        position:static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .navbar-nav .open .dropdown-menu .dropdown-header,.navbar-nav .open .dropdown-menu>li>a {
        padding: 5px 15px 5px 25px
    }

    .navbar-nav .open .dropdown-menu>li>a {
        line-height: 22px
    }

    .navbar-nav .open .dropdown-menu>li>a:focus,.navbar-nav .open .dropdown-menu>li>a:hover {
        background-image: none
    }
}

@media (min-width: 768px) {
    .navbar-nav {
        float:left;
        margin: 0
    }

    .navbar-nav>li {
        float: left
    }

    .navbar-nav>li>a {
        padding-top: 14px;
        padding-bottom: 14px
    }
}

.navbar-form {
    margin: 7px -15px;
    padding: 10px 15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1),0 1px 0 hsla(0,0%,100%,.1);
    box-shadow: inset 0 1px 0 hsla(0,0%,100%,.1),0 1px 0 hsla(0,0%,100%,.1)
}

@media (min-width: 768px) {
    .navbar-form .form-group {
        display:inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .navbar-form .form-control-static {
        display: inline-block
    }

    .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle
    }

    .navbar-form .input-group .form-control,.navbar-form .input-group .input-group-addon,.navbar-form .input-group .input-group-btn {
        width: auto
    }

    .navbar-form .input-group>.form-control {
        width: 100%
    }

    .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .checkbox,.navbar-form .radio {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .checkbox label,.navbar-form .radio label {
        padding-left: 0
    }

    .navbar-form .checkbox input[type=checkbox],.navbar-form .radio input[type=radio] {
        position: relative;
        margin-left: 0
    }

    .navbar-form .has-feedback .form-control-feedback {
        top: 0
    }
}

@media (max-width: 767px) {
    .navbar-form .form-group {
        margin-bottom:5px
    }

    .navbar-form .form-group:last-child {
        margin-bottom: 0
    }
}

@media (min-width: 768px) {
    .navbar-form {
        width:auto;
        border: 0;
        margin-left: 0;
        margin-right: 0;
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

.navbar-nav>li>.dropdown-menu {
    margin-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
    margin-bottom: 0;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.navbar-btn {
    margin-top: 7px;
    margin-bottom: 7px
}

.btn-group-sm>.navbar-btn.btn,.navbar-btn.btn-sm {
    margin-top: 10px;
    margin-bottom: 10px
}

.btn-group-xs>.navbar-btn.btn,.navbar-btn.btn-xs,.navbar-text {
    margin-top: 14px;
    margin-bottom: 14px
}

@media (min-width: 768px) {
    .navbar-text {
        float:left;
        margin-left: 15px;
        margin-right: 15px
    }
}

@media (min-width: 768px) {
    .navbar-left {
        float:left!important
    }

    .navbar-right {
        float: right!important;
        margin-right: -15px
    }

    .navbar-right~.navbar-right {
        margin-right: 0
    }
}

.navbar-default {
    border-color: #d3e0e9
}

.navbar-default .navbar-brand {
    color: #777
}

.navbar-default .navbar-brand:focus,.navbar-default .navbar-brand:hover {
    color: #5e5d5d;
    background-color: transparent
}

.navbar-default .navbar-nav>li>a,.navbar-default .navbar-text {
    color: #777
}

.navbar-default .navbar-nav>li>a:focus,.navbar-default .navbar-nav>li>a:hover {
    color: #333;
    background-color: transparent
}

.navbar-default .navbar-nav>.active>a,.navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>.active>a:hover {
    color: #555;
    background-color: #eee
}

.navbar-default .navbar-nav>.disabled>a,.navbar-default .navbar-nav>.disabled>a:focus,.navbar-default .navbar-nav>.disabled>a:hover {
    color: #ccc;
    background-color: transparent
}

.navbar-default .navbar-toggle {
    border-color: #ddd
}

.navbar-default .navbar-toggle:focus,.navbar-default .navbar-toggle:hover {
    background-color: #ddd
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: #888
}

.navbar-default .navbar-collapse,.navbar-default .navbar-form {
    border-color: #d3e0e9
}

.navbar-default .navbar-nav>.open>a,.navbar-default .navbar-nav>.open>a:focus,.navbar-default .navbar-nav>.open>a:hover {
    background-color: #eee;
    color: #555
}

@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu>li>a {
        color:#777
    }

    .navbar-default .navbar-nav .open .dropdown-menu>li>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
        color: #333;
        background-color: transparent
    }

    .navbar-default .navbar-nav .open .dropdown-menu>.active>a,.navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover {
        color: #555;
        background-color: #eee
    }

    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus,.navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover {
        color: #ccc;
        background-color: transparent
    }
}

.navbar-default .navbar-link {
    color: #777
}

.navbar-default .navbar-link:hover {
    color: #333
}

.navbar-default .btn-link {
    color: #777
}

.navbar-default .btn-link:focus,.navbar-default .btn-link:hover {
    color: #333
}

.navbar-default .btn-link[disabled]:focus,.navbar-default .btn-link[disabled]:hover,fieldset[disabled] .navbar-default .btn-link:focus,fieldset[disabled] .navbar-default .btn-link:hover {
    color: #ccc
}

.navbar-inverse {
    background-color: #222;
    border-color: #090909
}

.navbar-inverse .navbar-brand {
    color: #9d9d9d
}

.navbar-inverse .navbar-brand:focus,.navbar-inverse .navbar-brand:hover {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-nav>li>a,.navbar-inverse .navbar-text {
    color: #9d9d9d
}

.navbar-inverse .navbar-nav>li>a:focus,.navbar-inverse .navbar-nav>li>a:hover {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-nav>.active>a,.navbar-inverse .navbar-nav>.active>a:focus,.navbar-inverse .navbar-nav>.active>a:hover {
    color: #fff;
    background-color: #090909
}

.navbar-inverse .navbar-nav>.disabled>a,.navbar-inverse .navbar-nav>.disabled>a:focus,.navbar-inverse .navbar-nav>.disabled>a:hover {
    color: #444;
    background-color: transparent
}

.navbar-inverse .navbar-toggle {
    border-color: #333
}

.navbar-inverse .navbar-toggle:focus,.navbar-inverse .navbar-toggle:hover {
    background-color: #333
}

.navbar-inverse .navbar-toggle .icon-bar {
    background-color: #fff
}

.navbar-inverse .navbar-collapse,.navbar-inverse .navbar-form {
    border-color: #101010
}

.navbar-inverse .navbar-nav>.open>a,.navbar-inverse .navbar-nav>.open>a:focus,.navbar-inverse .navbar-nav>.open>a:hover {
    background-color: #090909;
    color: #fff
}

@media (max-width: 767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
        border-color:#090909
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
        background-color: #090909
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
        color: #9d9d9d
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover {
        color: #fff;
        background-color: transparent
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover {
        color: #fff;
        background-color: #090909
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus,.navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover {
        color: #444;
        background-color: transparent
    }
}

.navbar-inverse .navbar-link {
    color: #9d9d9d
}

.navbar-inverse .navbar-link:hover {
    color: #fff
}

.navbar-inverse .btn-link {
    color: #9d9d9d
}

.navbar-inverse .btn-link:focus,.navbar-inverse .btn-link:hover {
    color: #fff
}

.navbar-inverse .btn-link[disabled]:focus,.navbar-inverse .btn-link[disabled]:hover,fieldset[disabled] .navbar-inverse .btn-link:focus,fieldset[disabled] .navbar-inverse .btn-link:hover {
    color: #444
}

.breadcrumb {
    padding: 8px 15px;
    margin-bottom: 22px;
    list-style: none;
    background-color: #f5f5f5;
    border-radius: 4px
}

.breadcrumb>li {
    display: inline-block
}

.breadcrumb>li+li:before {
    content: "/\A0";
    padding: 0 5px;
    color: #ccc
}

.breadcrumb>.active {
    color: #777
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 22px 0;
    border-radius: 4px
}

.pagination>li {
    display: inline
}

.pagination>li>a,.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    line-height: 1.6;
    text-decoration: none;
    color: #3097d1;
    background-color: #fff;
    border: 1px solid #ddd;
    margin-left: -1px
}

.pagination>li:first-child>a,.pagination>li:first-child>span {
    margin-left: 0;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px
}

.pagination>li:last-child>a,.pagination>li:last-child>span {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px
}

.pagination>li>a:focus,.pagination>li>a:hover,.pagination>li>span:focus,.pagination>li>span:hover {
    z-index: 2;
    color: #216a94;
    background-color: #eee;
    border-color: #ddd
}

.pagination>.active>a,.pagination>.active>a:focus,.pagination>.active>a:hover,.pagination>.active>span,.pagination>.active>span:focus,.pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    background-color: #3097d1;
    border-color: #3097d1;
    cursor: default
}

.pagination>.disabled>a,.pagination>.disabled>a:focus,.pagination>.disabled>a:hover,.pagination>.disabled>span,.pagination>.disabled>span:focus,.pagination>.disabled>span:hover {
    color: #777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed
}

.pagination-lg>li>a,.pagination-lg>li>span {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333
}

.pagination-lg>li:first-child>a,.pagination-lg>li:first-child>span {
    border-bottom-left-radius: 6px;
    border-top-left-radius: 6px
}

.pagination-lg>li:last-child>a,.pagination-lg>li:last-child>span {
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px
}

.pagination-sm>li>a,.pagination-sm>li>span {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5
}

.pagination-sm>li:first-child>a,.pagination-sm>li:first-child>span {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px
}

.pagination-sm>li:last-child>a,.pagination-sm>li:last-child>span {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px
}

.pager {
    padding-left: 0;
    margin: 22px 0;
    list-style: none;
    text-align: center
}

.pager:after,.pager:before {
    content: " ";
    display: table
}

.pager:after {
    clear: both
}

.pager li {
    display: inline
}

.pager li>a,.pager li>span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px
}

.pager li>a:focus,.pager li>a:hover {
    text-decoration: none;
    background-color: #eee
}

.pager .next>a,.pager .next>span {
    float: right
}

.pager .previous>a,.pager .previous>span {
    float: left
}

.pager .disabled>a,.pager .disabled>a:focus,.pager .disabled>a:hover,.pager .disabled>span {
    color: #777;
    background-color: #fff;
    cursor: not-allowed
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.label:empty {
    display: none
}

.btn .label {
    position: relative;
    top: -1px
}

a.label:focus,a.label:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.label-default {
    background-color: #777
}

.label-default[href]:focus,.label-default[href]:hover {
    background-color: #5e5e5e
}

.label-primary {
    background-color: #3097d1
}

.label-primary[href]:focus,.label-primary[href]:hover {
    background-color: #2579a9
}

.label-success {
    background-color: #2ab27b
}

.label-success[href]:focus,.label-success[href]:hover {
    background-color: #20895e
}

.label-info {
    background-color: #8eb4cb
}

.label-info[href]:focus,.label-info[href]:hover {
    background-color: #6b9dbb
}

.label-warning {
    background-color: #feab3a
}

.label-warning[href]:focus,.label-warning[href]:hover {
    background-color: #fe9507
}

.label-danger {
    background-color: #bf5329
}

.label-danger[href]:focus,.label-danger[href]:hover {
    background-color: #954120
}

.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    line-height: 1;
    vertical-align: middle;
    white-space: nowrap;
    text-align: center;
    background-color: #777;
    border-radius: 10px
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.btn-group-xs>.btn .badge,.btn-xs .badge {
    top: 0;
    padding: 1px 5px
}

.list-group-item.active>.badge,.nav-pills>.active>a>.badge {
    color: #3097d1;
    background-color: #fff
}

.list-group-item>.badge {
    float: right
}

.list-group-item>.badge+.badge {
    margin-right: 5px
}

.nav-pills>li>a>.badge {
    margin-left: 3px
}

a.badge:focus,a.badge:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.jumbotron {
    padding-top: 30px;
    padding-bottom: 30px;
    margin-bottom: 30px;
    background-color: #eee
}

.jumbotron,.jumbotron .h1,.jumbotron h1 {
    color: inherit
}

.jumbotron p {
    margin-bottom: 15px;
    font-size: 21px;
    font-weight: 200
}

.jumbotron>hr {
    border-top-color: #d5d5d5
}

.container-fluid .jumbotron,.container .jumbotron {
    border-radius: 6px;
    padding-left: 15px;
    padding-right: 15px
}

.jumbotron .container {
    max-width: 100%
}

@media screen and (min-width: 768px) {
    .jumbotron {
        padding-top:48px;
        padding-bottom: 48px
    }

    .container-fluid .jumbotron,.container .jumbotron {
        padding-left: 60px;
        padding-right: 60px
    }

    .jumbotron .h1,.jumbotron h1 {
        font-size: 63px
    }
}

.thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 22px;
    line-height: 1.6;
    background-color: #f5f8fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-transition: border .2s ease-in-out;
    transition: border .2s ease-in-out
}

.thumbnail>img,.thumbnail a>img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto
}

.thumbnail .caption {
    padding: 9px;
    color: #636b6f
}

a.thumbnail.active,a.thumbnail:focus,a.thumbnail:hover {
    border-color: #3097d1
}

.alert {
    padding: 15px;
    margin-bottom: 22px;
    border: 1px solid transparent;
    border-radius: 4px
}

.alert h4 {
    margin-top: 0;
    color: inherit
}

.alert .alert-link {
    font-weight: 700
}

.alert>p,.alert>ul {
    margin-bottom: 0
}

.alert>p+p {
    margin-top: 5px
}

.alert-dismissable,.alert-dismissible {
    padding-right: 35px
}

.alert-dismissable .close,.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #4caf50
}

.alert-success hr {
    border-top-color: #c9e2b3
}

.alert-success .alert-link {
    color: #3d8b40
}

.alert-info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f
}

.alert-info hr {
    border-top-color: #a6e1ec
}

.alert-info .alert-link {
    color: #245269
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #a18c74
}

.alert-warning hr {
    border-top-color: #f7e1b5
}

.alert-warning .alert-link {
    color: #87725b
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442
}

.alert-danger hr {
    border-top-color: #e4b9c0
}

.alert-danger .alert-link {
    color: #843534
}

@-webkit-keyframes progress-bar-stripes {
    0% {
        background-position: 40px 0
    }

    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 40px 0
    }

    to {
        background-position: 0 0
    }
}

.progress {
    overflow: hidden;
    height: 22px;
    margin-bottom: 22px;
    background-color: #f5f5f5;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1)
}

.progress-bar {
    float: left;
    width: 0;
    height: 100%;
    font-size: 12px;
    line-height: 22px;
    color: #fff;
    text-align: center;
    background-color: #3097d1;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    -webkit-transition: width .6s ease;
    transition: width .6s ease
}

.progress-bar-striped,.progress-striped .progress-bar {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);
    background-size: 40px 40px
}

.progress-bar.active,.progress.active .progress-bar {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite
}

.progress-bar-success {
    background-color: #2ab27b
}

.progress-striped .progress-bar-success {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent)
}

.progress-bar-info {
    background-color: #8eb4cb
}

.progress-striped .progress-bar-info {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent)
}

.progress-bar-warning {
    background-color: #feab3a
}

.progress-striped .progress-bar-warning {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent)
}

.progress-bar-danger {
    background-color: #bf5329
}

.progress-striped .progress-bar-danger {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent)
}

.media {
    margin-top: 15px
}

.media:first-child {
    margin-top: 0
}

.media,.media-body {
    zoom:1;overflow: hidden
}

.media-body {
    width: 10000px
}

.media-object {
    display: block
}

.media-object.img-thumbnail {
    max-width: none
}

.media-right,.media>.pull-right {
    padding-left: 10px
}

.media-left,.media>.pull-left {
    padding-right: 10px
}

.media-body,.media-left,.media-right {
    display: table-cell;
    vertical-align: top
}

.media-middle {
    vertical-align: middle
}

.media-bottom {
    vertical-align: bottom
}

.media-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.media-list {
    padding-left: 0;
    list-style: none
}

.list-group {
    margin-bottom: 20px;
    padding-left: 0
}

.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #d3e0e9
}

.list-group-item:first-child {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}

a.list-group-item,button.list-group-item {
    color: #555
}

a.list-group-item .list-group-item-heading,button.list-group-item .list-group-item-heading {
    color: #333
}

a.list-group-item:focus,a.list-group-item:hover,button.list-group-item:focus,button.list-group-item:hover {
    text-decoration: none;
    color: #555;
    background-color: #f5f5f5
}

button.list-group-item {
    width: 100%;
    text-align: left
}

.list-group-item.disabled,.list-group-item.disabled:focus,.list-group-item.disabled:hover {
    background-color: #eee;
    color: #777;
    cursor: not-allowed
}

.list-group-item.disabled .list-group-item-heading,.list-group-item.disabled:focus .list-group-item-heading,.list-group-item.disabled:hover .list-group-item-heading {
    color: inherit
}

.list-group-item.disabled .list-group-item-text,.list-group-item.disabled:focus .list-group-item-text,.list-group-item.disabled:hover .list-group-item-text {
    color: #777
}

.list-group-item.active,.list-group-item.active:focus,.list-group-item.active:hover {
    z-index: 2;
    color: #fff;
    background-color: #3097d1;
    border-color: #3097d1
}

.list-group-item.active .list-group-item-heading,.list-group-item.active .list-group-item-heading>.small,.list-group-item.active .list-group-item-heading>small,.list-group-item.active:focus .list-group-item-heading,.list-group-item.active:focus .list-group-item-heading>.small,.list-group-item.active:focus .list-group-item-heading>small,.list-group-item.active:hover .list-group-item-heading,.list-group-item.active:hover .list-group-item-heading>.small,.list-group-item.active:hover .list-group-item-heading>small {
    color: inherit
}

.list-group-item.active .list-group-item-text,.list-group-item.active:focus .list-group-item-text,.list-group-item.active:hover .list-group-item-text {
    color: #d7ebf6
}

.list-group-item-success {
    color: #4caf50;
    background-color: #dff0d8
}

a.list-group-item-success,button.list-group-item-success {
    color: #4caf50
}

a.list-group-item-success .list-group-item-heading,button.list-group-item-success .list-group-item-heading {
    color: inherit
}

a.list-group-item-success:focus,a.list-group-item-success:hover,button.list-group-item-success:focus,button.list-group-item-success:hover {
    color: #4caf50;
    background-color: #d0e9c6
}

a.list-group-item-success.active,a.list-group-item-success.active:focus,a.list-group-item-success.active:hover,button.list-group-item-success.active,button.list-group-item-success.active:focus,button.list-group-item-success.active:hover {
    color: #fff;
    background-color: #4caf50;
    border-color: #4caf50
}

.list-group-item-info {
    color: #31708f;
    background-color: #d9edf7
}

a.list-group-item-info,button.list-group-item-info {
    color: #31708f
}

a.list-group-item-info .list-group-item-heading,button.list-group-item-info .list-group-item-heading {
    color: inherit
}

a.list-group-item-info:focus,a.list-group-item-info:hover,button.list-group-item-info:focus,button.list-group-item-info:hover {
    color: #31708f;
    background-color: #c4e3f3
}

a.list-group-item-info.active,a.list-group-item-info.active:focus,a.list-group-item-info.active:hover,button.list-group-item-info.active,button.list-group-item-info.active:focus,button.list-group-item-info.active:hover {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f
}

.list-group-item-warning {
    color: #a18c74;
    background-color: #fcf8e3
}

a.list-group-item-warning,button.list-group-item-warning {
    color: #a18c74
}

a.list-group-item-warning .list-group-item-heading,button.list-group-item-warning .list-group-item-heading {
    color: inherit
}

a.list-group-item-warning:focus,a.list-group-item-warning:hover,button.list-group-item-warning:focus,button.list-group-item-warning:hover {
    color: #a18c74;
    background-color: #faf2cc
}

a.list-group-item-warning.active,a.list-group-item-warning.active:focus,a.list-group-item-warning.active:hover,button.list-group-item-warning.active,button.list-group-item-warning.active:focus,button.list-group-item-warning.active:hover {
    color: #fff;
    background-color: #a18c74;
    border-color: #a18c74
}

.list-group-item-danger {
    color: #a94442;
    background-color: #f2dede
}

a.list-group-item-danger,button.list-group-item-danger {
    color: #a94442
}

a.list-group-item-danger .list-group-item-heading,button.list-group-item-danger .list-group-item-heading {
    color: inherit
}

a.list-group-item-danger:focus,a.list-group-item-danger:hover,button.list-group-item-danger:focus,button.list-group-item-danger:hover {
    color: #a94442;
    background-color: #ebcccc
}

a.list-group-item-danger.active,a.list-group-item-danger.active:focus,a.list-group-item-danger.active:hover,button.list-group-item-danger.active,button.list-group-item-danger.active:focus,button.list-group-item-danger.active:hover {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442
}

.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3
}

.box,.panel {
    margin-bottom: 22px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
    box-shadow: 0 1px 1px rgba(0,0,0,.05)
}

.box-body,.panel-body {
    padding: 15px
}

.box-body:after,.box-body:before,.panel-body:after,.panel-body:before {
    content: " ";
    display: table
}

.box-body:after,.panel-body:after {
    clear: both
}

.box-heading,.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px
}

.box-heading>.dropdown .dropdown-toggle,.panel-heading>.dropdown .dropdown-toggle {
    color: inherit
}

.box-title,.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    color: inherit
}

.box-title>.small,.box-title>.small>a,.box-title>a,.box-title>small,.box-title>small>a,.panel-title>.small,.panel-title>.small>a,.panel-title>a,.panel-title>small,.panel-title>small>a {
    color: inherit
}

.panel-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #d3e0e9;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.box>.list-group,.box>.panel-collapse>.list-group,.panel>.list-group,.panel>.panel-collapse>.list-group {
    margin-bottom: 0
}

.box>.list-group .list-group-item,.box>.panel-collapse>.list-group .list-group-item,.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0
}

.box>.list-group:first-child .list-group-item:first-child,.box>.panel-collapse>.list-group:first-child .list-group-item:first-child,.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px
}

.box>.list-group:last-child .list-group-item:last-child,.box>.panel-collapse>.list-group:last-child .list-group-item:last-child,.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.box>.box-heading+.panel-collapse>.list-group .list-group-item:first-child,.box>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child,.panel>.box-heading+.panel-collapse>.list-group .list-group-item:first-child,.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.box-heading+.list-group .list-group-item:first-child,.list-group+.panel-footer,.panel-heading+.list-group .list-group-item:first-child {
    border-top-width: 0
}

.box>.panel-collapse>.table,.box>.table,.box>.table-responsive>.table,.panel>.panel-collapse>.table,.panel>.table,.panel>.table-responsive>.table {
    margin-bottom: 0
}

.box>.panel-collapse>.table caption,.box>.table-responsive>.table caption,.box>.table caption,.panel>.panel-collapse>.table caption,.panel>.table-responsive>.table caption,.panel>.table caption {
    padding-left: 15px;
    padding-right: 15px
}

.box>.table-responsive:first-child>.table:first-child,.box>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child,.box>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.box>.table:first-child,.box>.table:first-child>tbody:first-child>tr:first-child,.box>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table:first-child>thead:first-child>tr:first-child {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px
}

.box>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.box>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child,.box>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.box>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.box>.table:first-child>tbody:first-child>tr:first-child td:first-child,.box>.table:first-child>tbody:first-child>tr:first-child th:first-child,.box>.table:first-child>thead:first-child>tr:first-child td:first-child,.box>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child {
    border-top-left-radius: 3px
}

.box>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.box>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child,.box>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.box>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.box>.table:first-child>tbody:first-child>tr:first-child td:last-child,.box>.table:first-child>tbody:first-child>tr:first-child th:last-child,.box>.table:first-child>thead:first-child>tr:first-child td:last-child,.box>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child {
    border-top-right-radius: 3px
}

.box>.table-responsive:last-child>.table:last-child,.box>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.box>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child,.box>.table:last-child,.box>.table:last-child>tbody:last-child>tr:last-child,.box>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child {
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.box>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.box>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.box>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.box>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.box>.table:last-child>tbody:last-child>tr:last-child td:first-child,.box>.table:last-child>tbody:last-child>tr:last-child th:first-child,.box>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.box>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
    border-bottom-left-radius: 3px
}

.box>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.box>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.box>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.box>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.box>.table:last-child>tbody:last-child>tr:last-child td:last-child,.box>.table:last-child>tbody:last-child>tr:last-child th:last-child,.box>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.box>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
    border-bottom-right-radius: 3px
}

.box>.box-body+.table,.box>.box-body+.table-responsive,.box>.panel-body+.table,.box>.panel-body+.table-responsive,.box>.table+.box-body,.box>.table+.panel-body,.box>.table-responsive+.box-body,.box>.table-responsive+.panel-body,.panel>.box-body+.table,.panel>.box-body+.table-responsive,.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.box-body,.panel>.table+.panel-body,.panel>.table-responsive+.box-body,.panel>.table-responsive+.panel-body {
    border-top: 1px solid #ddd
}

.box>.table>tbody:first-child>tr:first-child td,.box>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td,.panel>.table>tbody:first-child>tr:first-child th {
    border-top: 0
}

.box>.table-bordered,.box>.table-responsive>.table-bordered,.panel>.table-bordered,.panel>.table-responsive>.table-bordered {
    border: 0
}

.box>.table-bordered>tbody>tr>td:first-child,.box>.table-bordered>tbody>tr>th:first-child,.box>.table-bordered>tfoot>tr>td:first-child,.box>.table-bordered>tfoot>tr>th:first-child,.box>.table-bordered>thead>tr>td:first-child,.box>.table-bordered>thead>tr>th:first-child,.box>.table-responsive>.table-bordered>tbody>tr>td:first-child,.box>.table-responsive>.table-bordered>tbody>tr>th:first-child,.box>.table-responsive>.table-bordered>tfoot>tr>td:first-child,.box>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.box>.table-responsive>.table-bordered>thead>tr>td:first-child,.box>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child {
    border-left: 0
}

.box>.table-bordered>tbody>tr>td:last-child,.box>.table-bordered>tbody>tr>th:last-child,.box>.table-bordered>tfoot>tr>td:last-child,.box>.table-bordered>tfoot>tr>th:last-child,.box>.table-bordered>thead>tr>td:last-child,.box>.table-bordered>thead>tr>th:last-child,.box>.table-responsive>.table-bordered>tbody>tr>td:last-child,.box>.table-responsive>.table-bordered>tbody>tr>th:last-child,.box>.table-responsive>.table-bordered>tfoot>tr>td:last-child,.box>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.box>.table-responsive>.table-bordered>thead>tr>td:last-child,.box>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child {
    border-right: 0
}

.box>.table-bordered>tbody>tr:first-child>td,.box>.table-bordered>tbody>tr:first-child>th,.box>.table-bordered>tbody>tr:last-child>td,.box>.table-bordered>tbody>tr:last-child>th,.box>.table-bordered>tfoot>tr:last-child>td,.box>.table-bordered>tfoot>tr:last-child>th,.box>.table-bordered>thead>tr:first-child>td,.box>.table-bordered>thead>tr:first-child>th,.box>.table-responsive>.table-bordered>tbody>tr:first-child>td,.box>.table-responsive>.table-bordered>tbody>tr:first-child>th,.box>.table-responsive>.table-bordered>tbody>tr:last-child>td,.box>.table-responsive>.table-bordered>tbody>tr:last-child>th,.box>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.box>.table-responsive>.table-bordered>tfoot>tr:last-child>th,.box>.table-responsive>.table-bordered>thead>tr:first-child>td,.box>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th {
    border-bottom: 0
}

.box>.table-responsive,.panel>.table-responsive {
    border: 0;
    margin-bottom: 0
}

.panel-group {
    margin-bottom: 22px
}

.panel-group .box,.panel-group .panel {
    margin-bottom: 0;
    border-radius: 4px
}

.panel-group .box+.box,.panel-group .box+.panel,.panel-group .panel+.box,.panel-group .panel+.panel {
    margin-top: 5px
}

.panel-group .box-heading,.panel-group .panel-heading {
    border-bottom: 0
}

.panel-group .box-heading+.panel-collapse>.box-body,.panel-group .box-heading+.panel-collapse>.list-group,.panel-group .box-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.box-body,.panel-group .panel-heading+.panel-collapse>.list-group,.panel-group .panel-heading+.panel-collapse>.panel-body {
    border-top: 1px solid #d3e0e9
}

.panel-group .panel-footer {
    border-top: 0
}

.panel-group .panel-footer+.panel-collapse .box-body,.panel-group .panel-footer+.panel-collapse .panel-body {
    border-bottom: 1px solid #d3e0e9
}

.panel-default {
    border-color: #d3e0e9
}

.panel-default>.box-heading,.panel-default>.panel-heading {
    color: #333;
    background-color: #fff;
    border-color: #d3e0e9
}

.panel-default>.box-heading+.panel-collapse>.box-body,.panel-default>.box-heading+.panel-collapse>.panel-body,.panel-default>.panel-heading+.panel-collapse>.box-body,.panel-default>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #d3e0e9
}

.panel-default>.box-heading .badge,.panel-default>.panel-heading .badge {
    color: #fff;
    background-color: #333
}

.panel-default>.panel-footer+.panel-collapse>.box-body,.panel-default>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #d3e0e9
}

.panel-primary {
    border-color: #3097d1
}

.panel-primary>.box-heading,.panel-primary>.panel-heading {
    color: #fff;
    background-color: #3097d1;
    border-color: #3097d1
}

.panel-primary>.box-heading+.panel-collapse>.box-body,.panel-primary>.box-heading+.panel-collapse>.panel-body,.panel-primary>.panel-heading+.panel-collapse>.box-body,.panel-primary>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #3097d1
}

.panel-primary>.box-heading .badge,.panel-primary>.panel-heading .badge {
    color: #3097d1;
    background-color: #fff
}

.panel-primary>.panel-footer+.panel-collapse>.box-body,.panel-primary>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #3097d1
}

.panel-success {
    border-color: #d6e9c6
}

.panel-success>.box-heading,.panel-success>.panel-heading {
    color: #4caf50;
    background-color: #dff0d8;
    border-color: #d6e9c6
}

.panel-success>.box-heading+.panel-collapse>.box-body,.panel-success>.box-heading+.panel-collapse>.panel-body,.panel-success>.panel-heading+.panel-collapse>.box-body,.panel-success>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #d6e9c6
}

.panel-success>.box-heading .badge,.panel-success>.panel-heading .badge {
    color: #dff0d8;
    background-color: #4caf50
}

.panel-success>.panel-footer+.panel-collapse>.box-body,.panel-success>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #d6e9c6
}

.panel-info {
    border-color: #bce8f1
}

.panel-info>.box-heading,.panel-info>.panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1
}

.panel-info>.box-heading+.panel-collapse>.box-body,.panel-info>.box-heading+.panel-collapse>.panel-body,.panel-info>.panel-heading+.panel-collapse>.box-body,.panel-info>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #bce8f1
}

.panel-info>.box-heading .badge,.panel-info>.panel-heading .badge {
    color: #d9edf7;
    background-color: #31708f
}

.panel-info>.panel-footer+.panel-collapse>.box-body,.panel-info>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #bce8f1
}

.panel-warning {
    border-color: #faebcc
}

.panel-warning>.box-heading,.panel-warning>.panel-heading {
    color: #a18c74;
    background-color: #fcf8e3;
    border-color: #faebcc
}

.panel-warning>.box-heading+.panel-collapse>.box-body,.panel-warning>.box-heading+.panel-collapse>.panel-body,.panel-warning>.panel-heading+.panel-collapse>.box-body,.panel-warning>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #faebcc
}

.panel-warning>.box-heading .badge,.panel-warning>.panel-heading .badge {
    color: #fcf8e3;
    background-color: #a18c74
}

.panel-warning>.panel-footer+.panel-collapse>.box-body,.panel-warning>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #faebcc
}

.panel-danger {
    border-color: #ebccd1
}

.panel-danger>.box-heading,.panel-danger>.panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1
}

.panel-danger>.box-heading+.panel-collapse>.box-body,.panel-danger>.box-heading+.panel-collapse>.panel-body,.panel-danger>.panel-heading+.panel-collapse>.box-body,.panel-danger>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #ebccd1
}

.panel-danger>.box-heading .badge,.panel-danger>.panel-heading .badge {
    color: #f2dede;
    background-color: #a94442
}

.panel-danger>.panel-footer+.panel-collapse>.box-body,.panel-danger>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #ebccd1
}

.embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden
}

.embed-responsive .embed-responsive-item,.embed-responsive embed,.embed-responsive iframe,.embed-responsive object,.embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0
}

.embed-responsive-16by9 {
    padding-bottom: 56.25%
}

.embed-responsive-4by3 {
    padding-bottom: 75%
}

.well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.05);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.05)
}

.well blockquote {
    border-color: #ddd;
    border-color: rgba(0,0,0,.15)
}

.well-lg {
    padding: 24px;
    border-radius: 6px
}

.well-sm {
    padding: 9px;
    border-radius: 3px
}

.close {
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .2;
    filter: alpha(opacity=20)
}

.close:focus,.close:hover {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: .5;
    filter: alpha(opacity=50)
}

button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none
}

.modal,.modal-open {
    overflow: hidden
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.modal.fade .modal-dialog {
    -webkit-transform: translateY(-25%);
    transform: translateY(-25%);
    -webkit-transition: -webkit-transform .3s ease-out;
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0);
    transform: translate(0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px
}

.modal-content {
    position: relative;
    background-color: #fff;
    border: 1px solid #999;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 6px;
    -webkit-box-shadow: 0 3px 9px rgba(0,0,0,.5);
    box-shadow: 0 3px 9px rgba(0,0,0,.5);
    background-clip: padding-box;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000
}

.modal-backdrop.fade {
    opacity: 0;
    filter: alpha(opacity=0)
}

.modal-backdrop.in {
    opacity: .5;
    filter: alpha(opacity=50)
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5
}

.modal-header:after,.modal-header:before {
    content: " ";
    display: table
}

.modal-header:after {
    clear: both
}

.modal-header .close {
    margin-top: -2px
}

.modal-title {
    margin: 0;
    line-height: 1.6
}

.modal-body {
    position: relative;
    padding: 15px
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5
}

.modal-footer:after,.modal-footer:before {
    content: " ";
    display: table
}

.modal-footer:after {
    clear: both
}

.modal-footer .btn+.btn {
    margin-left: 5px;
    margin-bottom: 0
}

.modal-footer .btn-group .btn+.btn {
    margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
    margin-left: 0
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width: 768px) {
    .modal-dialog {
        width:600px;
        margin: 30px auto
    }

    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0,0,0,.5);
        box-shadow: 0 5px 15px rgba(0,0,0,.5)
    }

    .modal-sm {
        width: 300px
    }
}

@media (min-width: 992px) {
    .modal-lg {
        width:900px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: Raleway,sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.6;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 12px;
    opacity: 0;
    filter: alpha(opacity=0)
}

.tooltip.in {
    opacity: .9;
    filter: alpha(opacity=90)
}

.tooltip.top {
    margin-top: -3px;
    padding: 5px 0
}

.tooltip.right {
    margin-left: 3px;
    padding: 0 5px
}

.tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0
}

.tooltip.left {
    margin-left: -3px;
    padding: 0 5px
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 4px
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-left .tooltip-arrow {
    right: 5px
}

.tooltip.top-left .tooltip-arrow,.tooltip.top-right .tooltip-arrow {
    bottom: 0;
    margin-bottom: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-right .tooltip-arrow {
    left: 5px
}

.tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-left .tooltip-arrow {
    top: 0;
    right: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-right .tooltip-arrow {
    top: 0;
    left: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: none;
    max-width: 276px;
    padding: 1px;
    font-family: Raleway,sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.6;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 14px;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0,0,0,.2);
    box-shadow: 0 5px 10px rgba(0,0,0,.2)
}

.popover.top {
    margin-top: -10px
}

.popover.right {
    margin-left: 10px
}

.popover.bottom {
    margin-top: 10px
}

.popover.left {
    margin-left: -10px
}

.popover-title {
    margin: 0;
    padding: 8px 14px;
    font-size: 14px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-radius: 5px 5px 0 0
}

.popover-content {
    padding: 9px 14px
}

.popover>.arrow,.popover>.arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover>.arrow {
    border-width: 11px
}

.popover>.arrow:after {
    border-width: 10px;
    content: ""
}

.popover.top>.arrow {
    left: 50%;
    margin-left: -11px;
    border-bottom-width: 0;
    border-top-color: #999;
    border-top-color: rgba(0,0,0,.25);
    bottom: -11px
}

.popover.top>.arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #fff
}

.popover.right>.arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-left-width: 0;
    border-right-color: #999;
    border-right-color: rgba(0,0,0,.25)
}

.popover.right>.arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #fff
}

.popover.bottom>.arrow {
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0,0,0,.25);
    top: -11px
}

.popover.bottom>.arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #fff
}

.popover.left>.arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: #999;
    border-left-color: rgba(0,0,0,.25)
}

.popover.left>.arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #fff;
    bottom: -10px
}

.carousel,.carousel-inner {
    position: relative
}

.carousel-inner {
    overflow: hidden;
    width: 100%
}

.carousel-inner>.item {
    display: none;
    position: relative;
    -webkit-transition: left .6s ease-in-out;
    transition: left .6s ease-in-out
}

.carousel-inner>.item>a>img,.carousel-inner>.item>img {
    display: block;
    max-width: 100%;
    height: auto;
    line-height: 1
}

@media (-webkit-transform-3d),(transform-3d) {
    .carousel-inner>.item {
        -webkit-transition: -webkit-transform .6s ease-in-out;
        transition: -webkit-transform .6s ease-in-out;
        transition: transform .6s ease-in-out;
        transition: transform .6s ease-in-out,-webkit-transform .6s ease-in-out;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        perspective: 1000px
    }

    .carousel-inner>.item.active.right,.carousel-inner>.item.next {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0);
        left: 0
    }

    .carousel-inner>.item.active.left,.carousel-inner>.item.prev {
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0);
        left: 0
    }

    .carousel-inner>.item.active,.carousel-inner>.item.next.left,.carousel-inner>.item.prev.right {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        left: 0
    }
}

.carousel-inner>.active,.carousel-inner>.next,.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 15%;
    opacity: .5;
    filter: alpha(opacity=50);
    font-size: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0,0,0,.6);
    background-color: transparent
}

.carousel-control.left {
    background-image: -webkit-gradient(linear,left top,right top,from(rgba(0,0,0,.5)),to(rgba(0,0,0,.0001)));
    background-image: linear-gradient(90deg,rgba(0,0,0,.5) 0,rgba(0,0,0,.0001));
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#80000000",endColorstr="#00000000",GradientType=1)
}

.carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-gradient(linear,left top,right top,from(rgba(0,0,0,.0001)),to(rgba(0,0,0,.5)));
    background-image: linear-gradient(90deg,rgba(0,0,0,.0001) 0,rgba(0,0,0,.5));
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00000000",endColorstr="#80000000",GradientType=1)
}

.carousel-control:focus,.carousel-control:hover {
    outline: 0;
    color: #fff;
    text-decoration: none;
    opacity: .9;
    filter: alpha(opacity=90)
}

.carousel-control .glyphicon-chevron-left,.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next,.carousel-control .icon-prev {
    position: absolute;
    top: 50%;
    margin-top: -10px;
    z-index: 5;
    display: inline-block
}

.carousel-control .glyphicon-chevron-left,.carousel-control .icon-prev {
    left: 50%;
    margin-left: -10px
}

.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next {
    right: 50%;
    margin-right: -10px
}

.carousel-control .icon-next,.carousel-control .icon-prev {
    width: 20px;
    height: 20px;
    line-height: 1;
    font-family: serif
}

.carousel-control .icon-prev:before {
    content: "\2039"
}

.carousel-control .icon-next:before {
    content: "\203A"
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 15;
    width: 60%;
    margin-left: -30%;
    padding-left: 0;
    list-style: none;
    text-align: center
}

.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #fff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000\9;
    background-color: transparent
}

.carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    left: 15%;
    right: 15%;
    bottom: 20px;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0,0,0,.6)
}

.carousel-caption .btn {
    text-shadow: none
}

@media screen and (min-width: 768px) {
    .carousel-control .glyphicon-chevron-left,.carousel-control .glyphicon-chevron-right,.carousel-control .icon-next,.carousel-control .icon-prev {
        width:30px;
        height: 30px;
        margin-top: -10px;
        font-size: 30px
    }

    .carousel-control .glyphicon-chevron-left,.carousel-control .icon-prev {
        margin-left: -10px
    }

    .carousel-control .glyphicon-chevron-right,.carousel-control .icon-next {
        margin-right: -10px
    }

    .carousel-caption {
        left: 20%;
        right: 20%;
        padding-bottom: 30px
    }

    .carousel-indicators {
        bottom: 20px
    }
}

.clearfix:after,.clearfix:before {
    content: " ";
    display: table
}

.clearfix:after {
    clear: both
}

.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.pull-right {
    float: right!important
}

.pull-left {
    float: left!important
}

.hide {
    display: none!important
}

.show {
    display: block!important
}

.invisible {
    visibility: hidden
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.hidden {
    display: none!important
}

.affix {
    position: fixed
}

@-ms-viewport {
    width: device-width
}

.visible-lg,.visible-lg-block,.visible-lg-inline,.visible-lg-inline-block,.visible-md,.visible-md-block,.visible-md-inline,.visible-md-inline-block,.visible-sm,.visible-sm-block,.visible-sm-inline,.visible-sm-inline-block,.visible-xs,.visible-xs-block,.visible-xs-inline,.visible-xs-inline-block {
    display: none!important
}

@media (max-width: 767px) {
    .visible-xs {
        display:block!important
    }

    table.visible-xs {
        display: table!important
    }

    tr.visible-xs {
        display: table-row!important
    }

    td.visible-xs,th.visible-xs {
        display: table-cell!important
    }
}

@media (max-width: 767px) {
    .visible-xs-block {
        display:block!important
    }
}

@media (max-width: 767px) {
    .visible-xs-inline {
        display:inline!important
    }
}

@media (max-width: 767px) {
    .visible-xs-inline-block {
        display:inline-block!important
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .visible-sm {
        display:block!important
    }

    table.visible-sm {
        display: table!important
    }

    tr.visible-sm {
        display: table-row!important
    }

    td.visible-sm,th.visible-sm {
        display: table-cell!important
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .visible-sm-block {
        display:block!important
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .visible-sm-inline {
        display:inline!important
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .visible-sm-inline-block {
        display:inline-block!important
    }
}

@media (min-width: 992px) and (max-width:1199px) {
    .visible-md {
        display:block!important
    }

    table.visible-md {
        display: table!important
    }

    tr.visible-md {
        display: table-row!important
    }

    td.visible-md,th.visible-md {
        display: table-cell!important
    }
}

@media (min-width: 992px) and (max-width:1199px) {
    .visible-md-block {
        display:block!important
    }
}

@media (min-width: 992px) and (max-width:1199px) {
    .visible-md-inline {
        display:inline!important
    }
}

@media (min-width: 992px) and (max-width:1199px) {
    .visible-md-inline-block {
        display:inline-block!important
    }
}

@media (min-width: 1200px) {
    .visible-lg {
        display:block!important
    }

    table.visible-lg {
        display: table!important
    }

    tr.visible-lg {
        display: table-row!important
    }

    td.visible-lg,th.visible-lg {
        display: table-cell!important
    }
}

@media (min-width: 1200px) {
    .visible-lg-block {
        display:block!important
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline {
        display:inline!important
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline-block {
        display:inline-block!important
    }
}

@media (max-width: 767px) {
    .hidden-xs {
        display:none!important
    }
}

@media (min-width: 768px) and (max-width:991px) {
    .hidden-sm {
        display:none!important
    }
}

@media (min-width: 992px) and (max-width:1199px) {
    .hidden-md {
        display:none!important
    }
}

@media (min-width: 1200px) {
    .hidden-lg {
        display:none!important
    }
}

.visible-print {
    display: none!important
}

@media print {
    .visible-print {
        display: block!important
    }

    table.visible-print {
        display: table!important
    }

    tr.visible-print {
        display: table-row!important
    }

    td.visible-print,th.visible-print {
        display: table-cell!important
    }
}

.visible-print-block {
    display: none!important
}

@media print {
    .visible-print-block {
        display: block!important
    }
}

.visible-print-inline {
    display: none!important
}

@media print {
    .visible-print-inline {
        display: inline!important
    }
}

.visible-print-inline-block {
    display: none!important
}

@media print {
    .visible-print-inline-block {
        display: inline-block!important
    }
}

@media print {
    .hidden-print {
        display: none!important
    }
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
    font-family: FontAwesome;
    src: url(/fonts/vendor/font-awesome/fontawesome-webfont.eot?674f50d287a8c48dc19ba404d20fe713);
    src: url(/fonts/vendor/font-awesome/fontawesome-webfont.eot?674f50d287a8c48dc19ba404d20fe713) format("embedded-opentype"),url(/fonts/vendor/font-awesome/fontawesome-webfont.woff2?af7ae505a9eed503f8b8e6982036873e) format("woff2"),url(/fonts/vendor/font-awesome/fontawesome-webfont.woff?fee66e712a8a08eef5805a46892932ad) format("woff"),url(/fonts/vendor/font-awesome/fontawesome-webfont.ttf?b06871f281fee6b241d60582ae9369b9) format("truetype"),url(/fonts/vendor/font-awesome/fontawesome-webfont.svg?912ec66d7572ff821749319396470bde) format("svg");
    font-weight: 400;
    font-style: normal
}

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.fa-lg {
    font-size: 1.33333333em;
    line-height: .75em;
    vertical-align: -15%
}

.fa-2x {
    font-size: 2em
}

.fa-3x {
    font-size: 3em
}

.fa-4x {
    font-size: 4em
}

.fa-5x {
    font-size: 5em
}

.fa-fw {
    width: 1.28571429em;
    text-align: center
}

.fa-ul {
    padding-left: 0;
    margin-left: 2.14285714em;
    list-style-type: none
}

.fa-ul>li {
    position: relative
}

.fa-li {
    position: absolute;
    left: -2.14285714em;
    width: 2.14285714em;
    top: .14285714em;
    text-align: center
}

.fa-li.fa-lg {
    left: -1.85714286em
}

.fa-border {
    padding: .2em .25em .15em;
    border: .08em solid #eee;
    border-radius: .1em
}

.fa-pull-left {
    float: left
}

.fa-pull-right {
    float: right
}

.fa.fa-pull-left {
    margin-right: .3em
}

.fa.fa-pull-right {
    margin-left: .3em
}

.pull-right {
    float: right
}

.pull-left {
    float: left
}

.fa.pull-left {
    margin-right: .3em
}

.fa.pull-right {
    margin-left: .3em
}

.fa-spin {
    -webkit-animation: fa-spin 2s infinite linear;
    animation: fa-spin 2s infinite linear
}

.fa-pulse {
    -webkit-animation: fa-spin 1s infinite steps(8);
    animation: fa-spin 1s infinite steps(8)
}

@-webkit-keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

@keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

.fa-rotate-90 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.fa-rotate-180 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.fa-rotate-270 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
}

.fa-flip-horizontal {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1)
}

.fa-flip-vertical {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
    -webkit-transform: scaleY(-1);
    transform: scaleY(-1)
}

:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270 {
    -webkit-filter: none;
    filter: none
}

.fa-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle
}

.fa-stack-1x,.fa-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center
}

.fa-stack-1x {
    line-height: inherit
}

.fa-stack-2x {
    font-size: 2em
}

.fa-inverse {
    color: #fff
}

.fa-glass:before {
    content: "\F000"
}

.fa-music:before {
    content: "\F001"
}

.fa-search:before {
    content: "\F002"
}

.fa-envelope-o:before {
    content: "\F003"
}

.fa-heart:before {
    content: "\F004"
}

.fa-star:before {
    content: "\F005"
}

.fa-star-o:before {
    content: "\F006"
}

.fa-user:before {
    content: "\F007"
}

.fa-film:before {
    content: "\F008"
}

.fa-th-large:before {
    content: "\F009"
}

.fa-th:before {
    content: "\F00A"
}

.fa-th-list:before {
    content: "\F00B"
}

.fa-check:before {
    content: "\F00C"
}

.fa-close:before,.fa-remove:before,.fa-times:before {
    content: "\F00D"
}

.fa-search-plus:before {
    content: "\F00E"
}

.fa-search-minus:before {
    content: "\F010"
}

.fa-power-off:before {
    content: "\F011"
}

.fa-signal:before {
    content: "\F012"
}

.fa-cog:before,.fa-gear:before {
    content: "\F013"
}

.fa-trash-o:before {
    content: "\F014"
}

.fa-home:before {
    content: "\F015"
}

.fa-file-o:before {
    content: "\F016"
}

.fa-clock-o:before {
    content: "\F017"
}

.fa-road:before {
    content: "\F018"
}

.fa-download:before {
    content: "\F019"
}

.fa-arrow-circle-o-down:before {
    content: "\F01A"
}

.fa-arrow-circle-o-up:before {
    content: "\F01B"
}

.fa-inbox:before {
    content: "\F01C"
}

.fa-play-circle-o:before {
    content: "\F01D"
}

.fa-repeat:before,.fa-rotate-right:before {
    content: "\F01E"
}

.fa-refresh:before {
    content: "\F021"
}

.fa-list-alt:before {
    content: "\F022"
}

.fa-lock:before {
    content: "\F023"
}

.fa-flag:before {
    content: "\F024"
}

.fa-headphones:before {
    content: "\F025"
}

.fa-volume-off:before {
    content: "\F026"
}

.fa-volume-down:before {
    content: "\F027"
}

.fa-volume-up:before {
    content: "\F028"
}

.fa-qrcode:before {
    content: "\F029"
}

.fa-barcode:before {
    content: "\F02A"
}

.fa-tag:before {
    content: "\F02B"
}

.fa-tags:before {
    content: "\F02C"
}

.fa-book:before {
    content: "\F02D"
}

.fa-bookmark:before {
    content: "\F02E"
}

.fa-print:before {
    content: "\F02F"
}

.fa-camera:before {
    content: "\F030"
}

.fa-font:before {
    content: "\F031"
}

.fa-bold:before {
    content: "\F032"
}

.fa-italic:before {
    content: "\F033"
}

.fa-text-height:before {
    content: "\F034"
}

.fa-text-width:before {
    content: "\F035"
}

.fa-align-left:before {
    content: "\F036"
}

.fa-align-center:before {
    content: "\F037"
}

.fa-align-right:before {
    content: "\F038"
}

.fa-align-justify:before {
    content: "\F039"
}

.fa-list:before {
    content: "\F03A"
}

.fa-dedent:before,.fa-outdent:before {
    content: "\F03B"
}

.fa-indent:before {
    content: "\F03C"
}

.fa-video-camera:before {
    content: "\F03D"
}

.fa-image:before,.fa-photo:before,.fa-picture-o:before {
    content: "\F03E"
}

.fa-pencil:before {
    content: "\F040"
}

.fa-map-marker:before {
    content: "\F041"
}

.fa-adjust:before {
    content: "\F042"
}

.fa-tint:before {
    content: "\F043"
}

.fa-edit:before,.fa-pencil-square-o:before {
    content: "\F044"
}

.fa-share-square-o:before {
    content: "\F045"
}

.fa-check-square-o:before {
    content: "\F046"
}

.fa-arrows:before {
    content: "\F047"
}

.fa-step-backward:before {
    content: "\F048"
}

.fa-fast-backward:before {
    content: "\F049"
}

.fa-backward:before {
    content: "\F04A"
}

.fa-play:before {
    content: "\F04B"
}

.fa-pause:before {
    content: "\F04C"
}

.fa-stop:before {
    content: "\F04D"
}

.fa-forward:before {
    content: "\F04E"
}

.fa-fast-forward:before {
    content: "\F050"
}

.fa-step-forward:before {
    content: "\F051"
}

.fa-eject:before {
    content: "\F052"
}

.fa-chevron-left:before {
    content: "\F053"
}

.fa-chevron-right:before {
    content: "\F054"
}

.fa-plus-circle:before {
    content: "\F055"
}

.fa-minus-circle:before {
    content: "\F056"
}

.fa-times-circle:before {
    content: "\F057"
}

.fa-check-circle:before {
    content: "\F058"
}

.fa-question-circle:before {
    content: "\F059"
}

.fa-info-circle:before {
    content: "\F05A"
}

.fa-crosshairs:before {
    content: "\F05B"
}

.fa-times-circle-o:before {
    content: "\F05C"
}

.fa-check-circle-o:before {
    content: "\F05D"
}

.fa-ban:before {
    content: "\F05E"
}

.fa-arrow-left:before {
    content: "\F060"
}

.fa-arrow-right:before {
    content: "\F061"
}

.fa-arrow-up:before {
    content: "\F062"
}

.fa-arrow-down:before {
    content: "\F063"
}

.fa-mail-forward:before,.fa-share:before {
    content: "\F064"
}

.fa-expand:before {
    content: "\F065"
}

.fa-compress:before {
    content: "\F066"
}

.fa-plus:before {
    content: "\F067"
}

.fa-minus:before {
    content: "\F068"
}

.fa-asterisk:before {
    content: "\F069"
}

.fa-exclamation-circle:before {
    content: "\F06A"
}

.fa-gift:before {
    content: "\F06B"
}

.fa-leaf:before {
    content: "\F06C"
}

.fa-fire:before {
    content: "\F06D"
}

.fa-eye:before {
    content: "\F06E"
}

.fa-eye-slash:before {
    content: "\F070"
}

.fa-exclamation-triangle:before,.fa-warning:before {
    content: "\F071"
}

.fa-plane:before {
    content: "\F072"
}

.fa-calendar:before {
    content: "\F073"
}

.fa-random:before {
    content: "\F074"
}

.fa-comment:before {
    content: "\F075"
}

.fa-magnet:before {
    content: "\F076"
}

.fa-chevron-up:before {
    content: "\F077"
}

.fa-chevron-down:before {
    content: "\F078"
}

.fa-retweet:before {
    content: "\F079"
}

.fa-shopping-cart:before {
    content: "\F07A"
}

.fa-folder:before {
    content: "\F07B"
}

.fa-folder-open:before {
    content: "\F07C"
}

.fa-arrows-v:before {
    content: "\F07D"
}

.fa-arrows-h:before {
    content: "\F07E"
}

.fa-bar-chart-o:before,.fa-bar-chart:before {
    content: "\F080"
}

.fa-twitter-square:before {
    content: "\F081"
}

.fa-facebook-square:before {
    content: "\F082"
}

.fa-camera-retro:before {
    content: "\F083"
}

.fa-key:before {
    content: "\F084"
}

.fa-cogs:before,.fa-gears:before {
    content: "\F085"
}

.fa-comments:before {
    content: "\F086"
}

.fa-thumbs-o-up:before {
    content: "\F087"
}

.fa-thumbs-o-down:before {
    content: "\F088"
}

.fa-star-half:before {
    content: "\F089"
}

.fa-heart-o:before {
    content: "\F08A"
}

.fa-sign-out:before {
    content: "\F08B"
}

.fa-linkedin-square:before {
    content: "\F08C"
}

.fa-thumb-tack:before {
    content: "\F08D"
}

.fa-external-link:before {
    content: "\F08E"
}

.fa-sign-in:before {
    content: "\F090"
}

.fa-trophy:before {
    content: "\F091"
}

.fa-github-square:before {
    content: "\F092"
}

.fa-upload:before {
    content: "\F093"
}

.fa-lemon-o:before {
    content: "\F094"
}

.fa-phone:before {
    content: "\F095"
}

.fa-square-o:before {
    content: "\F096"
}

.fa-bookmark-o:before {
    content: "\F097"
}

.fa-phone-square:before {
    content: "\F098"
}

.fa-twitter:before {
    content: "\F099"
}

.fa-facebook-f:before,.fa-facebook:before {
    content: "\F09A"
}

.fa-github:before {
    content: "\F09B"
}

.fa-unlock:before {
    content: "\F09C"
}

.fa-credit-card:before {
    content: "\F09D"
}

.fa-feed:before,.fa-rss:before {
    content: "\F09E"
}

.fa-hdd-o:before {
    content: "\F0A0"
}

.fa-bullhorn:before {
    content: "\F0A1"
}

.fa-bell:before {
    content: "\F0F3"
}

.fa-certificate:before {
    content: "\F0A3"
}

.fa-hand-o-right:before {
    content: "\F0A4"
}

.fa-hand-o-left:before {
    content: "\F0A5"
}

.fa-hand-o-up:before {
    content: "\F0A6"
}

.fa-hand-o-down:before {
    content: "\F0A7"
}

.fa-arrow-circle-left:before {
    content: "\F0A8"
}

.fa-arrow-circle-right:before {
    content: "\F0A9"
}

.fa-arrow-circle-up:before {
    content: "\F0AA"
}

.fa-arrow-circle-down:before {
    content: "\F0AB"
}

.fa-globe:before {
    content: "\F0AC"
}

.fa-wrench:before {
    content: "\F0AD"
}

.fa-tasks:before {
    content: "\F0AE"
}

.fa-filter:before {
    content: "\F0B0"
}

.fa-briefcase:before {
    content: "\F0B1"
}

.fa-arrows-alt:before {
    content: "\F0B2"
}

.fa-group:before,.fa-users:before {
    content: "\F0C0"
}

.fa-chain:before,.fa-link:before {
    content: "\F0C1"
}

.fa-cloud:before {
    content: "\F0C2"
}

.fa-flask:before {
    content: "\F0C3"
}

.fa-cut:before,.fa-scissors:before {
    content: "\F0C4"
}

.fa-copy:before,.fa-files-o:before {
    content: "\F0C5"
}

.fa-paperclip:before {
    content: "\F0C6"
}

.fa-floppy-o:before,.fa-save:before {
    content: "\F0C7"
}

.fa-square:before {
    content: "\F0C8"
}

.fa-bars:before,.fa-navicon:before,.fa-reorder:before {
    content: "\F0C9"
}

.fa-list-ul:before {
    content: "\F0CA"
}

.fa-list-ol:before {
    content: "\F0CB"
}

.fa-strikethrough:before {
    content: "\F0CC"
}

.fa-underline:before {
    content: "\F0CD"
}

.fa-table:before {
    content: "\F0CE"
}

.fa-magic:before {
    content: "\F0D0"
}

.fa-truck:before {
    content: "\F0D1"
}

.fa-pinterest:before {
    content: "\F0D2"
}

.fa-pinterest-square:before {
    content: "\F0D3"
}

.fa-google-plus-square:before {
    content: "\F0D4"
}

.fa-google-plus:before {
    content: "\F0D5"
}

.fa-money:before {
    content: "\F0D6"
}

.fa-caret-down:before {
    content: "\F0D7"
}

.fa-caret-up:before {
    content: "\F0D8"
}

.fa-caret-left:before {
    content: "\F0D9"
}

.fa-caret-right:before {
    content: "\F0DA"
}

.fa-columns:before {
    content: "\F0DB"
}

.fa-sort:before,.fa-unsorted:before {
    content: "\F0DC"
}

.fa-sort-desc:before,.fa-sort-down:before {
    content: "\F0DD"
}

.fa-sort-asc:before,.fa-sort-up:before {
    content: "\F0DE"
}

.fa-envelope:before {
    content: "\F0E0"
}

.fa-linkedin:before {
    content: "\F0E1"
}

.fa-rotate-left:before,.fa-undo:before {
    content: "\F0E2"
}

.fa-gavel:before,.fa-legal:before {
    content: "\F0E3"
}

.fa-dashboard:before,.fa-tachometer:before {
    content: "\F0E4"
}

.fa-comment-o:before {
    content: "\F0E5"
}

.fa-comments-o:before {
    content: "\F0E6"
}

.fa-bolt:before,.fa-flash:before {
    content: "\F0E7"
}

.fa-sitemap:before {
    content: "\F0E8"
}

.fa-umbrella:before {
    content: "\F0E9"
}

.fa-clipboard:before,.fa-paste:before {
    content: "\F0EA"
}

.fa-lightbulb-o:before {
    content: "\F0EB"
}

.fa-exchange:before {
    content: "\F0EC"
}

.fa-cloud-download:before {
    content: "\F0ED"
}

.fa-cloud-upload:before {
    content: "\F0EE"
}

.fa-user-md:before {
    content: "\F0F0"
}

.fa-stethoscope:before {
    content: "\F0F1"
}

.fa-suitcase:before {
    content: "\F0F2"
}

.fa-bell-o:before {
    content: "\F0A2"
}

.fa-coffee:before {
    content: "\F0F4"
}

.fa-cutlery:before {
    content: "\F0F5"
}

.fa-file-text-o:before {
    content: "\F0F6"
}

.fa-building-o:before {
    content: "\F0F7"
}

.fa-hospital-o:before {
    content: "\F0F8"
}

.fa-ambulance:before {
    content: "\F0F9"
}

.fa-medkit:before {
    content: "\F0FA"
}

.fa-fighter-jet:before {
    content: "\F0FB"
}

.fa-beer:before {
    content: "\F0FC"
}

.fa-h-square:before {
    content: "\F0FD"
}

.fa-plus-square:before {
    content: "\F0FE"
}

.fa-angle-double-left:before {
    content: "\F100"
}

.fa-angle-double-right:before {
    content: "\F101"
}

.fa-angle-double-up:before {
    content: "\F102"
}

.fa-angle-double-down:before {
    content: "\F103"
}

.fa-angle-left:before {
    content: "\F104"
}

.fa-angle-right:before {
    content: "\F105"
}

.fa-angle-up:before {
    content: "\F106"
}

.fa-angle-down:before {
    content: "\F107"
}

.fa-desktop:before {
    content: "\F108"
}

.fa-laptop:before {
    content: "\F109"
}

.fa-tablet:before {
    content: "\F10A"
}

.fa-mobile-phone:before,.fa-mobile:before {
    content: "\F10B"
}

.fa-circle-o:before {
    content: "\F10C"
}

.fa-quote-left:before {
    content: "\F10D"
}

.fa-quote-right:before {
    content: "\F10E"
}

.fa-spinner:before {
    content: "\F110"
}

.fa-circle:before {
    content: "\F111"
}

.fa-mail-reply:before,.fa-reply:before {
    content: "\F112"
}

.fa-github-alt:before {
    content: "\F113"
}

.fa-folder-o:before {
    content: "\F114"
}

.fa-folder-open-o:before {
    content: "\F115"
}

.fa-smile-o:before {
    content: "\F118"
}

.fa-frown-o:before {
    content: "\F119"
}

.fa-meh-o:before {
    content: "\F11A"
}

.fa-gamepad:before {
    content: "\F11B"
}

.fa-keyboard-o:before {
    content: "\F11C"
}

.fa-flag-o:before {
    content: "\F11D"
}

.fa-flag-checkered:before {
    content: "\F11E"
}

.fa-terminal:before {
    content: "\F120"
}

.fa-code:before {
    content: "\F121"
}

.fa-mail-reply-all:before,.fa-reply-all:before {
    content: "\F122"
}

.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before {
    content: "\F123"
}

.fa-location-arrow:before {
    content: "\F124"
}

.fa-crop:before {
    content: "\F125"
}

.fa-code-fork:before {
    content: "\F126"
}

.fa-chain-broken:before,.fa-unlink:before {
    content: "\F127"
}

.fa-question:before {
    content: "\F128"
}

.fa-info:before {
    content: "\F129"
}

.fa-exclamation:before {
    content: "\F12A"
}

.fa-superscript:before {
    content: "\F12B"
}

.fa-subscript:before {
    content: "\F12C"
}

.fa-eraser:before {
    content: "\F12D"
}

.fa-puzzle-piece:before {
    content: "\F12E"
}

.fa-microphone:before {
    content: "\F130"
}

.fa-microphone-slash:before {
    content: "\F131"
}

.fa-shield:before {
    content: "\F132"
}

.fa-calendar-o:before {
    content: "\F133"
}

.fa-fire-extinguisher:before {
    content: "\F134"
}

.fa-rocket:before {
    content: "\F135"
}

.fa-maxcdn:before {
    content: "\F136"
}

.fa-chevron-circle-left:before {
    content: "\F137"
}

.fa-chevron-circle-right:before {
    content: "\F138"
}

.fa-chevron-circle-up:before {
    content: "\F139"
}

.fa-chevron-circle-down:before {
    content: "\F13A"
}

.fa-html5:before {
    content: "\F13B"
}

.fa-css3:before {
    content: "\F13C"
}

.fa-anchor:before {
    content: "\F13D"
}

.fa-unlock-alt:before {
    content: "\F13E"
}

.fa-bullseye:before {
    content: "\F140"
}

.fa-ellipsis-h:before {
    content: "\F141"
}

.fa-ellipsis-v:before {
    content: "\F142"
}

.fa-rss-square:before {
    content: "\F143"
}

.fa-play-circle:before {
    content: "\F144"
}

.fa-ticket:before {
    content: "\F145"
}

.fa-minus-square:before {
    content: "\F146"
}

.fa-minus-square-o:before {
    content: "\F147"
}

.fa-level-up:before {
    content: "\F148"
}

.fa-level-down:before {
    content: "\F149"
}

.fa-check-square:before {
    content: "\F14A"
}

.fa-pencil-square:before {
    content: "\F14B"
}

.fa-external-link-square:before {
    content: "\F14C"
}

.fa-share-square:before {
    content: "\F14D"
}

.fa-compass:before {
    content: "\F14E"
}

.fa-caret-square-o-down:before,.fa-toggle-down:before {
    content: "\F150"
}

.fa-caret-square-o-up:before,.fa-toggle-up:before {
    content: "\F151"
}

.fa-caret-square-o-right:before,.fa-toggle-right:before {
    content: "\F152"
}

.fa-eur:before,.fa-euro:before {
    content: "\F153"
}

.fa-gbp:before {
    content: "\F154"
}

.fa-dollar:before,.fa-usd:before {
    content: "\F155"
}

.fa-inr:before,.fa-rupee:before {
    content: "\F156"
}

.fa-cny:before,.fa-jpy:before,.fa-rmb:before,.fa-yen:before {
    content: "\F157"
}

.fa-rouble:before,.fa-rub:before,.fa-ruble:before {
    content: "\F158"
}

.fa-krw:before,.fa-won:before {
    content: "\F159"
}

.fa-bitcoin:before,.fa-btc:before {
    content: "\F15A"
}

.fa-file:before {
    content: "\F15B"
}

.fa-file-text:before {
    content: "\F15C"
}

.fa-sort-alpha-asc:before {
    content: "\F15D"
}

.fa-sort-alpha-desc:before {
    content: "\F15E"
}

.fa-sort-amount-asc:before {
    content: "\F160"
}

.fa-sort-amount-desc:before {
    content: "\F161"
}

.fa-sort-numeric-asc:before {
    content: "\F162"
}

.fa-sort-numeric-desc:before {
    content: "\F163"
}

.fa-thumbs-up:before {
    content: "\F164"
}

.fa-thumbs-down:before {
    content: "\F165"
}

.fa-youtube-square:before {
    content: "\F166"
}

.fa-youtube:before {
    content: "\F167"
}

.fa-xing:before {
    content: "\F168"
}

.fa-xing-square:before {
    content: "\F169"
}

.fa-youtube-play:before {
    content: "\F16A"
}

.fa-dropbox:before {
    content: "\F16B"
}

.fa-stack-overflow:before {
    content: "\F16C"
}

.fa-instagram:before {
    content: "\F16D"
}

.fa-flickr:before {
    content: "\F16E"
}

.fa-adn:before {
    content: "\F170"
}

.fa-bitbucket:before {
    content: "\F171"
}

.fa-bitbucket-square:before {
    content: "\F172"
}

.fa-tumblr:before {
    content: "\F173"
}

.fa-tumblr-square:before {
    content: "\F174"
}

.fa-long-arrow-down:before {
    content: "\F175"
}

.fa-long-arrow-up:before {
    content: "\F176"
}

.fa-long-arrow-left:before {
    content: "\F177"
}

.fa-long-arrow-right:before {
    content: "\F178"
}

.fa-apple:before {
    content: "\F179"
}

.fa-windows:before {
    content: "\F17A"
}

.fa-android:before {
    content: "\F17B"
}

.fa-linux:before {
    content: "\F17C"
}

.fa-dribbble:before {
    content: "\F17D"
}

.fa-skype:before {
    content: "\F17E"
}

.fa-foursquare:before {
    content: "\F180"
}

.fa-trello:before {
    content: "\F181"
}

.fa-female:before {
    content: "\F182"
}

.fa-male:before {
    content: "\F183"
}

.fa-gittip:before,.fa-gratipay:before {
    content: "\F184"
}

.fa-sun-o:before {
    content: "\F185"
}

.fa-moon-o:before {
    content: "\F186"
}

.fa-archive:before {
    content: "\F187"
}

.fa-bug:before {
    content: "\F188"
}

.fa-vk:before {
    content: "\F189"
}

.fa-weibo:before {
    content: "\F18A"
}

.fa-renren:before {
    content: "\F18B"
}

.fa-pagelines:before {
    content: "\F18C"
}

.fa-stack-exchange:before {
    content: "\F18D"
}

.fa-arrow-circle-o-right:before {
    content: "\F18E"
}

.fa-arrow-circle-o-left:before {
    content: "\F190"
}

.fa-caret-square-o-left:before,.fa-toggle-left:before {
    content: "\F191"
}

.fa-dot-circle-o:before {
    content: "\F192"
}

.fa-wheelchair:before {
    content: "\F193"
}

.fa-vimeo-square:before {
    content: "\F194"
}

.fa-try:before,.fa-turkish-lira:before {
    content: "\F195"
}

.fa-plus-square-o:before {
    content: "\F196"
}

.fa-space-shuttle:before {
    content: "\F197"
}

.fa-slack:before {
    content: "\F198"
}

.fa-envelope-square:before {
    content: "\F199"
}

.fa-wordpress:before {
    content: "\F19A"
}

.fa-openid:before {
    content: "\F19B"
}

.fa-bank:before,.fa-institution:before,.fa-university:before {
    content: "\F19C"
}

.fa-graduation-cap:before,.fa-mortar-board:before {
    content: "\F19D"
}

.fa-yahoo:before {
    content: "\F19E"
}

.fa-google:before {
    content: "\F1A0"
}

.fa-reddit:before {
    content: "\F1A1"
}

.fa-reddit-square:before {
    content: "\F1A2"
}

.fa-stumbleupon-circle:before {
    content: "\F1A3"
}

.fa-stumbleupon:before {
    content: "\F1A4"
}

.fa-delicious:before {
    content: "\F1A5"
}

.fa-digg:before {
    content: "\F1A6"
}

.fa-pied-piper-pp:before {
    content: "\F1A7"
}

.fa-pied-piper-alt:before {
    content: "\F1A8"
}

.fa-drupal:before {
    content: "\F1A9"
}

.fa-joomla:before {
    content: "\F1AA"
}

.fa-language:before {
    content: "\F1AB"
}

.fa-fax:before {
    content: "\F1AC"
}

.fa-building:before {
    content: "\F1AD"
}

.fa-child:before {
    content: "\F1AE"
}

.fa-paw:before {
    content: "\F1B0"
}

.fa-spoon:before {
    content: "\F1B1"
}

.fa-cube:before {
    content: "\F1B2"
}

.fa-cubes:before {
    content: "\F1B3"
}

.fa-behance:before {
    content: "\F1B4"
}

.fa-behance-square:before {
    content: "\F1B5"
}

.fa-steam:before {
    content: "\F1B6"
}

.fa-steam-square:before {
    content: "\F1B7"
}

.fa-recycle:before {
    content: "\F1B8"
}

.fa-automobile:before,.fa-car:before {
    content: "\F1B9"
}

.fa-cab:before,.fa-taxi:before {
    content: "\F1BA"
}

.fa-tree:before {
    content: "\F1BB"
}

.fa-spotify:before {
    content: "\F1BC"
}

.fa-deviantart:before {
    content: "\F1BD"
}

.fa-soundcloud:before {
    content: "\F1BE"
}

.fa-database:before {
    content: "\F1C0"
}

.fa-file-pdf-o:before {
    content: "\F1C1"
}

.fa-file-word-o:before {
    content: "\F1C2"
}

.fa-file-excel-o:before {
    content: "\F1C3"
}

.fa-file-powerpoint-o:before {
    content: "\F1C4"
}

.fa-file-image-o:before,.fa-file-photo-o:before,.fa-file-picture-o:before {
    content: "\F1C5"
}

.fa-file-archive-o:before,.fa-file-zip-o:before {
    content: "\F1C6"
}

.fa-file-audio-o:before,.fa-file-sound-o:before {
    content: "\F1C7"
}

.fa-file-movie-o:before,.fa-file-video-o:before {
    content: "\F1C8"
}

.fa-file-code-o:before {
    content: "\F1C9"
}

.fa-vine:before {
    content: "\F1CA"
}

.fa-codepen:before {
    content: "\F1CB"
}

.fa-jsfiddle:before {
    content: "\F1CC"
}

.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-ring:before,.fa-life-saver:before,.fa-support:before {
    content: "\F1CD"
}

.fa-circle-o-notch:before {
    content: "\F1CE"
}

.fa-ra:before,.fa-rebel:before,.fa-resistance:before {
    content: "\F1D0"
}

.fa-empire:before,.fa-ge:before {
    content: "\F1D1"
}

.fa-git-square:before {
    content: "\F1D2"
}

.fa-git:before {
    content: "\F1D3"
}

.fa-hacker-news:before,.fa-y-combinator-square:before,.fa-yc-square:before {
    content: "\F1D4"
}

.fa-tencent-weibo:before {
    content: "\F1D5"
}

.fa-qq:before {
    content: "\F1D6"
}

.fa-wechat:before,.fa-weixin:before {
    content: "\F1D7"
}

.fa-paper-plane:before,.fa-send:before {
    content: "\F1D8"
}

.fa-paper-plane-o:before,.fa-send-o:before {
    content: "\F1D9"
}

.fa-history:before {
    content: "\F1DA"
}

.fa-circle-thin:before {
    content: "\F1DB"
}

.fa-header:before {
    content: "\F1DC"
}

.fa-paragraph:before {
    content: "\F1DD"
}

.fa-sliders:before {
    content: "\F1DE"
}

.fa-share-alt:before {
    content: "\F1E0"
}

.fa-share-alt-square:before {
    content: "\F1E1"
}

.fa-bomb:before {
    content: "\F1E2"
}

.fa-futbol-o:before,.fa-soccer-ball-o:before {
    content: "\F1E3"
}

.fa-tty:before {
    content: "\F1E4"
}

.fa-binoculars:before {
    content: "\F1E5"
}

.fa-plug:before {
    content: "\F1E6"
}

.fa-slideshare:before {
    content: "\F1E7"
}

.fa-twitch:before {
    content: "\F1E8"
}

.fa-yelp:before {
    content: "\F1E9"
}

.fa-newspaper-o:before {
    content: "\F1EA"
}

.fa-wifi:before {
    content: "\F1EB"
}

.fa-calculator:before {
    content: "\F1EC"
}

.fa-paypal:before {
    content: "\F1ED"
}

.fa-google-wallet:before {
    content: "\F1EE"
}

.fa-cc-visa:before {
    content: "\F1F0"
}

.fa-cc-mastercard:before {
    content: "\F1F1"
}

.fa-cc-discover:before {
    content: "\F1F2"
}

.fa-cc-amex:before {
    content: "\F1F3"
}

.fa-cc-paypal:before {
    content: "\F1F4"
}

.fa-cc-stripe:before {
    content: "\F1F5"
}

.fa-bell-slash:before {
    content: "\F1F6"
}

.fa-bell-slash-o:before {
    content: "\F1F7"
}

.fa-trash:before {
    content: "\F1F8"
}

.fa-copyright:before {
    content: "\F1F9"
}

.fa-at:before {
    content: "\F1FA"
}

.fa-eyedropper:before {
    content: "\F1FB"
}

.fa-paint-brush:before {
    content: "\F1FC"
}

.fa-birthday-cake:before {
    content: "\F1FD"
}

.fa-area-chart:before {
    content: "\F1FE"
}

.fa-pie-chart:before {
    content: "\F200"
}

.fa-line-chart:before {
    content: "\F201"
}

.fa-lastfm:before {
    content: "\F202"
}

.fa-lastfm-square:before {
    content: "\F203"
}

.fa-toggle-off:before {
    content: "\F204"
}

.fa-toggle-on:before {
    content: "\F205"
}

.fa-bicycle:before {
    content: "\F206"
}

.fa-bus:before {
    content: "\F207"
}

.fa-ioxhost:before {
    content: "\F208"
}

.fa-angellist:before {
    content: "\F209"
}

.fa-cc:before {
    content: "\F20A"
}

.fa-ils:before,.fa-shekel:before,.fa-sheqel:before {
    content: "\F20B"
}

.fa-meanpath:before {
    content: "\F20C"
}

.fa-buysellads:before {
    content: "\F20D"
}

.fa-connectdevelop:before {
    content: "\F20E"
}

.fa-dashcube:before {
    content: "\F210"
}

.fa-forumbee:before {
    content: "\F211"
}

.fa-leanpub:before {
    content: "\F212"
}

.fa-sellsy:before {
    content: "\F213"
}

.fa-shirtsinbulk:before {
    content: "\F214"
}

.fa-simplybuilt:before {
    content: "\F215"
}

.fa-skyatlas:before {
    content: "\F216"
}

.fa-cart-plus:before {
    content: "\F217"
}

.fa-cart-arrow-down:before {
    content: "\F218"
}

.fa-diamond:before {
    content: "\F219"
}

.fa-ship:before {
    content: "\F21A"
}

.fa-user-secret:before {
    content: "\F21B"
}

.fa-motorcycle:before {
    content: "\F21C"
}

.fa-street-view:before {
    content: "\F21D"
}

.fa-heartbeat:before {
    content: "\F21E"
}

.fa-venus:before {
    content: "\F221"
}

.fa-mars:before {
    content: "\F222"
}

.fa-mercury:before {
    content: "\F223"
}

.fa-intersex:before,.fa-transgender:before {
    content: "\F224"
}

.fa-transgender-alt:before {
    content: "\F225"
}

.fa-venus-double:before {
    content: "\F226"
}

.fa-mars-double:before {
    content: "\F227"
}

.fa-venus-mars:before {
    content: "\F228"
}

.fa-mars-stroke:before {
    content: "\F229"
}

.fa-mars-stroke-v:before {
    content: "\F22A"
}

.fa-mars-stroke-h:before {
    content: "\F22B"
}

.fa-neuter:before {
    content: "\F22C"
}

.fa-genderless:before {
    content: "\F22D"
}

.fa-facebook-official:before {
    content: "\F230"
}

.fa-pinterest-p:before {
    content: "\F231"
}

.fa-whatsapp:before {
    content: "\F232"
}

.fa-server:before {
    content: "\F233"
}

.fa-user-plus:before {
    content: "\F234"
}

.fa-user-times:before {
    content: "\F235"
}

.fa-bed:before,.fa-hotel:before {
    content: "\F236"
}

.fa-viacoin:before {
    content: "\F237"
}

.fa-train:before {
    content: "\F238"
}

.fa-subway:before {
    content: "\F239"
}

.fa-medium:before {
    content: "\F23A"
}

.fa-y-combinator:before,.fa-yc:before {
    content: "\F23B"
}

.fa-optin-monster:before {
    content: "\F23C"
}

.fa-opencart:before {
    content: "\F23D"
}

.fa-expeditedssl:before {
    content: "\F23E"
}

.fa-battery-4:before,.fa-battery-full:before,.fa-battery:before {
    content: "\F240"
}

.fa-battery-3:before,.fa-battery-three-quarters:before {
    content: "\F241"
}

.fa-battery-2:before,.fa-battery-half:before {
    content: "\F242"
}

.fa-battery-1:before,.fa-battery-quarter:before {
    content: "\F243"
}

.fa-battery-0:before,.fa-battery-empty:before {
    content: "\F244"
}

.fa-mouse-pointer:before {
    content: "\F245"
}

.fa-i-cursor:before {
    content: "\F246"
}

.fa-object-group:before {
    content: "\F247"
}

.fa-object-ungroup:before {
    content: "\F248"
}

.fa-sticky-note:before {
    content: "\F249"
}

.fa-sticky-note-o:before {
    content: "\F24A"
}

.fa-cc-jcb:before {
    content: "\F24B"
}

.fa-cc-diners-club:before {
    content: "\F24C"
}

.fa-clone:before {
    content: "\F24D"
}

.fa-balance-scale:before {
    content: "\F24E"
}

.fa-hourglass-o:before {
    content: "\F250"
}

.fa-hourglass-1:before,.fa-hourglass-start:before {
    content: "\F251"
}

.fa-hourglass-2:before,.fa-hourglass-half:before {
    content: "\F252"
}

.fa-hourglass-3:before,.fa-hourglass-end:before {
    content: "\F253"
}

.fa-hourglass:before {
    content: "\F254"
}

.fa-hand-grab-o:before,.fa-hand-rock-o:before {
    content: "\F255"
}

.fa-hand-paper-o:before,.fa-hand-stop-o:before {
    content: "\F256"
}

.fa-hand-scissors-o:before {
    content: "\F257"
}

.fa-hand-lizard-o:before {
    content: "\F258"
}

.fa-hand-spock-o:before {
    content: "\F259"
}

.fa-hand-pointer-o:before {
    content: "\F25A"
}

.fa-hand-peace-o:before {
    content: "\F25B"
}

.fa-trademark:before {
    content: "\F25C"
}

.fa-registered:before {
    content: "\F25D"
}

.fa-creative-commons:before {
    content: "\F25E"
}

.fa-gg:before {
    content: "\F260"
}

.fa-gg-circle:before {
    content: "\F261"
}

.fa-tripadvisor:before {
    content: "\F262"
}

.fa-odnoklassniki:before {
    content: "\F263"
}

.fa-odnoklassniki-square:before {
    content: "\F264"
}

.fa-get-pocket:before {
    content: "\F265"
}

.fa-wikipedia-w:before {
    content: "\F266"
}

.fa-safari:before {
    content: "\F267"
}

.fa-chrome:before {
    content: "\F268"
}

.fa-firefox:before {
    content: "\F269"
}

.fa-opera:before {
    content: "\F26A"
}

.fa-internet-explorer:before {
    content: "\F26B"
}

.fa-television:before,.fa-tv:before {
    content: "\F26C"
}

.fa-contao:before {
    content: "\F26D"
}

.fa-500px:before {
    content: "\F26E"
}

.fa-amazon:before {
    content: "\F270"
}

.fa-calendar-plus-o:before {
    content: "\F271"
}

.fa-calendar-minus-o:before {
    content: "\F272"
}

.fa-calendar-times-o:before {
    content: "\F273"
}

.fa-calendar-check-o:before {
    content: "\F274"
}

.fa-industry:before {
    content: "\F275"
}

.fa-map-pin:before {
    content: "\F276"
}

.fa-map-signs:before {
    content: "\F277"
}

.fa-map-o:before {
    content: "\F278"
}

.fa-map:before {
    content: "\F279"
}

.fa-commenting:before {
    content: "\F27A"
}

.fa-commenting-o:before {
    content: "\F27B"
}

.fa-houzz:before {
    content: "\F27C"
}

.fa-vimeo:before {
    content: "\F27D"
}

.fa-black-tie:before {
    content: "\F27E"
}

.fa-fonticons:before {
    content: "\F280"
}

.fa-reddit-alien:before {
    content: "\F281"
}

.fa-edge:before {
    content: "\F282"
}

.fa-credit-card-alt:before {
    content: "\F283"
}

.fa-codiepie:before {
    content: "\F284"
}

.fa-modx:before {
    content: "\F285"
}

.fa-fort-awesome:before {
    content: "\F286"
}

.fa-usb:before {
    content: "\F287"
}

.fa-product-hunt:before {
    content: "\F288"
}

.fa-mixcloud:before {
    content: "\F289"
}

.fa-scribd:before {
    content: "\F28A"
}

.fa-pause-circle:before {
    content: "\F28B"
}

.fa-pause-circle-o:before {
    content: "\F28C"
}

.fa-stop-circle:before {
    content: "\F28D"
}

.fa-stop-circle-o:before {
    content: "\F28E"
}

.fa-shopping-bag:before {
    content: "\F290"
}

.fa-shopping-basket:before {
    content: "\F291"
}

.fa-hashtag:before {
    content: "\F292"
}

.fa-bluetooth:before {
    content: "\F293"
}

.fa-bluetooth-b:before {
    content: "\F294"
}

.fa-percent:before {
    content: "\F295"
}

.fa-gitlab:before {
    content: "\F296"
}

.fa-wpbeginner:before {
    content: "\F297"
}

.fa-wpforms:before {
    content: "\F298"
}

.fa-envira:before {
    content: "\F299"
}

.fa-universal-access:before {
    content: "\F29A"
}

.fa-wheelchair-alt:before {
    content: "\F29B"
}

.fa-question-circle-o:before {
    content: "\F29C"
}

.fa-blind:before {
    content: "\F29D"
}

.fa-audio-description:before {
    content: "\F29E"
}

.fa-volume-control-phone:before {
    content: "\F2A0"
}

.fa-braille:before {
    content: "\F2A1"
}

.fa-assistive-listening-systems:before {
    content: "\F2A2"
}

.fa-american-sign-language-interpreting:before,.fa-asl-interpreting:before {
    content: "\F2A3"
}

.fa-deaf:before,.fa-deafness:before,.fa-hard-of-hearing:before {
    content: "\F2A4"
}

.fa-glide:before {
    content: "\F2A5"
}

.fa-glide-g:before {
    content: "\F2A6"
}

.fa-sign-language:before,.fa-signing:before {
    content: "\F2A7"
}

.fa-low-vision:before {
    content: "\F2A8"
}

.fa-viadeo:before {
    content: "\F2A9"
}

.fa-viadeo-square:before {
    content: "\F2AA"
}

.fa-snapchat:before {
    content: "\F2AB"
}

.fa-snapchat-ghost:before {
    content: "\F2AC"
}

.fa-snapchat-square:before {
    content: "\F2AD"
}

.fa-pied-piper:before {
    content: "\F2AE"
}

.fa-first-order:before {
    content: "\F2B0"
}

.fa-yoast:before {
    content: "\F2B1"
}

.fa-themeisle:before {
    content: "\F2B2"
}

.fa-google-plus-circle:before,.fa-google-plus-official:before {
    content: "\F2B3"
}

.fa-fa:before,.fa-font-awesome:before {
    content: "\F2B4"
}

.fa-handshake-o:before {
    content: "\F2B5"
}

.fa-envelope-open:before {
    content: "\F2B6"
}

.fa-envelope-open-o:before {
    content: "\F2B7"
}

.fa-linode:before {
    content: "\F2B8"
}

.fa-address-book:before {
    content: "\F2B9"
}

.fa-address-book-o:before {
    content: "\F2BA"
}

.fa-address-card:before,.fa-vcard:before {
    content: "\F2BB"
}

.fa-address-card-o:before,.fa-vcard-o:before {
    content: "\F2BC"
}

.fa-user-circle:before {
    content: "\F2BD"
}

.fa-user-circle-o:before {
    content: "\F2BE"
}

.fa-user-o:before {
    content: "\F2C0"
}

.fa-id-badge:before {
    content: "\F2C1"
}

.fa-drivers-license:before,.fa-id-card:before {
    content: "\F2C2"
}

.fa-drivers-license-o:before,.fa-id-card-o:before {
    content: "\F2C3"
}

.fa-quora:before {
    content: "\F2C4"
}

.fa-free-code-camp:before {
    content: "\F2C5"
}

.fa-telegram:before {
    content: "\F2C6"
}

.fa-thermometer-4:before,.fa-thermometer-full:before,.fa-thermometer:before {
    content: "\F2C7"
}

.fa-thermometer-3:before,.fa-thermometer-three-quarters:before {
    content: "\F2C8"
}

.fa-thermometer-2:before,.fa-thermometer-half:before {
    content: "\F2C9"
}

.fa-thermometer-1:before,.fa-thermometer-quarter:before {
    content: "\F2CA"
}

.fa-thermometer-0:before,.fa-thermometer-empty:before {
    content: "\F2CB"
}

.fa-shower:before {
    content: "\F2CC"
}

.fa-bath:before,.fa-bathtub:before,.fa-s15:before {
    content: "\F2CD"
}

.fa-podcast:before {
    content: "\F2CE"
}

.fa-window-maximize:before {
    content: "\F2D0"
}

.fa-window-minimize:before {
    content: "\F2D1"
}

.fa-window-restore:before {
    content: "\F2D2"
}

.fa-times-rectangle:before,.fa-window-close:before {
    content: "\F2D3"
}

.fa-times-rectangle-o:before,.fa-window-close-o:before {
    content: "\F2D4"
}

.fa-bandcamp:before {
    content: "\F2D5"
}

.fa-grav:before {
    content: "\F2D6"
}

.fa-etsy:before {
    content: "\F2D7"
}

.fa-imdb:before {
    content: "\F2D8"
}

.fa-ravelry:before {
    content: "\F2D9"
}

.fa-eercast:before {
    content: "\F2DA"
}

.fa-microchip:before {
    content: "\F2DB"
}

.fa-snowflake-o:before {
    content: "\F2DC"
}

.fa-superpowers:before {
    content: "\F2DD"
}

.fa-wpexplorer:before {
    content: "\F2DE"
}

.fa-meetup:before {
    content: "\F2E0"
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0
}

.sr-only-focusable:active,.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

@font-face {
    font-family: me_quran;
    src: url(/fonts/me_quran.ttf?a79b204e9c3055c77f0d81921bd881c2)
}

::-webkit-scrollbar {
    width: 5px;
    height: 15px
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3)
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #aaa
}

.quran {
    font-family: me_quran;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.bracket {
    font-family: Simplified Arabic!important
}

@media (max-width: 992px) {
    .col-md-50 {
        width:auto
    }
}

body {
    background-color: #f2f2f2;
    background-image: url("/img/bg_foot.png"),url("/img/bg_zkharf.png");
    background-repeat: repeat-x,repeat;
    background-position: bottom,50%;
    font-family: Noto Naskh Arabic,Source Sans Pro,Helvetica Neue,Helvetica,Arial,sans-serif;
    padding-top: 50px;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden
}

.navbar-default {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 2000;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-box-shadow: 0 0 2px rgba(6,8,8,.15);
    box-shadow: 0 0 2px rgba(6,8,8,.15)
}

.navbar-brand {
    padding: 5px 15px
}

ul.menu.top-menu li a {
    font-size: 15px;
    font-family: Noto Kufi Arabic;
    border-left: 1px solid #f2f2f2
}

ul.menu.top-menu li a.active,ul.menu.top-menu li a:hover {
    background-color: #f9f9f9;
    color: #896c3a
}

a {
    color: #3a3a3a
}

a:focus,a:hover {
    color: #9e9e9e;
    text-decoration: none
}

.under-header {
    background-color: #f2f2f2;
    color: #717171;
    border-bottom: 1px solid #ddd;
    z-index: 1
}

.main-title {
    font-size: 50px;
    font-weight: 700;
    text-shadow: 2px 2px 0 #ababab;
    text-align: center;
    white-space: normal;
    margin: 50px 0;
    font-family: Noto Kufi Arabic
}

#right-menu {
    height: calc(100vh - 52px);
    position: fixed;
    overflow: auto;
    top: 52px;
    background-color: #272525;
    color: #1b7ba6;
    direction: ltr;
    -webkit-box-shadow: -3px 0 9px -3px #1b1b1b,inset 0 10px 7px -10px #fff;
    box-shadow: -3px 0 9px -3px #1b1b1b,inset 0 10px 7px -10px #fff
}

#right-menu>* {
    direction: rtl
}

.container.main-content {
    padding: 20px 0 0;
    width: auto
}

.container.main-content>.box {
    background-color: hsla(0,0%,100%,.65);
    border: 1px dashed #ccc;
    border-radius: 20px;
    margin: 22px 22px 60px;
    -webkit-box-shadow: 0 1px 15px -5px #ababab;
    box-shadow: 0 1px 15px -5px #ababab
}

.site-title {
    font-size: 20px;
    color: #8a6d3b;
    background: url("/img/xqq53345.png") no-repeat bottom -10px center;
    background-size: 163%;
    line-height: 50px
}

.breadcrumb,.site-title {
    font-family: Noto Kufi Arabic
}

.breadcrumb {
    background: #f2f2f2 url("/img/bg_zkharf.png") repeat;
    padding: 0;
    line-height: 48px;
    font-size: 16px;
    margin: 0
}

.contact-form {
    display: inline-block
}

.site-title-nav {
    margin-left: 35px
}

@media (min-width: 768px) {
    .navbar-collapse {
        position:relative;
        padding-top: 30px!important;
        max-height: 270px;
        padding-top: 0!important;
        margin-right: 50px!important;
        padding-right: 0!important
    }

    .navbar-collapse form[role=search].active button[type=submit] {
        background-color: #e7e7e7
    }
}

@media (max-width: 767px) {
    .navbar-collapse form[role=search] {
        right:-16px;
        float: none
    }
}

.navbar-collapse form[role=search] {
    position: relative;
    left: -30px;
    padding: 0;
    margin: 0;
    z-index: 1;
    width: 57px;
    overflow: hidden;
    -webkit-transition: all .5s;
    transition: all .5s;
    max-width: 90%
}

.navbar-collapse form[role=search].active {
    width: 300px
}

.navbar-collapse form[role=search] button,.navbar-collapse form[role=search] input {
    padding: 0 20px;
    border-radius: 0!important;
    color: #777;
    background: #f9f9f9;
    border: 1px solid #f2f2f2;
    -webkit-box-shadow: none;
    box-shadow: none;
    line-height: 48px;
    -webkit-transition: all .5s;
    transition: all .5s;
    height: 50px;
    float: left
}

.navbar-collapse form[role=search] button:focus {
    outline: none
}

.navbar-collapse form[role=search] input {
    font-style: italic;
    display: none
}

.navbar-collapse form[role=search].active .input-group {
    float: left;
    max-width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.navbar-collapse form[role=search].active .input-group .input-group-btn {
    width: auto
}

.navbar-collapse form[role=search].active .input-group input[type=search] {
    display: inline-block
}

.col-md-50 {
    width: 50%
}

.label {
    padding: 0 5px;
    border-radius: 4px
}

.ltr {
    direction: ltr
}

.box-footer {
    padding: 5px 10px
}

.search-input .glyphicon-search {
    position: relative;
    float: left;
    margin-top: -27px;
    margin-left: 10px;
    color: #d1d1d1
}

.inline-form.form-closed {
    max-height: 0;
    padding: 0
}

.inline-form {
    overflow: hidden;
    max-height: 170px;
    border-bottom: 1px solid #eee;
    margin-top: -12px;
    padding-top: 15px;
    -webkit-transition: all 1s;
    transition: all 1s
}

.search-open,.search-open:focus,.search-open:hover {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0 0 5px 5px;
    border: 1px solid #eee
}

.search-results-header {
    background-color: #fbfbfb;
    padding: 10px;
    border: 1px dashed #e8e8e8;
    border-radius: 10px;
    -webkit-box-shadow: 0 0 7px -3px #777;
    box-shadow: 0 0 7px -3px #777
}

.search-result {
    margin: 10px 0;
    border: 2px solid #e8e8e8;
    color: #777;
    background-color: #fff;
    padding: 10px;
    border-radius: 10px
}

.search-result-title,.tag-box-title {
    border-bottom: 1px dashed #ccc;
    margin: 0 0 5px
}

.search-result-title a {
    font-weight: 700;
    color: #ad7f2e
}

.search-result-title a:hover {
    text-shadow: 2px 2px 0 #eee
}

.search-result-title a:hover:after {
    content: " \F100   ";
    font-family: FontAwesome;
    position: absolute
}

.search-result-path a {
    font-size: 14px;
    color: #aaa;
    border-bottom: 1px dashed #ccc;
    margin: 0 0 3px;
    display: inline-block
}

.search-result-path a:hover {
    color: #dea849
}

.search-result-path>:not(:last-child):after {
    content: " \BB   ";
    color: #ccb181
}

.search-result-path:before {
    content: " \2756   "
}

.search-result-text {
    font-size: 15px
}

.breadcrumb a {
    color: #8a6d3b;
    -webkit-transition: all .2s;
    transition: all .2s;
    text-shadow: 0 0 0 #ecc075
}

.breadcrumb a:hover {
    text-shadow: 0 0 12px #ffda9a
}

.book-header {
    border-bottom: 1px solid #f4f4f4
}

#txt-read {
    text-align: justify;
    line-height: 30px
}

.box-disabled {
    display: none
}

[footnote=index] {
    display: inline-block
}

.note-text {
    font-size: 12px;
    color: #ff6a83
}

.input-group-addon:first-child {
    border-right: 1px solid #ccd0d2!important
}

.text-line {
    white-space: nowrap;
    text-overflow: ellipsis
}

.t-10 {
    font-size: 10px!important
}

.t-12 {
    font-size: 12px!important
}

.fz-13 {
    font-size: 13px!important
}

.fz-14 {
    font-size: 14px!important
}

.fz-15 {
    font-size: 15px!important
}

.fz-20 {
    font-size: 20px!important;
    line-height: 20px
}

.over-hidden {
    overflow: hidden
}

.p-0 {
    padding: 0!important
}

.m-0 {
    margin: 0!important
}

.p-r-0 {
    padding-right: 0!important
}

.p-l-0 {
    padding-left: 0!important
}

.align-v-m {
    vertical-align: middle!important
}

.d-table-cell {
    display: table-cell
}

.color-gray {
    color: #aaa
}

.hr {
    height: 10px;
    clear: both;
    text-align: center;
    margin: 10px
}

.hr,.hr img {
    max-width: 100%
}

.hr img {
    height: 100%;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    opacity: .4
}

.site-color {
    color: #a18c74!important
}

.facebook-color {
    color: #4267b2!important
}

.user-menu .user-image {
    width: 25px;
    height: 25px;
    border-radius: 50%
}

.pagination {
    margin: 0
}

#share {
    font-size: 12px
}

#share a {
    border-radius: 5px
}

.modal-body {
    display: inline-block;
    width: 100%
}

.main-container {
    position: relative;
    margin-bottom: 50px
}

.share-btns {
    clear: both;
    margin: 10px 0 0
}

.share-btns a {
    padding: 0 5px;
    margin: 0 10px;
    color: #aaa;
    border: 1px solid transparent;
    border-radius: 3px
}

.share-btns a:hover {
    border-color: #f9cb86;
    text-decoration: none;
    color: #f9cb86
}

.share-btns a.btn-like.disabled,.share-btns a.btn-like.disabled .fa {
    opacity: 1;
    color: #4ac355
}

.share-btns a.btn-unlike.disabled,.share-btns a.btn-unlike.disabled .fa {
    opacity: 1;
    color: #e91e63
}

.social-likes {
    width: 30px;
    overflow: hidden;
    white-space: nowrap;
    direction: ltr;
    -webkit-transition: all .2s;
    transition: all .2s;
    background: hsla(0,0%,100%,.9);
    height: 25px;
    padding-top: 5px
}

.social-likes.expanded {
    width: 160px
}

.social-likes .btn {
    float: left
}

.social-likes .fa-twitter {
    color: #00aced
}

.social-likes .fa-facebook-official {
    color: #3b5998
}

.social-likes .fa-google-plus {
    color: #dd4b39
}

.social-likes .fa-pinterest-p {
    color: #bd081b
}

.social-likes .fa-whatsapp {
    background: #25d366;
    color: #fff;
    padding: 1px 3px;
    border-radius: 5px
}

.social-likes .fa:hover {
    opacity: .5
}

.modal {
    z-index: 2050
}

.modal-backdrop {
    z-index: 2040
}

.pre {
    white-space: pre
}

.pre-line {
    white-space: pre-line
}

.text-blue {
    color: #0073b7!important
}

.chosen-container {
    min-width: 100%
}

.navbar-collapse .user-menu>a.dropdown-toggle {
    padding: 12px
}

.noselect,.noselect * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.f1 {
    font-family: Noto Kufi Arabic
}

.iframe-box-outter {
    text-align: center;
    float: right;
    width: 50%
}

.iframe-box-outter .iframe-box {
    position: relative;
    background: #ddd;
    border-radius: 6px;
    color: rgba(0,0,0,.8);
    text-shadow: 0 1px 0 #fff;
    line-height: 0;
    margin: 0 0 30px;
    display: inline-block;
    width: 400px;
    max-width: 100%;
    height: 200px;
    z-index: 1
}

.iframe-box-outter .iframe-box:after,.iframe-box-outter .iframe-box:before {
    z-index: 0;
    position: absolute;
    content: "";
    bottom: 15px;
    left: 10px;
    width: 50%;
    top: 80%;
    background: rgba(0,0,0,.7);
    -webkit-box-shadow: 0 25px 17px rgba(0,0,0,.7);
    box-shadow: 0 25px 17px rgba(0,0,0,.7);
    -webkit-transform: rotate(-3deg);
    transform: rotate(-3deg)
}

.iframe-box-outter .iframe-box:after {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
    right: 10px;
    left: auto
}

.iframe-box-outter .iframe-box iframe {
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    z-index: 1;
    background: #ddd;
    height: 100%
}

#loading-ajax {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: hsla(0,0%,100%,.5);
    text-align: center;
    z-index: 10001
}

.center-vertical {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.box-site {
    border-color: #a18c74
}

.box-site>.box-heading {
    color: #fff;
    background-color: #a18c74;
    border-color: #faebcc
}

.box-site>.box-heading+.box-collapse>.box-body {
    border-top-color: #a18c74
}

.box-site>.box-heading .badge {
    color: #a18c74;
    background-color: #fff
}

.box-site>.box-footer+.box-collapse>.box-body {
    border-bottom-color: #a18c74
}

.btn-site {
    color: #fff;
    background-color: #a18c74;
    border-color: #a18c74
}

.btn-site.focus,.btn-site:focus {
    color: #fff;
    background-color: #87725b;
    border-color: #594c3c
}

.btn-site.active,.btn-site:active,.btn-site:hover,.open>.btn-site.dropdown-toggle {
    color: #fff;
    background-color: #87725b;
    border-color: #816d57
}

.btn-site.active.focus,.btn-site.active:focus,.btn-site.active:hover,.btn-site:active.focus,.btn-site:active:focus,.btn-site:active:hover,.open>.btn-site.dropdown-toggle.focus,.open>.btn-site.dropdown-toggle:focus,.open>.btn-site.dropdown-toggle:hover {
    color: #fff;
    background-color: #72604d;
    border-color: #594c3c
}

.btn-site.active,.btn-site:active,.open>.btn-site.dropdown-toggle {
    background-image: none
}

.btn-site.disabled.focus,.btn-site.disabled:focus,.btn-site.disabled:hover,.btn-site[disabled].focus,.btn-site[disabled]:focus,.btn-site[disabled]:hover,fieldset[disabled] .btn-site.focus,fieldset[disabled] .btn-site:focus,fieldset[disabled] .btn-site:hover {
    background-color: #a18c74;
    border-color: #a18c74
}

.btn-site .badge {
    color: #a18c74;
    background-color: #fff
}

.btn-facebook {
    color: #fff;
    background-color: #4267b2;
    border-color: #4267b2
}

.btn-facebook.focus,.btn-facebook:focus {
    color: #fff;
    background-color: #34518d;
    border-color: #203155
}

.btn-facebook.active,.btn-facebook:active,.btn-facebook:hover,.open>.btn-facebook.dropdown-toggle {
    color: #fff;
    background-color: #34518d;
    border-color: #314d85
}

.btn-facebook.active.focus,.btn-facebook.active:focus,.btn-facebook.active:hover,.btn-facebook:active.focus,.btn-facebook:active:focus,.btn-facebook:active:hover,.open>.btn-facebook.dropdown-toggle.focus,.open>.btn-facebook.dropdown-toggle:focus,.open>.btn-facebook.dropdown-toggle:hover {
    color: #fff;
    background-color: #2b4273;
    border-color: #203155
}

.btn-facebook.active,.btn-facebook:active,.open>.btn-facebook.dropdown-toggle {
    background-image: none
}

.btn-facebook.disabled.focus,.btn-facebook.disabled:focus,.btn-facebook.disabled:hover,.btn-facebook[disabled].focus,.btn-facebook[disabled]:focus,.btn-facebook[disabled]:hover,fieldset[disabled] .btn-facebook.focus,fieldset[disabled] .btn-facebook:focus,fieldset[disabled] .btn-facebook:hover {
    background-color: #4267b2;
    border-color: #4267b2
}

.btn-facebook .badge {
    color: #4267b2;
    background-color: #fff
}

.bg-site {
    color: #fff
}

.bg-site,.label-site {
    background-color: #a18c74
}

.label-site[href]:focus,.label-site[href]:hover {
    background-color: #87725b
}
