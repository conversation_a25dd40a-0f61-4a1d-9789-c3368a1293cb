var X=Object.defineProperty;var b=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var w=Object.prototype.hasOwnProperty;var D=(i,r)=>()=>(i&&(r=i(i=0)),r);var A=(i,r,h,g)=>{if(r&&typeof r=="object"||typeof r=="function")for(let p of y(r))!w.call(i,p)&&p!==h&&X(i,p,{get:()=>r[p],enumerable:!(g=b(r,p))||g.enumerable});return i};var R=i=>A(X({},"__esModule",{value:!0}),i);var T={};var Y=D(()=>{(function(i,r,h,g){var p="ontouchstart"in h,E=h.dir=="rtl",v=function(){var t=h.createElement("div"),s=h.documentElement;if(!("pointerEvents"in t.style))return!1;t.style.pointerEvents="auto",t.style.pointerEvents="x",s.appendChild(t);var n=r.getComputedStyle&&r.getComputedStyle(t,"").pointerEvents==="auto";return s.removeChild(t),!!n}(),C={listNodeName:"ol",itemNodeName:"li",rootClass:"dd",listClass:"dd-list",itemClass:"dd-item",dragClass:"dd-dragel",handleClass:"dd-handle",collapsedClass:"dd-collapsed",placeClass:"dd-placeholder",noDragClass:"dd-nodrag",emptyClass:"dd-empty",expandBtnHTML:'<button data-action="expand" type="button">Expand</button>',collapseBtnHTML:'<button data-action="collapse" type="button">Collapse</button>',group:0,maxDepth:5,threshold:20};function N(t,s){this.w=i(h),this.el=i(t),this.options=i.extend({},C,s),this.init()}N.prototype={init:function(){var t=this;t.reset(),t.el.data("nestable-group",this.options.group),t.placeEl=i('<div class="'+t.options.placeClass+'"/>'),i.each(this.el.find(t.options.itemNodeName),function(l,d){t.setParent(i(d))}),t.el.on("click","button",function(l){if(!t.dragEl){var d=i(l.currentTarget),a=d.data("action"),e=d.parent(t.options.itemNodeName);a==="collapse"&&t.collapseItem(e),a==="expand"&&t.expandItem(e)}});var s=function(l){var d=i(l.target);if(!d.hasClass(t.options.handleClass)){if(d.closest("."+t.options.noDragClass).length)return;d=d.closest("."+t.options.handleClass)}!d.length||t.dragEl||(t.isTouch=/^touch/.test(l.type),!(t.isTouch&&l.touches.length!==1)&&(l.preventDefault(),t.dragStart(l.touches?l.touches[0]:l)))},n=function(l){t.dragEl&&(l.preventDefault(),t.dragMove(l.touches?l.touches[0]:l))},o=function(l){t.dragEl&&(l.preventDefault(),t.dragStop(l.touches?l.touches[0]:l))};p&&(t.el[0].addEventListener("touchstart",s,!1),r.addEventListener("touchmove",n,!1),r.addEventListener("touchend",o,!1),r.addEventListener("touchcancel",o,!1)),t.el.on("mousedown",s),t.w.on("mousemove",n),t.w.on("mouseup",o)},serialize:function(){var t,s=0,n=this;return step=function(o,l){var d=[],a=o.children(n.options.itemNodeName);return a.each(function(){var e=i(this),f=i.extend({},e.data()),c=e.children(n.options.listNodeName);c.length&&(f.children=step(c,l+1)),d.push(f)}),d},t=step(n.el.find(n.options.listNodeName).first(),s),t},serialise:function(){return this.serialize()},reset:function(){this.mouse={offsetX:0,offsetY:0,startX:0,startY:0,lastX:0,lastY:0,nowX:0,nowY:0,distX:0,distY:0,dirAx:0,dirX:0,dirY:0,lastDirX:0,lastDirY:0,distAxX:0,distAxY:0},this.isTouch=!1,this.moving=!1,this.dragEl=null,this.dragRootEl=null,this.dragDepth=0,this.hasNewRoot=!1,this.pointEl=null},expandItem:function(t){t.removeClass(this.options.collapsedClass),t.children('[data-action="expand"]').hide(),t.children('[data-action="collapse"]').show(),t.children(this.options.listNodeName).show()},collapseItem:function(t){var s=t.children(this.options.listNodeName);s.length&&(t.addClass(this.options.collapsedClass),t.children('[data-action="collapse"]').hide(),t.children('[data-action="expand"]').show(),t.children(this.options.listNodeName).hide())},expandAll:function(){var t=this;t.el.find(t.options.itemNodeName).each(function(){t.expandItem(i(this))})},collapseAll:function(){var t=this;t.el.find(t.options.itemNodeName).each(function(){t.collapseItem(i(this))})},setParent:function(t){t.children(this.options.listNodeName).length&&(t.prepend(i(this.options.expandBtnHTML)),t.prepend(i(this.options.collapseBtnHTML))),t.children('[data-action="expand"]').hide()},unsetParent:function(t){t.removeClass(this.options.collapsedClass),t.children("[data-action]").remove(),t.children(this.options.listNodeName).remove()},dragStart:function(t){var s=this.mouse,n=i(t.target),o=n.closest(this.options.itemNodeName);this.placeEl.css("height",o.height()),s.offsetX=t.offsetX!==g?t.offsetX:t.pageX-n.offset().left,s.offsetY=t.offsetY!==g?t.offsetY:t.pageY-n.offset().top,s.startX=s.lastX=t.pageX,s.startY=s.lastY=t.pageY,this.dragRootEl=this.el,this.dragEl=i(h.createElement(this.options.listNodeName)).addClass(this.options.listClass+" "+this.options.dragClass),this.dragEl.css("width",o.width()),o.after(this.placeEl),o[0].parentNode.removeChild(o[0]),o.appendTo(this.dragEl),i(h.body).append(this.dragEl),E?this.dragEl.css({right:i(h).width()-t.pageX-s.offsetX,top:t.pageY-s.offsetY}):this.dragEl.css({left:t.pageX-s.offsetX,top:t.pageY-s.offsetY});var l,d,a=this.dragEl.find(this.options.itemNodeName);for(l=0;l<a.length;l++)d=i(a[l]).parents(this.options.listNodeName).length,d>this.dragDepth&&(this.dragDepth=d)},dragStop:function(t){var s=this.dragEl.children(this.options.itemNodeName).first();s[0].parentNode.removeChild(s[0]),this.placeEl.replaceWith(s),this.dragEl.remove(),this.el.trigger("change"),this.hasNewRoot&&this.dragRootEl.trigger("change"),this.reset()},dragMove:function(t){var s,n,o,l,d,a=this.options,e=this.mouse;E?this.dragEl.css({right:i(r).width()-t.pageX-e.offsetX,top:t.pageY-e.offsetY}):this.dragEl.css({left:t.pageX-e.offsetX,top:t.pageY-e.offsetY}),e.lastX=e.nowX,e.lastY=e.nowY,e.nowX=t.pageX,e.nowY=t.pageY,e.distX=e.nowX-e.lastX,e.distY=e.nowY-e.lastY,e.lastDirX=e.dirX,e.lastDirY=e.dirY,e.dirX=e.distX===0?0:e.distX>0?1:-1,e.dirY=e.distY===0?0:e.distY>0?1:-1;var f=Math.abs(e.distX)>Math.abs(e.distY)?1:0;if(!e.moving){e.dirAx=f,e.moving=!0;return}e.dirAx!==f?(e.distAxX=0,e.distAxY=0):(e.distAxX+=Math.abs(e.distX),e.dirX!==0&&e.dirX!==e.lastDirX&&(e.distAxX=0),e.distAxY+=Math.abs(e.distY),e.dirY!==0&&e.dirY!==e.lastDirY&&(e.distAxY=0)),e.dirAx=f,e.dirAx&&e.distAxX>=a.threshold&&(e.distAxX=0,o=this.placeEl.prev(a.itemNodeName),e.distX>0&&o.length&&!o.hasClass(a.collapsedClass)&&(s=o.find(a.listNodeName).last(),d=this.placeEl.parents(a.listNodeName).length,d+this.dragDepth<=a.maxDepth&&(s.length?(s=o.children(a.listNodeName).last(),s.append(this.placeEl)):(s=i("<"+a.listNodeName+"/>").addClass(a.listClass),s.append(this.placeEl),o.append(s),this.setParent(o)))),e.distX<0&&(l=this.placeEl.next(a.itemNodeName),l.length||(n=this.placeEl.parent(),this.placeEl.closest(a.itemNodeName).after(this.placeEl),n.children().length||this.unsetParent(n.parent()))));var c=!1;if(v||(this.dragEl[0].style.visibility="hidden"),this.pointEl=i(h.elementFromPoint(t.pageX-h.body.scrollLeft,t.pageY-(r.pageYOffset||h.documentElement.scrollTop))),v||(this.dragEl[0].style.visibility="visible"),this.pointEl.hasClass(a.handleClass)&&(this.pointEl=this.pointEl.parent(a.itemNodeName)),this.pointEl.hasClass(a.emptyClass))c=!0;else if(!this.pointEl.length||!this.pointEl.hasClass(a.itemClass))return;var u=this.pointEl.closest("."+a.rootClass),m=this.dragRootEl.data("nestable-id")!==u.data("nestable-id");if(!e.dirAx||m||c){if(m&&a.group!==u.data("nestable-group")||(d=this.dragDepth-1+this.pointEl.parents(a.listNodeName).length,d>a.maxDepth))return;var x=t.pageY<this.pointEl.offset().top+this.pointEl.height()/2;n=this.placeEl.parent(),c?(s=i(h.createElement(a.listNodeName)).addClass(a.listClass),s.append(this.placeEl),this.pointEl.replaceWith(s)):x?this.pointEl.before(this.placeEl):this.pointEl.after(this.placeEl),n.children().length||this.unsetParent(n.parent()),this.dragRootEl.find(a.itemNodeName).length||this.dragRootEl.append('<div class="'+a.emptyClass+'"/>'),m&&(this.dragRootEl=u,this.hasNewRoot=this.el[0]!==this.dragRootEl[0])}}},i.fn.nestable=function(t){var s=this,n=this;return s.each(function(){var o=i(this).data("nestable");o?typeof t=="string"&&typeof o[t]=="function"&&(n=o[t]()):(i(this).data("nestable",new N(this,t)),i(this).data("nestable-id",new Date().getTime()))}),n||s}})(window.jQuery||window.Zepto,window,document)});Y();
/*!
 * Nestable jQuery Plugin - Copyright (c) 2012 David Bushell - http://dbushell.com/
 * Dual-licensed under the BSD or MIT licenses
 */
