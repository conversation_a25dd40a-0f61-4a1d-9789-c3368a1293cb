'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"flutter_bootstrap.js": "691bf58dd75c42c555bd72d76d98ae83",
"version.json": "a2a325b4de0018cae3597ee91d3b195d",
"index.html": "08d20e515c571013bf5be85f8ad9e70c",
"/": "08d20e515c571013bf5be85f8ad9e70c",
"main.dart.js": "2c6e73fab3fc0032fbcbc5e1c8130399",
"flutter.js": "76f08d47ff9f5715220992f993002504",
"favicon.png": "5dcef449791fa27946b3d35ad8803796",
"icons/Icon-192.png": "ac9a721a12bbc803b44f645561ecb1e1",
"icons/Icon-maskable-192.png": "c457ef57daa1d16f64b27b786ec2ea3c",
"icons/Icon-maskable-512.png": "301a7604d45b3e739efc881eb04896ea",
"icons/Icon-512.png": "96e752610906ba2a93c65f8abe1645f1",
"manifest.json": "59167e8d98b5a4d1dceac69dc4ff3364",
"assets/AssetManifest.json": "3e4d351af2f6fb57e11a9610da6ba8e2",
"assets/NOTICES": "8b69d99ec678ad7a909482a11632222f",
"assets/FontManifest.json": "1a6184c6ca656e8be25d25329c87e502",
"assets/AssetManifest.bin.json": "4ccea483fe397e83892689a2cc72be6b",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "d7d83bd9ee909f8a9b348f56ca7b68c6",
"assets/packages/youtube_player_flutter/assets/speedometer.webp": "50448630e948b5b3998ae5a5d112622b",
"assets/packages/flutter_inappwebview_web/assets/web/web_support.js": "509ae636cfdd93e49b5a6eaf0f06d79f",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css": "5a8d0222407e388155d7d1395a75d5b9",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html": "16911fcc170c8af1c5457940bd0bf055",
"assets/packages/wakelock_plus/assets/no_sleep.js": "7748a45cd593f33280669b29c2c8919a",
"assets/packages/feather_icons/fonts/feather.ttf": "0d0d92d310cc68e53796bf15c36838c2",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"assets/AssetManifest.bin": "a55734f3abc4ff8909d833543d1c15e6",
"assets/fonts/MaterialIcons-Regular.otf": "001de868c8d2ce83c43ac7f74c97df56",
"assets/assets/images/family_tree.png": "7d40c93f56af4c0ae2011fc55eeec7c7",
"assets/assets/images/pattern.png": "5a1fa00ae49efb7de2d02013765a6438",
"assets/assets/images/background.png": "b0897ff454cdf7f884a290bc37f80b40",
"assets/assets/images/smart-fingers.png": "e31a5f19d9db33c039d56a95d7d59eee",
"assets/assets/images/logo.png": "2bcfc2f4516e75464d9ef0ec86706d06",
"assets/assets/images/home_card_bg.png": "79bfee50007411dedcada1e138b162e0",
"assets/assets/images/sample.png": "de06c63bc0094963a1b18c052ef1a776",
"assets/assets/images/sample3.png": "4aba95430e4f11f2e8410147a449013a",
"assets/assets/images/sample2.png": "6578b5e268d1a441fa7c52a8de29a70b",
"assets/assets/images/appbar_bg.png": "c52c0fe0cb227846d28c1e673e846228",
"assets/assets/vectors/pdf-file.svg": "b28bf9ed9034068744d1c0c1623409df",
"assets/assets/vectors/managements.svg": "af1718baa330067e123b79c4b04ece6b",
"assets/assets/vectors/show_image.svg": "fb61f3be1d3bfa4da3fe9af5f40f51a5",
"assets/assets/vectors/events.svg": "c4ba8f756c90c15336a9e30629b74a7b",
"assets/assets/vectors/family.svg": "fc4c6c2bb2cfdbbfcbfeaf0c4ac156c6",
"assets/assets/vectors/tree-structure.svg": "643e1e25b2bfa016880624f17c7e064a",
"assets/assets/vectors/news.svg": "3a79f66b941e413dbcd5711112b9d478",
"assets/assets/vectors/no_result.svg": "362184ad94db8a6fb72c493997b15dee",
"assets/assets/vectors/images.svg": "7aa5e75bb04c35942567849270343284",
"assets/assets/vectors/father.svg": "ca1034a1c289d5034658580490f90dfd",
"assets/assets/vectors/play.svg": "21de4319a7c7b2ae7444a97e18dfac45",
"assets/assets/vectors/no_internet.svg": "5f1cded67ff827bd2d4798ea02780a02",
"assets/assets/vectors/memory.svg": "447f6da06fe01899732c8d4e6f59ea19",
"assets/assets/vectors/server_error.svg": "b5b0f1b4c44ea8d6db0c1cf00ef8988a",
"assets/assets/vectors/almaalem-logo.svg": "525286e18e400556c8821e98ac80511f",
"assets/assets/vectors/tree.svg": "57682fe4e949ad05ec31a35ad94b2c9e",
"assets/assets/vectors/medal.svg": "319f690d766a3ca7968d2fbb3adb782d",
"assets/assets/vectors/calendar.svg": "b3d6e64c582711b7961aee4c427535dd",
"assets/assets/vectors/logo.svg": "7ed73b693e0446033bdb5019b462444f",
"assets/assets/vectors/news2.svg": "de733d23ef59d95c1ba647588eaa3a1c",
"assets/assets/vectors/no_content.svg": "12c5a36d69469ef2dd8736a92d36afee",
"assets/assets/vectors/commitee.svg": "8fb9837dd9333ed6839f38004028aa26",
"service_worker.js": "2f6a7cdabcb11cf1887490040e913c50",
"canvaskit/skwasm_st.js": "d1326ceef381ad382ab492ba5d96f04d",
"canvaskit/skwasm.js": "f2ad9363618c5f62e813740099a80e63",
"canvaskit/skwasm.js.symbols": "80806576fa1056b43dd6d0b445b4b6f7",
"canvaskit/canvaskit.js.symbols": "68eb703b9a609baef8ee0e413b442f33",
"canvaskit/skwasm.wasm": "f0dfd99007f989368db17c9abeed5a49",
"canvaskit/chromium/canvaskit.js.symbols": "5a23598a2a8efd18ec3b60de5d28af8f",
"canvaskit/chromium/canvaskit.js": "ba4a8ae1a65ff3ad81c6818fd47e348b",
"canvaskit/chromium/canvaskit.wasm": "64a386c87532ae52ae041d18a32a3635",
"canvaskit/skwasm_st.js.symbols": "c7e7aac7cd8b612defd62b43e3050bdd",
"canvaskit/canvaskit.js": "6cfe36b4647fbfa15683e09e7dd366bc",
"canvaskit/canvaskit.wasm": "efeeba7dcc952dae57870d4df3111fad",
"canvaskit/skwasm_st.wasm": "56c3973560dfcbf28ce47cebe40f3206"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
