<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
    <base href="/app/">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description"
        content="تطبيق أسرة المشعل - منصة متكاملة لإدارة شؤون العائلة ومشاركة المعلومات بين أفرادها">
    <meta name="keywords" content="أسرة المشعل, شجرة العائلة, نسب العائلة, مناسبات, أخبار, ذكريات, لجان, جائزة التفوق">
    <meta name="author" content="أسرة المشعل">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="أسرة المشعل">
    <link rel="apple-touch-icon" href="icons/Icon-192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="icons/Icon-152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="icons/Icon-180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="icons/Icon-167.png">

    <!-- إضافة أيقونات شاشة البدء لـ iOS -->
    <link rel="apple-touch-startup-image" href="icons/splash-2048x2732.png"
        media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-1668x2388.png"
        media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-1668x2224.png"
        media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-1536x2048.png"
        media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-1242x2688.png"
        media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-1125x2436.png"
        media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-828x1792.png"
        media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-750x1334.png"
        media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
    <link rel="apple-touch-startup-image" href="icons/splash-640x1136.png"
        media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="shortcut icon" type="image/png" href="favicon.png" />

    <!-- Theme color for browser -->
    <meta name="theme-color" content="#B6977A">

    <title>أسرة المشعل</title>
    <link rel="manifest" href="manifest.json">

    <!-- بيانات منظمة لمحركات البحث -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "أسرة المشعل",
      "applicationCategory": "SocialApplication",
      "operatingSystem": "Web, iOS, Android",
      "description": "تطبيق أسرة المشعل - منصة متكاملة لإدارة شؤون العائلة ومشاركة المعلومات بين أفرادها",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "SAR"
      }
    }
  </script>

    <style>
        body {
            background-color: #F5F5F5;
            height: 100vh;
            margin: 0;
            overflow: hidden;
            position: fixed;
            width: 100%;
            font-family: 'Tajawal', Arial, sans-serif;
        }

        .loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 80%;
            max-width: 300px;
        }

        .logo {
            width: 120px;
            height: 120px;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .progress {
            width: 100%;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
            margin: 20px auto;
            overflow: hidden;
        }

        .progress-bar {
            width: 30%;
            height: 100%;
            background-color: #B6977A;
            border-radius: 2px;
            animation: progress 1.5s ease-in-out infinite;
        }

        .loading-text {
            color: #666;
            font-family: 'Tajawal', Arial, sans-serif;
            font-size: 16px;
            margin-top: 10px;
        }

        @keyframes progress {
            0% {
                width: 0%;
                margin-left: 0;
            }

            50% {
                width: 70%;
            }

            100% {
                width: 0%;
                margin-left: 100%;
            }
        }

        /* تحميل خط تجوال للغة العربية */
        @font-face {
            font-family: 'Tajawal';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/tajawal/v3/Iura6YBj_oCad4k1nzSBC5xLhLFw4Q.woff2) format('woff2');
            unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
            font-display: swap;
        }

        /* التأكد من عدم تحرك الصفحة عند التحميل */
        html,
        body {
            touch-action: none;
            -ms-touch-action: none;
            overflow: hidden;
            overscroll-behavior: none;
        }
    </style>

    <script>
        // The value below is injected by flutter build, do not touch.
        const serviceWorkerVersion = "1069182823";
    </script>
    <!-- This script adds the flutter initialization JS code -->
    <script src="flutter.js" defer></script>

    <!-- استخدام جافا سكريبت لتحسين الأداء -->
    <script>
        // إعادة توجيه للمستخدمين من متصفح سفاري على iOS لتحسين التوافق
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
            document.addEventListener("touchstart", function () { }, { passive: true });
        }

        // تسجيل Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function () {
                navigator.serviceWorker.register('/app/service_worker.js');
            });
        }
    </script>
</head>

<body>
    <div class="loader" id="loader">
        <img class="logo" src="icons/Icon-192.png" alt="أسرة المشعل" />
        <div class="progress">
            <div class="progress-bar"></div>
        </div>
        <div class="loading-text">جاري تحميل التطبيق...</div>
    </div>

    <script>
        window.addEventListener('load', function (ev) {
            var loading = document.querySelector('#loader');

            // تعيين وقت بدء التحميل لقياس الأداء
            var loadingStartTime = performance.now();

            // Download main.dart.js
            _flutter.loader.loadEntrypoint({
                serviceWorker: {
                    serviceWorkerVersion: serviceWorkerVersion,
                },
                onEntrypointLoaded: function (engineInitializer) {
                    // تكوين محرك العرض المناسب
                    let config = {
                        renderer: "canvaskit",
                        canvasKitBaseUrl: "/app/canvaskit/"
                    };

                    engineInitializer.initializeEngine(config).then(function (appRunner) {
                        // حساب وقت التحميل
                        var loadingTime = performance.now() - loadingStartTime;
                        console.log('تم تحميل التطبيق في ' + loadingTime.toFixed(2) + ' مللي ثانية');

                        // Loading done, hide loader
                        if (loading) {
                            loading.style.opacity = '0';
                            loading.style.transition = 'opacity 0.5s ease';
                            setTimeout(function () {
                                loading.style.display = 'none';
                            }, 500);
                        }
                        return appRunner.runApp();
                    });
                }
            });
        });
    </script>
</body>

</html>