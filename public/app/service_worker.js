// إصدار cache - يجب تغييره عند كل تعديل على التطبيق
const CACHE_NAME = "almashal-family-app-v1";

// الملفات التي سيتم تخزينها مؤقتًا
const STATIC_ASSETS = [
  "/",
  "/index.html",
  "/main.dart.js",
  "/flutter.js",
  "/manifest.json",
  "/assets/fonts/MaterialIcons-Regular.otf",
  "/assets/AssetManifest.json",
  "/assets/FontManifest.json",
  "/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf",
  "/icons/Icon-192.png",
  "/icons/Icon-512.png",
  "/icons/Icon-maskable-192.png",
  "/icons/Icon-maskable-512.png",
];

// تثبيت Service Worker
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log("تهيئة الذاكرة المؤقتة");
      return cache.addAll(STATIC_ASSETS);
    })
  );
  self.skipWaiting();
});

// تفعيل Service Worker
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((cacheName) => {
            return cacheName !== CACHE_NAME;
          })
          .map((cacheName) => {
            console.log("حذف الذاكرة المؤقتة القديمة: " + cacheName);
            return caches.delete(cacheName);
          })
      );
    })
  );
  self.clients.claim();
});

// استراتيجية الأنترنت أولًا ثم الذاكرة المؤقتة
self.addEventListener("fetch", (event) => {
  // تجاهل طلبات POST أو طلبات غير HTTPS
  if (
    event.request.method !== "GET" ||
    !event.request.url.startsWith("https")
  ) {
    return;
  }

  event.respondWith(
    fetch(event.request)
      .then((response) => {
        // ضمان أن الاستجابة صحيحة
        if (!response || response.status !== 200 || response.type !== "basic") {
          return response;
        }

        // تخزين نسخة من الاستجابة في الذاكرة المؤقتة
        const responseToCache = response.clone();
        caches.open(CACHE_NAME).then(function (cache) {
          cache.put(event.request, responseToCache);
        });

        return response;
      })
      .catch(() => {
        // في حالة فشل الشبكة، حاول استرداد من الذاكرة المؤقتة
        return caches.match(event.request);
      })
  );
});

// استقبال الإشعارات في الخلفية
self.addEventListener("push", (event) => {
  const data = event.data.json();
  const options = {
    body: data.body,
    icon: "/icons/Icon-192.png",
    badge: "/icons/Icon-192.png",
    data: {
      url: data.url,
    },
  };

  event.waitUntil(self.registration.showNotification(data.title, options));
});

// معالجة النقر على الإشعارات
self.addEventListener("notificationclick", (event) => {
  event.notification.close();

  event.waitUntil(clients.openWindow(event.notification.data.url || "/"));
});
