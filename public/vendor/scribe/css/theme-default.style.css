/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

html {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2em;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
    display: block
}

summary {
    cursor: pointer;
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline
}

audio:not([controls]) {
    display: none;
    height: 0
}

[hidden],
template {
    display: none
}

a {
    background-color: transparent
}

a:active,
a:hover {
    outline: 0
}

abbr[title] {
    border-bottom: 1px dotted
}

b,
strong {
    font-weight: 700
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

mark {
    background: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    box-sizing: content-box;
    height: 0
}

pre {
    overflow: auto
}

code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0
}

button {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],
html input[disabled] {
    cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0
}

input {
    line-height: normal
}

input[type=checkbox],
input[type=radio] {
    box-sizing: border-box;
    padding: 0
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    height: auto
}

input[type=search] {
    -webkit-appearance: textfield;
    box-sizing: content-box
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: 700
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

td,
th {
    padding: 0
}

body,
html {
    font-family: 'Open Sans', Helvetica Neue, Helvetica, Arial, Microsoft Yahei, 微软雅黑, STXihei, 华文细黑, sans-serif;
    font-size: 16px;
}

.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
    font-family: 'Open Sans', Helvetica Neue, Helvetica, Arial, Microsoft Yahei, 微软雅黑, STXihei, 华文细黑, sans-serif;
}

.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
    font-weight: 700
}

.content code,
.content pre {
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    line-height: 1.5
}

.content code {
    word-break: break-all;
    word-break: break-word;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    hyphens: auto
}

.content aside.notice:before,
.content aside.success:before,
.content aside.warning:before,
.tocify-wrapper>.search:before {
    font-family: 'Open Sans', sans-serif;
    speak: none;
    font-style: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1
}

.content aside.warning:before {
    content: "✋"
}

.content aside.notice:before {
    content: "ℹ"
}

.content aside.success:before {
    content: "✅"
}

.tocify-wrapper>.search:before {
    content: "🔎"
}

.highlight .c,
.highlight .c1,
.highlight .cm,
.highlight .cs {
    color: #909090
}

.highlight,
.highlight .w {
    background-color: #292929
}

.hljs {
    display: block;
    overflow-x: auto;
    padding: .5em;
    background: #23241f
}

.hljs,
.hljs-subst,
.hljs-tag {
    color: #f8f8f2
}

.hljs-emphasis,
.hljs-strong {
    color: #a8a8a2
}

.hljs-bullet,
.hljs-link,
.hljs-literal,
.hljs-number,
.hljs-quote,
.hljs-regexp {
    color: #ae81ff
}

.hljs-code,
.hljs-section,
.hljs-selector-class,
.hljs-title {
    color: #a6e22e
}

.hljs-strong {
    font-weight: 700
}

.hljs-emphasis {
    font-style: italic
}

.hljs-attr,
.hljs-keyword,
.hljs-name,
.hljs-selector-tag {
    color: #f92672
}

.hljs-attribute,
.hljs-symbol {
    color: #66d9ef
}

.hljs-class .hljs-title,
.hljs-params {
    color: #f8f8f2
}

.hljs-addition,
.hljs-built_in,
.hljs-builtin-name,
.hljs-selector-attr,
.hljs-selector-id,
.hljs-selector-pseudo,
.hljs-string,
.hljs-template-variable,
.hljs-type,
.hljs-variable {
    color: #e6db74
}

.hljs-comment,
.hljs-deletion,
.hljs-meta {
    color: #75715e
}

body,
html {
    color: #333;
    padding: 0;
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: whitesmoke;
    height: 100%;
    -webkit-text-size-adjust: none
}

#toc>ul>li>a>span {
    float: right;
    background-color: #2484ff;
    border-radius: 40px;
    width: 20px
}

.tocify-wrapper {
    transition: left .3s ease-in-out;
    overflow-y: auto;
    overflow-x: hidden;
    position: fixed;
    z-index: 30;
    top: 0;
    left: 0;
    bottom: 0;
    width: 230px;
    background-color: #393939;
    font-size: 13px;
    font-weight: 700
}

.tocify-wrapper .lang-selector {
    display: none
}

.tocify-wrapper .lang-selector a {
    padding-top: .5em;
    padding-bottom: .5em
}

.tocify-wrapper>img {
    display: block
}

.tocify-wrapper>.search {
    position: relative
}

.tocify-wrapper>.search input {
    background: #393939;
    border-width: 0 0 1px;
    border-color: #666;
    padding: 6px 0 6px 20px;
    box-sizing: border-box;
    margin: 10px 15px;
    width: 200px;
    outline: none;
    color: #fff;
    border-radius: 0
}

.tocify-wrapper>.search:before {
    position: absolute;
    top: 17px;
    left: 15px;
    color: #fff
}

.tocify-wrapper img+.tocify {
    margin-top: 20px
}

.tocify-wrapper .search-results {
    margin-top: 0;
    box-sizing: border-box;
    height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    transition-property: height, margin;
    transition-duration: .18s;
    transition-timing-function: ease-in-out;
    background: linear-gradient(180deg, rgba(0, 0, 0, .2), transparent 8px), linear-gradient(0deg, rgba(0, 0, 0, .2), transparent 8px), linear-gradient(180deg, #000, transparent 1.5px), linear-gradient(0deg, #939393, hsla(0, 0%, 58%, 0) 1.5px), #262626
}

.tocify-wrapper .search-results.visible {
    height: 30%;
    margin-bottom: 1em
}

.tocify-wrapper .search-results li {
    margin: 1em 15px;
    line-height: 1
}

.tocify-wrapper a {
    color: #fff;
    text-decoration: none
}

.tocify-wrapper .search-results a:hover {
    text-decoration: underline
}

.tocify-wrapper .toc-footer li,
.tocify-wrapper .tocify-item>a {
    padding: 0 15px;
    display: block;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}
.tocify-wrapper .tocify-item.level-3>a {
    padding: 0 25px;
}

.tocify-wrapper li,
.tocify-wrapper ul {
    list-style: none;
    margin: 0;
    padding: 0;
    line-height: 28px
}

.tocify-wrapper li {
    color: #fff;
    transition-property: background;
    transition-timing-function: linear;
    transition-duration: .23s
}

.tocify-wrapper .tocify-focus {
    box-shadow: 0 1px 0 #000;
    background-color: #2467af;
    color: #fff;
    font-weight: bold;
}

.tocify-wrapper .tocify-subheader {
    display: none;
    background-color: #262626;
    font-weight: 500;
    background: linear-gradient(180deg, rgba(0, 0, 0, .2), transparent 8px), linear-gradient(0deg, rgba(0, 0, 0, .2), transparent 8px), linear-gradient(180deg, #000, transparent 1.5px), linear-gradient(0deg, #939393, hsla(0, 0%, 58%, 0) 1.5px), #262626
}

.tocify-wrapper .jets-searching .tocify-subheader,
.tocify-wrapper .tocify-subheader.visible {
    display: block;
}

.tocify-wrapper .tocify-subheader .tocify-item>a {
    padding-left: 25px;
    font-size: 12px
}

.tocify-wrapper .tocify-subheader .tocify-item.level-3>a {
    padding-left: 35px;
}

.tocify-wrapper .tocify-subheader>li:last-child {
    box-shadow: none
}

.tocify-wrapper .toc-footer {
    padding: 1em 0;
    margin-top: 1em;
    border-top: 1px dashed #666
}

.tocify-wrapper .toc-footer a,
.tocify-wrapper .toc-footer li {
    color: #fff;
    text-decoration: none
}

.tocify-wrapper .toc-footer a:hover {
    text-decoration: underline
}

.tocify-wrapper .toc-footer li {
    font-size: .8em;
    line-height: 1.7;
    text-decoration: none
}

#nav-button {
    padding: 0 1.5em 5em 0;
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    color: #000;
    text-decoration: none;
    font-weight: 700;
    opacity: .7;
    line-height: 16px;
    transition: left .3s ease-in-out
}

#nav-button span {
    display: block;
    padding: 6px;
    background-color: rgba(234, 242, 246, .7);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: rotate(-90deg) translate(-100%);
    transform: rotate(-90deg) translate(-100%);
    border-radius: 0 0 0 5px
}

#nav-button img {
    height: 16px;
    vertical-align: bottom
}

#nav-button:hover {
    opacity: 1
}

#nav-button.open {
    left: 230px
}

.page-wrapper {
    margin-left: 230px;
    position: relative;
    z-index: 10;
    background-color: #eaf2f6;
    min-height: 100%;
    padding-bottom: 1px
}

.page-wrapper .dark-box {
    width: 50%;
    background-color: #393939;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0
}

.page-wrapper .lang-selector {
    position: fixed;
    z-index: 50;
    border-bottom: 5px solid #393939
}

.lang-selector {
    background-color: #222;
    width: 100%;
    font-weight: 700
}

.lang-selector button {
    display: block;
    float: left;
    color: #fff;
    text-decoration: none;
    padding: 0 10px;
    line-height: 30px;
    outline: 0;
    background: transparent;
    border: none;
}

.lang-selector button:active,
.lang-selector button:hover,
.lang-selector button:focus {
    background-color: #111;
    color: #fff
}

.lang-selector button.active {
    background-color: #393939;
    color: #fff
}

.lang-selector:after {
    content: '';
    clear: both;
    display: block
}

.content {
    position: relative;
    z-index: 30
}

.content:after {
    content: '';
    display: block;
    clear: both
}

.content>aside,
.content>details,
.content>dl,
.content>h1,
.content>h2,
.content>h3,
.content>h4,
.content>h5,
.content>h6,
.content>ol,
.content>p,
.content>table,
.content>ul,
.content>form>aside,
.content>form>details,
.content>form>h1,
.content>form>h2,
.content>form>h3,
.content>form>h4,
.content>form>h5,
.content>form>h6,
.content>form>p,
.content>form>table,
.content>form>ul,
.content>form>div {
    margin-right: 50%;
    padding: 0 28px;
    box-sizing: border-box;
    display: block;
    text-shadow: 0 1px 0 #fff
}

.content>ol,
.content>ul {
    padding-left: 43px
}

.content>div,
.content>h1,
.content>h2 {
    clear: both
}

.content h1 {
    font-size: 30px;
    padding-top: .5em;
    padding-bottom: .5em;
    border-bottom: 1px solid #ccc;
    margin-bottom: 21px;
    margin-top: 2em;
    border-top: 1px solid #ddd;
    background-image: linear-gradient(180deg, #fff, #f9f9f9)
}

.content div:first-child+h1,
.content h1:first-child {
    border-top-width: 0;
    margin-top: 0
}

.content h2 {
    font-size: 20px;
    margin-top: 4em;
    margin-bottom: 0;
    border-top: 1px solid #ccc;
    padding-top: 1.2em;
    padding-bottom: 1.2em;
    background-image: linear-gradient(180deg, hsla(0, 0%, 100%, .4), hsla(0, 0%, 100%, 0))
}

.content h1+div+h2,
.content h1+h2 {
    margin-top: -21px;
    border-top: none
}

.content h3,
.content h4,
.content h5,
.content h6 {
    font-size: 15px;
    margin-top: 2.5em;
    margin-bottom: .8em
}

.content h4,
.content h5,
.content h6 {
    font-size: 10px
}

.content hr {
    margin: 2em 0;
    border-top: 2px solid #393939;
    border-bottom: 2px solid #eaf2f6
}

.content table {
    margin-bottom: 1em;
    overflow: auto
}

.content table td,
.content table th {
    text-align: left;
    vertical-align: top;
    line-height: 1.6
}

.content table th {
    padding: 5px 10px;
    border-bottom: 1px solid #ccc;
    vertical-align: bottom
}

.content table td {
    padding: 10px
}

.content table tr:last-child {
    border-bottom: 1px solid #ccc
}

.content table tr:nth-child(odd)>td {
    background-color: #ebf3f6
}

.content table tr:nth-child(even)>td {
    background-color: #ebf2f6
}

.content dt {
    font-weight: 700
}

.content dd {
    margin-left: 15px
}

.content dd,
.content dt,
.content li,
.content p {
    line-height: 1.6;
    margin-top: 0
}

.content img {
    max-width: 100%
}

.content code {
    padding: 3px;
    border-radius: 3px
}

.content pre>code {
    background-color: transparent;
    padding: 0
}

.content aside {
    padding-top: 1em;
    padding-bottom: 1em;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    background: #292929;
    line-height: 1.6;
    color: #c8c8c8;
    text-shadow: none;
}

.content aside.info {
    background: #8fbcd4;
    text-shadow: 0 1px 0 #a0c6da;
    color: initial;
}

.content aside.warning {
    background-color: #c97a7e;
    text-shadow: 0 1px 0 #d18e91;
    color: initial;
}

.content aside.success {
    background-color: #6ac174;
    text-shadow: 0 1px 0 #80ca89;
    color: initial;
}

.content aside:before {
    vertical-align: middle;
    padding-right: .5em;
    font-size: 14px
}

.content .search-highlight {
    padding: 2px;
    margin: -2px;
    border-radius: 4px;
    border: 1px solid #f7e633;
    text-shadow: 1px 1px 0 #666;
    background: linear-gradient(to top left, #f7e633, #f1d32f)
}

.content blockquote,
.content pre {
    background-color: #292929;
    color: #fff;
    padding: 1.5em 28px;
    margin: 0;
    width: 50%;
    float: right;
    clear: right;
    box-sizing: border-box;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .4)
}

.content .annotation {
    background-color: #292929;
    color: #fff;
    padding: 0 28px;
    margin: 0;
    width: 50%;
    float: right;
    clear: right;
    box-sizing: border-box;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .4)
}

.content .annotation pre {
    padding: 0 0;
    width: 100%;
    float: none;
}

.content blockquote>p,
.content pre>p {
    margin: 0
}

.content blockquote a,
.content pre a {
    color: #fff;
    text-decoration: none;
    border-bottom: 1px dashed #ccc
}

.content blockquote>p {
    background-color: #1c1c1c;
    border-radius: 5px;
    padding: 13px;
    color: #ccc;
    border-top: 1px solid #000;
    border-bottom: 1px solid #404040
}

@media (max-width:930px) {
    .tocify-wrapper {
        left: -230px
    }
    .tocify-wrapper.open {
        left: 0
    }
    .page-wrapper {
        margin-left: 0
    }
    #nav-button {
        display: block
    }
    .tocify-wrapper .tocify-item>a {
        padding-top: .3em;
        padding-bottom: .3em
    }
}

@media (max-width:700px) {
    .dark-box {
        display: none
    }
    .tocify-wrapper .lang-selector {
        display: block
    }
    .page-wrapper .lang-selector {
        display: none
    }
    .content aside,
    .content dl,
    .content h1,
    .content h2,
    .content h3,
    .content h4,
    .content h5,
    .content h6,
    .content ol,
    .content p,
    .content table,
    .content ul {
        margin-right: 0
    }
    .content>aside,
    .content>details,
    .content>dl,
    .content>h1,
    .content>h2,
    .content>h3,
    .content>h4,
    .content>h5,
    .content>h6,
    .content>ol,
    .content>p,
    .content>table,
    .content>ul,
    .content>form>aside,
    .content>form>details,
    .content>form>h1,
    .content>form>h2,
    .content>form>h3,
    .content>form>h4,
    .content>form>h5,
    .content>form>h6,
    .content>form>p,
    .content>form>table,
    .content>form>ul,
    .content>form>div {
        margin-right: 0;
    }
    .content blockquote,
    .content pre {
        float: none;
        width: auto
    }
    .content .annotation {
        float: none;
        width: auto
    }
}

.badge {
    padding: 1px 9px 2px;
    white-space: nowrap;
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    border-radius: 9px;
    color: #ffffff;
    text-shadow: none !important;
    font-weight: bold;
}

.badge.badge-darkred {
    background-color: darkred;
}

.badge.badge-red {
    background-color: red;
}

.badge.badge-blue {
    background-color: blue;
}

.badge.badge-darkblue {
    background-color: darkblue;
}

.badge.badge-green {
    background-color: green;
}

.badge.badge-darkgreen {
    background-color: darkgreen;
}

.badge.badge-purple {
    background-color: purple;
}

.badge.badge-black {
    background-color: black;
}

.badge.badge-grey {
    background-color: grey;
}

.fancy-heading-panel {
    background-color: lightgrey;
    border-radius: 5px;
    padding-left: 5px !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    margin-left: 25px;
    margin-right: 10px;
    width: 47%;
}

@media screen and (max-width: 700px) {
    .fancy-heading-panel {
        width: 95%;
    }

}

button {
    border: none;
}

* {
    /* Foreground, Background */
    scrollbar-color: #3c4c67 transparent;
}
*::-webkit-scrollbar { /* Background */
    width: 10px;
    height: 10px;
    background: transparent;
}

*::-webkit-scrollbar-thumb { /* Foreground */
    background: #626161;
}
