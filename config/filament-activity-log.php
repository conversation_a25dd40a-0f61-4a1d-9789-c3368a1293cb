<?php

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return [
    'activity_resource' => \Alareqi\FilamentActivityLog\Resources\ActivityResource::class,
    'navigation_group' => 'common.navigation_groups.logs',
    'resources' => [
        'enabled' => true,
        'log_name' => 'filament-activity-log::filament-activity-log.types.resource',
        'logger' => \Alareqi\FilamentActivityLog\Loggers\ResourceLogger::class,
        'color' => 'success',
        'exclude' => [
            App\Filament\Resources\SuggestionResource::class,
            App\Filament\Resources\DiscussionResource::class,
            App\Filament\Resources\ComplaintResource::class,
        ],
    ],

    'access' => [
        'enabled' => true,
        'logger' => \Alareqi\FilamentActivityLog\Loggers\AccessLogger::class,
        'color' => 'danger',
        'log_name' => 'filament-activity-log::filament-activity-log.types.access',
    ],

    'notifications' => [
        'enabled' => false,
        'logger' => \Alareqi\FilamentActivityLog\Loggers\NotificationLogger::class,
        'color' => null,
        'log_name' => 'filament-activity-log::filament-activity-log.types.notification',
    ],

    'models' => [
        'enabled' => true,
        'log_name' => 'filament-activity-log::filament-activity-log.types.model',
        'color' => 'warning',
        'logger' => \Alareqi\FilamentActivityLog\Loggers\ModelLogger::class,
        'register' => [
            // Role::class,
            // Permission::class,
        ],
    ],

    'custom' => [
        // [
        //     'log_name' => 'Custom',
        //     'color' => 'primary',
        // ]
    ],
];
