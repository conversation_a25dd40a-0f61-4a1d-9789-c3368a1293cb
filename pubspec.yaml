name: almashal
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
# ios last 1.0.9+1
version: 1.0.10+1
# android  last 1.0.2+9
# version: 1.0.3+10

environment:
  sdk: '>=3.4.1 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  retrofit: '>=4.0.0 <5.0.0'
  cached_network_image: ^3.4.1
  cupertino_icons: ^1.0.2
  feather_icons: ^1.2.0
  flutter_svg: ^2.0.4
  get: ^4.6.5
  google_fonts: ^6.2.1
  photo_view: ^0.15.0
  youtube_player_flutter: ^9.0.0
  dio: ^5.1.0
  flutter_spinkit: ^5.1.0
  connectivity_plus: ^6.1.3
  flutter_secure_storage: ^9.0.0
  datetime_picker_formfield_new: ^2.1.0
  share_plus: ^10.0.0
  url_launcher: ^6.1.10
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  firebase_crashlytics: ^4.2.0
  firebase_analytics: ^11.3.6
  client_information: ^2.2.0
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  flutter_cached_pdfview: ^0.4.2
  flutter_widget_from_html: ^0.16.0
  app_links: ^6.1.1
  flutter_cache_manager: ^3.3.2
  fading_edge_scrollview: ^4.1.1
  get_storage: ^2.1.1
  graphview: ^1.2.0
  file_saver: ^0.2.14
  intl: ^0.20.2
  path_provider: ^2.1.5
  flutter_animate: ^4.5.2
  flutter_staggered_animations: ^1.1.1
  glassmorphism: ^3.0.0
  flutter_blurhash: ^0.8.2
  shimmer: ^3.0.0
  lottie: ^3.3.1
  file_picker: ^10.0.0
  open_file: ^3.5.10
  universal_html: ^2.2.4
  url_strategy: ^0.3.0
  phone_form_field: ^10.0.5
  font_awesome_flutter: ^10.8.0
  pretty_dio_logger: ^1.4.0
  confetti: ^0.8.0
  # share plus
  # url launcher

dev_dependencies:

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.2
  retrofit_generator: ^9.1.5
  build_runner: '>=2.3.0 <4.0.0'
flutter_launcher_icons:
  android: true
  ios: true
  web:
    generate: true
    image_path: "assets/launcher/launcher.png"
    background_color: "#B6977A"
    theme_color: "#B6977A"
  image_path_android: "assets/launcher/launcher.png"
  image_path_ios: "assets/launcher/launcher.png"
  adaptive_icon_foreground: "assets/launcher/foreground.png"
  adaptive_icon_background: "#B6977A"
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/vectors/
    - assets/lottie/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
