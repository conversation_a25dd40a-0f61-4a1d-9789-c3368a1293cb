<?php

namespace Tests\Feature;

use App\Models\FamilyMember;
use App\Models\Review;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ReviewApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $reviewer;
    protected $reviewedUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->reviewer = FamilyMember::factory()->create([
            'first_name' => 'John',
            'middle_name' => 'Doe',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'status' => true,
        ]);

        $this->reviewedUser = FamilyMember::factory()->create([
            'first_name' => 'Jane',
            'middle_name' => 'Doe',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'status' => true,
        ]);
    }

    /**
     * Get headers with API key for testing
     */
    private function getApiHeaders(): array
    {
        return [
            'api-key' => 'NwaAi8q5SXQAu9P5X3bqSPGkakoI'
        ];
    }

    /** @test */
    public function authenticated_user_can_create_review()
    {
        Sanctum::actingAs($this->reviewer);

        $reviewData = [
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 5,
            'comment' => 'Great family member!',
        ];

        $response = $this->postJson('/api/v1/reviews', $reviewData, $this->getApiHeaders());

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'rating',
                    'comment',
                    'reviewer' => ['id', 'name', 'image', 'thumb_image'],
                    'reviewed_user' => ['id', 'name', 'image', 'thumb_image'],
                    'status',
                    'created_at',
                    'created_at_formatted',
                    'updated_at',
                ]
            ]);

        $this->assertDatabaseHas('reviews', [
            'reviewer_id' => $this->reviewer->id,
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 5,
            'comment' => 'Great family member!',
        ]);
    }

    /** @test */
    public function user_cannot_review_themselves()
    {
        Sanctum::actingAs($this->reviewer);

        $reviewData = [
            'reviewed_user_id' => $this->reviewer->id,
            'rating' => 5,
            'comment' => 'Self review',
        ];

        $response = $this->postJson('/api/v1/reviews', $reviewData, $this->getApiHeaders());

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['reviewed_user_id']);
    }

    /** @test */
    public function user_can_get_reviews_for_specific_user()
    {
        // Create some reviews
        Review::factory()->create([
            'reviewer_id' => $this->reviewer->id,
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 5,
            'comment' => 'Excellent!',
            'status' => true,
        ]);

        Sanctum::actingAs($this->reviewer);

        $response = $this->getJson("/api/v1/users/{$this->reviewedUser->id}/reviews", $this->getApiHeaders());

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'rating',
                        'comment',
                        'reviewer' => ['id', 'name', 'image', 'thumb_image'],
                        'reviewed_user' => ['id', 'name', 'image', 'thumb_image'],
                        'status',
                        'created_at',
                        'created_at_formatted',
                        'updated_at',
                    ]
                ],
                'meta' => ['current_page', 'last_page', 'per_page', 'total']
            ]);
    }

    /** @test */
    public function user_can_get_review_summary()
    {
        // Create some reviews with different ratings
        Review::factory()->create([
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 5,
            'status' => true,
        ]);

        Review::factory()->create([
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 4,
            'status' => true,
        ]);

        Sanctum::actingAs($this->reviewer);

        $response = $this->getJson("/api/v1/users/{$this->reviewedUser->id}/reviews/summary", $this->getApiHeaders());

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'user' => ['id', 'name', 'image', 'thumb_image'],
                    'reviews_count',
                    'average_rating',
                    'ratings_breakdown',
                ]
            ]);

        // Check that ratings breakdown has the expected structure
        $data = $response->json('data');
        $this->assertArrayHasKey('ratings_breakdown', $data);
        $breakdown = $data['ratings_breakdown'];

        // Check that breakdown has the expected properties
        $this->assertArrayHasKey('5', $breakdown);
        $this->assertArrayHasKey('4', $breakdown);
        $this->assertArrayHasKey('3', $breakdown);
        $this->assertArrayHasKey('2', $breakdown);
        $this->assertArrayHasKey('1', $breakdown);
    }

    /** @test */
    public function reviewer_can_update_their_review()
    {
        $review = Review::factory()->create([
            'reviewer_id' => $this->reviewer->id,
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 4,
            'comment' => 'Good',
        ]);

        Sanctum::actingAs($this->reviewer);

        $updateData = [
            'rating' => 5,
            'comment' => 'Excellent!',
        ];

        $response = $this->putJson("/api/v1/reviews/{$review->id}", $updateData, $this->getApiHeaders());

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'id',
                    'rating',
                    'comment',
                    'reviewer',
                    'reviewed_user',
                    'status',
                    'created_at',
                    'created_at_formatted',
                    'updated_at',
                ]
            ]);

        $this->assertDatabaseHas('reviews', [
            'id' => $review->id,
            'rating' => 5,
            'comment' => 'Excellent!',
        ]);
    }

    /** @test */
    public function reviewer_can_delete_their_review()
    {
        $review = Review::factory()->create([
            'reviewer_id' => $this->reviewer->id,
            'reviewed_user_id' => $this->reviewedUser->id,
        ]);

        Sanctum::actingAs($this->reviewer);

        $response = $this->deleteJson("/api/v1/reviews/{$review->id}", [], $this->getApiHeaders());

        $response->assertStatus(200)
            ->assertJson(['message' => __('common.messages.deleted_successfully')]);

        $this->assertSoftDeleted('reviews', ['id' => $review->id]);
    }

    /** @test */
    public function user_cannot_update_others_review()
    {
        $otherUser = FamilyMember::factory()->create();

        $review = Review::factory()->create([
            'reviewer_id' => $otherUser->id,
            'reviewed_user_id' => $this->reviewedUser->id,
        ]);

        Sanctum::actingAs($this->reviewer);

        $response = $this->putJson("/api/v1/reviews/{$review->id}", [
            'rating' => 1,
            'comment' => 'Bad',
        ], $this->getApiHeaders());

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_review_endpoints()
    {
        $response = $this->postJson('/api/v1/reviews', [
            'reviewed_user_id' => $this->reviewedUser->id,
            'rating' => 5,
        ], $this->getApiHeaders());

        $response->assertStatus(401);
    }
}
